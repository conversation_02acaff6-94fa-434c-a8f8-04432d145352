#!/usr/bin/env python3

# Test the conversion process from USDT to EUR pairs
print("Testing USDT to EUR conversion order...")

# Simulate the trend_assets order (USDT pairs)
trend_assets = [
    'ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 
    'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 
    'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT'
]

# Simulate the trading_assets order (EUR pairs)
trading_assets = [
    'BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 
    'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 
    'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR'
]

# Simulate the latest_scores with tied BTC and TRX (in USDT order)
latest_scores = {
    'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 
    'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 
    'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 
    'BNB/USDT': 11.0, 'DOT/USDT': 1.0
}

print(f"Original USDT scores order: {list(latest_scores.keys())}")
print(f"BTC/USDT score: {latest_scores['BTC/USDT']}")
print(f"TRX/USDT score: {latest_scores['TRX/USDT']}")

# Create mapping from USDT to EUR (simulating the conversion logic)
target_quote = 'EUR'
usdt_to_trading_map = {}
for usdt_asset in trend_assets:  # This uses trend_assets order
    if '/USDT' in usdt_asset:
        base_asset = usdt_asset.replace('/USDT', '')
        trading_equivalent = f"{base_asset}/{target_quote}"
        if trading_equivalent in trading_assets:
            usdt_to_trading_map[usdt_asset] = trading_equivalent

print(f"Conversion mapping: {usdt_to_trading_map}")

# Convert the scores dictionary (simulating the conversion logic)
converted_scores = {}
for usdt_asset, score in latest_scores.items():  # This iterates in latest_scores order
    if usdt_asset in usdt_to_trading_map:
        trading_asset = usdt_to_trading_map[usdt_asset]
        converted_scores[trading_asset] = score
        print(f"Converting: {usdt_asset} -> {trading_asset} (score: {score})")
    else:
        converted_scores[usdt_asset] = score  # Keep as-is if no mapping

print(f"Final EUR scores order: {list(converted_scores.keys())}")
print(f"BTC/EUR score: {converted_scores['BTC/EUR']}")
print(f"TRX/EUR score: {converted_scores['TRX/EUR']}")

# Test tie-breaking
max_score = max(converted_scores.values())
tied_assets = [asset for asset, score in converted_scores.items() if score == max_score]
print(f"Max score: {max_score}")
print(f"Tied assets: {tied_assets}")
print(f"First tied asset (winner): {tied_assets[0]}")
