#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config_manager import load_config

def debug_usdt_order():
    """Debug the USDT asset order to understand the tie-breaking issue."""
    
    print("=" * 80)
    print("DEBUGGING USDT ASSET ORDER")
    print("=" * 80)
    
    # Load the configuration
    config_path = "config/settings_bitvavo_eur.yaml"
    print(f"Loading config from: {config_path}")
    
    try:
        config = load_config(config_path)
        settings = config.get('settings', {})
        
        print("\n1. CONFIGURATION ASSETS (EUR):")
        print("-" * 40)
        assets = settings.get('assets', [])
        for i, asset in enumerate(assets, 1):
            print(f"  {i:2d}. {asset}")
        
        print(f"\nBTC position in EUR config: {assets.index('BTC/EUR') + 1 if 'BTC/EUR' in assets else 'NOT FOUND'}")
        print(f"TRX position in EUR config: {assets.index('TRX/EUR') + 1 if 'TRX/EUR' in assets else 'NOT FOUND'}")
        
        print("\n2. TREND_ASSETS (USDT):")
        print("-" * 40)
        trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
        for i, asset in enumerate(trend_assets, 1):
            print(f"  {i:2d}. {asset}")
        
        print(f"\nBTC position in USDT trend_assets: {trend_assets.index('BTC/USDT') + 1 if 'BTC/USDT' in trend_assets else 'NOT FOUND'}")
        print(f"TRX position in USDT trend_assets: {trend_assets.index('TRX/USDT') + 1 if 'TRX/USDT' in trend_assets else 'NOT FOUND'}")
        
        print("\n3. COMPARISON:")
        print("-" * 40)
        if 'BTC/USDT' in trend_assets and 'TRX/USDT' in trend_assets:
            btc_pos = trend_assets.index('BTC/USDT')
            trx_pos = trend_assets.index('TRX/USDT')
            print(f"In USDT trend_assets: BTC is at position {btc_pos + 1}, TRX is at position {trx_pos + 1}")
            print(f"BTC comes before TRX in USDT: {btc_pos < trx_pos}")
        
        if 'BTC/EUR' in assets and 'TRX/EUR' in assets:
            btc_pos_eur = assets.index('BTC/EUR')
            trx_pos_eur = assets.index('TRX/EUR')
            print(f"In EUR assets: BTC is at position {btc_pos_eur + 1}, TRX is at position {trx_pos_eur + 1}")
            print(f"BTC comes before TRX in EUR: {btc_pos_eur < trx_pos_eur}")
        
        print("\n4. SIMULATING DATA_DICT ORDER:")
        print("-" * 40)
        print("If data_dict is created from trend_assets order:")
        data_dict_keys = list(trend_assets)  # This simulates list(data_dict.keys())
        print("data_dict.keys() order:")
        for i, key in enumerate(data_dict_keys, 1):
            print(f"  {i:2d}. {key}")
        
        print("\n5. SIMULATING DATAFRAME COLUMN ORDER:")
        print("-" * 40)
        print("DataFrame columns would be in this order:")
        for i, col in enumerate(data_dict_keys, 1):
            print(f"  {i:2d}. {col}")
        
        print("\n6. SIMULATING SCORES DICTIONARY ORDER:")
        print("-" * 40)
        print("latest_scores.to_dict() would have this key order:")
        for i, key in enumerate(data_dict_keys, 1):
            print(f"  {i:2d}. {key}")
        
        print("\n7. SIMULATING CONVERSION TO EUR:")
        print("-" * 40)
        print("Conversion loop: for usdt_asset, score in latest_scores.items():")
        
        # Simulate the conversion mapping
        usdt_to_eur_map = {}
        for usdt_asset in trend_assets:
            if '/USDT' in usdt_asset:
                base_asset = usdt_asset.replace('/USDT', '')
                eur_equivalent = f"{base_asset}/EUR"
                if eur_equivalent in assets:
                    usdt_to_eur_map[usdt_asset] = eur_equivalent
        
        print("Conversion mapping:")
        for usdt, eur in usdt_to_eur_map.items():
            print(f"  {usdt} -> {eur}")
        
        print("\nFinal EUR scores dictionary order (after conversion):")
        final_eur_order = []
        for i, usdt_asset in enumerate(data_dict_keys, 1):
            if usdt_asset in usdt_to_eur_map:
                eur_asset = usdt_to_eur_map[usdt_asset]
                final_eur_order.append(eur_asset)
                print(f"  {i:2d}. {eur_asset} (from {usdt_asset})")
        
        print("\n8. TIE-BREAKING ANALYSIS:")
        print("-" * 40)
        if 'BTC/EUR' in final_eur_order and 'TRX/EUR' in final_eur_order:
            btc_final_pos = final_eur_order.index('BTC/EUR')
            trx_final_pos = final_eur_order.index('TRX/EUR')
            print(f"In final EUR scores dict: BTC at position {btc_final_pos + 1}, TRX at position {trx_final_pos + 1}")
            print(f"BTC comes before TRX in final dict: {btc_final_pos < trx_final_pos}")
            
            if btc_final_pos < trx_final_pos:
                print("✅ BTC should be selected in tie-breaking")
            else:
                print("❌ TRX would be selected in tie-breaking")
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_usdt_order()
