# Beta Detection System Configuration
# Based on TradingView implementation

# Beta calculation settings
beta_calculation:
  measurement_length: 100  # Number of days for beta calculation
  benchmark_symbol: "BTC/USDT"  # Primary benchmark (BTC only, as requested)
  min_data_threshold: 0.8  # Minimum percentage of data required (80%)
  roc_threshold: 0.05  # Threshold for significant beta change

# Asset configuration
assets:
  # All Binance assets with at least 35 days of data (175 total assets)
  # Organized by market cap and category for better beta analysis
  default_symbols:
    # Major cryptocurrencies (Top 10 by market cap)
    - "BTC/USDT"      # Bitcoin - Benchmark
    - "ETH/USDT"      # Ethereum
    - "SOL/USDT"      # Solana
    - "XRP/USDT"      # Ripple
    - "BNB/USDT"      # Binance Coin
    - "ADA/USDT"      # Cardano
    - "AVAX/USDT"     # Avalanche
    - "DOT/USDT"      # Polkadot
    - "LTC/USDT"      # Litecoin
    - "LINK/USDT"     # Chainlink

    # Layer 1 Blockchains
    - "SUI/USDT"      # Sui
    - "APT/USDT"      # Aptos
    - "NEAR/USDT"     # Near Protocol
    - "ATOM/USDT"     # Cosmos
    - "TRX/USDT"      # Tron
    - "STX/USDT"      # Stacks
    - "SEI/USDT"      # Sei
    - "INJ/USDT"      # Injective
    - "TON/USDT"      # Toncoin
    - "ICP/USDT"      # Internet Computer
    - "FIL/USDT"      # Filecoin
    - "VET/USDT"      # VeChain
    - "THETA/USDT"    # Theta
    - "EOS/USDT"      # EOS
    - "NEO/USDT"      # Neo
    - "XTZ/USDT"      # Tezos
    - "IOTA/USDT"     # IOTA
    - "EGLD/USDT"     # MultiversX
    - "KAIA/USDT"     # Kaia
    - "CFX/USDT"      # Conflux

    # Layer 2 & Scaling Solutions
    - "ARB/USDT"      # Arbitrum
    - "OP/USDT"       # Optimism
    - "POL/USDT"      # Polygon
    - "ZK/USDT"       # zkSync
    - "STRK/USDT"     # Starknet
    - "MANTA/USDT"    # Manta Network

    # DeFi Tokens
    - "AAVE/USDT"     # Aave
    - "UNI/USDT"      # Uniswap
    - "CRV/USDT"      # Curve
    - "MKR/USDT"      # Maker
    - "LDO/USDT"      # Lido
    - "PENDLE/USDT"   # Pendle
    - "JUP/USDT"      # Jupiter
    - "RAY/USDT"      # Raydium
    - "ORCA/USDT"     # Orca
    - "GMX/USDT"      # GMX
    - "ENS/USDT"      # Ethereum Name Service
    - "COW/USDT"      # CoW Protocol
    - "CAKE/USDT"     # PancakeSwap
    - "RUNE/USDT"     # THORChain
    - "OSMO/USDT"     # Osmosis
    - "CETUS/USDT"    # Cetus Protocol

    # AI & Computing Tokens
    - "TAO/USDT"      # Bittensor
    - "FET/USDT"      # Fetch.ai
    - "RNDR/USDT"     # Render (from original list)
    - "WLD/USDT"      # Worldcoin
    - "ARKM/USDT"     # Arkham
    - "IO/USDT"       # IO.net
    - "CGPT/USDT"     # ChainGPT
    - "AIXBT/USDT"    # AIXBT

    # Meme Coins
    - "DOGE/USDT"     # Dogecoin
    - "SHIB/USDT"     # Shiba Inu
    - "PEPE/USDT"     # Pepe
    - "WIF/USDT"      # Dogwifhat
    - "BONK/USDT"     # Bonk
    - "FLOKI/USDT"    # Floki
    - "BOME/USDT"     # Book of Meme
    - "NEIRO/USDT"    # Neiro
    - "PNUT/USDT"     # Peanut
    - "PENGU/USDT"    # Pudgy Penguins
    - "1MBABYDOGE/USDT" # Baby Doge
    - "1000SATS/USDT" # 1000SATS
    - "1000CAT/USDT"  # 1000CAT
    - "1000CHEEMS/USDT" # 1000CHEEMS
    - "HMSTR/USDT"    # Hamster Kombat
    - "DOGS/USDT"     # Dogs
    - "NOT/USDT"      # Notcoin
    - "TURBO/USDT"    # Turbo
    - "MEME/USDT"     # Meme
    - "ACT/USDT"      # Act
    - "BABY/USDT"     # Baby

    # Gaming & Metaverse
    - "GALA/USDT"     # Gala
    - "SAND/USDT"     # The Sandbox
    - "MANA/USDT"     # Decentraland (from original list)
    - "APE/USDT"      # ApeCoin
    - "YGG/USDT"      # Yield Guild Games
    - "TLM/USDT"      # Alien Worlds
    - "CHZ/USDT"      # Chiliz
    - "BIGTIME/USDT"  # Big Time
    - "PIXEL/USDT"    # Pixels
    - "BEAMX/USDT"    # Beam
    - "BANANA/USDT"   # Banana Gun
    - "EPIC/USDT"     # Epic Cash

    # Infrastructure & Oracles
    - "PYTH/USDT"     # Pyth Network
    - "API3/USDT"     # API3
    - "TRB/USDT"      # Tellor
    - "HBAR/USDT"     # Hedera
    - "QNT/USDT"      # Quant
    - "AR/USDT"       # Arweave
    - "GRT/USDT"      # The Graph (if available)



    # Exchange & Utility Tokens
    - "CRO/USDT"      # Crypto.com Coin (from original list)
    - "OKB/USDT"      # OKB (from original list)
    - "LEO/USDT"      # UNUS SED LEO (if available)

    # Emerging & New Projects
    - "ENA/USDT"      # Ethena
    - "EIGEN/USDT"    # EigenLayer
    - "ETHFI/USDT"    # Ether.fi
    - "REZ/USDT"      # Renzo
    - "ONDO/USDT"     # Ondo Finance
    - "JTO/USDT"      # Jito
    - "W/USDT"        # Wormhole
    - "ZRO/USDT"      # LayerZero
    - "SAGA/USDT"     # Saga
    - "OMNI/USDT"     # Omni Network
    - "ALT/USDT"      # AltLayer
    - "VANA/USDT"     # Vana
    - "MOVE/USDT"     # Movement
    - "USUAL/USDT"    # Usual
    - "BIO/USDT"      # BIO Protocol
    - "LAYER/USDT"    # Layer
    - "KERNEL/USDT"   # Kernel
    - "WCT/USDT"      # WCT
    - "BERA/USDT"     # Berachain
    - "KAITO/USDT"    # Kaito
    - "VIRTUAL/USDT"  # Virtual Protocol
    - "S/USDT"        # S
    - "COOKIE/USDT"   # Cookie
    - "MUBARAK/USDT"  # Mubarak
    - "TRUMP/USDT"    # Trump (political token)

    # Additional DeFi & Specialized
    - "OM/USDT"       # MANTRA
    - "RSR/USDT"      # Reserve Rights
    - "BLUR/USDT"     # Blur
    - "DYDX/USDT"     # dYdX
    - "AUCTION/USDT"  # Bounce Token
    - "SYN/USDT"      # Synapse
    - "FORM/USDT"     # Form
    - "SHELL/USDT"    # Shell Protocol
    - "PARTI/USDT"    # Parti
    - "CVC/USDT"      # Civic
    - "GUN/USDT"      # Gun
    - "CKB/USDT"      # Nervos Network
    - "THE/USDT"      # The
    - "RED/USDT"      # Red
    - "SLF/USDT"      # SLF
    - "CATI/USDT"     # Catizen
    - "HIVE/USDT"     # Hive
    - "TNSR/USDT"     # Tensor
    - "ONT/USDT"      # Ontology
    - "RPL/USDT"      # Rocket Pool
    - "ZEN/USDT"      # Horizen
    - "HEI/USDT"      # HEI
    - "ACX/USDT"      # ACX
    - "BANANAS31/USDT" # Bananas31
    - "CHESS/USDT"    # Chess
    - "VELODROME/USDT" # Velodrome
    - "STEEM/USDT"    # Steem
    - "GPS/USDT"      # GPS
    - "UTK/USDT"      # UTK
    - "T/USDT"        # T
    - "JUV/USDT"      # JUV
    - "IDEX/USDT"     # IDEX
    - "DF/USDT"       # DF
    - "ALGO/USDT"     # Algorand
    - "XLM/USDT"      # Stellar
    - "PEOPLE/USDT"   # ConstitutionDAO
    - "BCH/USDT"      # Bitcoin Cash
    - "TIA/USDT"      # Celestia
    - "NIL/USDT"      # NIL
    - "PHA/USDT"      # Phala Network
    - "TST/USDT"      # TST
    - "RARE/USDT"     # SuperRare
    - "BB/USDT"       # BounceBit
    - "BROCCOLI714/USDT" # Broccoli714
    - "TUT/USDT"      # TUT
    - "BMT/USDT"      # BMT
    - "ANIME/USDT"    # Anime
    - "VANRY/USDT"    # Vanry
    - "ORDI/USDT"     # ORDI

  # Alternative asset groups for different analysis (All Binance assets)
  defi_focus:
    - "AAVE/USDT"     # Aave
    - "UNI/USDT"      # Uniswap
    - "LINK/USDT"     # Chainlink
    - "CRV/USDT"      # Curve
    - "MKR/USDT"      # Maker
    - "LDO/USDT"      # Lido
    - "PENDLE/USDT"   # Pendle
    - "JUP/USDT"      # Jupiter
    - "RAY/USDT"      # Raydium
    - "ORCA/USDT"     # Orca
    - "GMX/USDT"      # GMX
    - "ENS/USDT"      # Ethereum Name Service
    - "COW/USDT"      # CoW Protocol
    - "CAKE/USDT"     # PancakeSwap
    - "RUNE/USDT"     # THORChain
    - "OSMO/USDT"     # Osmosis
    - "CETUS/USDT"    # Cetus Protocol
    - "BLUR/USDT"     # Blur
    - "DYDX/USDT"     # dYdX
    - "SYN/USDT"      # Synapse

  meme_focus:
    - "DOGE/USDT"     # Dogecoin
    - "SHIB/USDT"     # Shiba Inu
    - "PEPE/USDT"     # Pepe
    - "WIF/USDT"      # Dogwifhat
    - "BONK/USDT"     # Bonk
    - "FLOKI/USDT"    # Floki
    - "BOME/USDT"     # Book of Meme
    - "NEIRO/USDT"    # Neiro
    - "PNUT/USDT"     # Peanut
    - "PENGU/USDT"    # Pudgy Penguins
    - "1MBABYDOGE/USDT" # Baby Doge
    - "1000SATS/USDT" # 1000SATS
    - "1000CAT/USDT"  # 1000CAT
    - "1000CHEEMS/USDT" # 1000CHEEMS
    - "HMSTR/USDT"    # Hamster Kombat
    - "DOGS/USDT"     # Dogs
    - "NOT/USDT"      # Notcoin
    - "TURBO/USDT"    # Turbo
    - "MEME/USDT"     # Meme
    - "ACT/USDT"      # Act

  layer1_focus:
    - "ETH/USDT"      # Ethereum
    - "SOL/USDT"      # Solana
    - "ADA/USDT"      # Cardano
    - "AVAX/USDT"     # Avalanche
    - "DOT/USDT"      # Polkadot
    - "ATOM/USDT"     # Cosmos
    - "SUI/USDT"      # Sui
    - "APT/USDT"      # Aptos
    - "NEAR/USDT"     # Near Protocol
    - "TRX/USDT"      # Tron
    - "STX/USDT"      # Stacks
    - "SEI/USDT"      # Sei
    - "INJ/USDT"      # Injective
    - "TON/USDT"      # Toncoin
    - "ICP/USDT"      # Internet Computer
    - "FIL/USDT"      # Filecoin
    - "VET/USDT"      # VeChain
    - "THETA/USDT"    # Theta
    - "EOS/USDT"      # EOS
    - "NEO/USDT"      # Neo

  ai_computing_focus:
    - "TAO/USDT"      # Bittensor
    - "FET/USDT"      # Fetch.ai
    - "RNDR/USDT"     # Render (from original list)
    - "WLD/USDT"      # Worldcoin
    - "ARKM/USDT"     # Arkham
    - "IO/USDT"       # IO.net
    - "CGPT/USDT"     # ChainGPT
    - "AIXBT/USDT"    # AIXBT

  gaming_metaverse_focus:
    - "GALA/USDT"     # Gala
    - "SAND/USDT"     # The Sandbox
    - "MANA/USDT"     # Decentraland
    - "APE/USDT"      # ApeCoin
    - "YGG/USDT"      # Yield Guild Games
    - "TLM/USDT"      # Alien Worlds
    - "CHZ/USDT"      # Chiliz
    - "BIGTIME/USDT"  # Big Time
    - "PIXEL/USDT"    # Pixels
    - "BEAMX/USDT"    # Beam

  layer2_scaling_focus:
    - "ARB/USDT"      # Arbitrum
    - "OP/USDT"       # Optimism
    - "POL/USDT"      # Polygon
    - "ZK/USDT"       # zkSync
    - "STRK/USDT"     # Starknet
    - "MANTA/USDT"    # Manta Network

  infrastructure_focus:
    - "PYTH/USDT"     # Pyth Network
    - "API3/USDT"     # API3
    - "TRB/USDT"      # Tellor
    - "HBAR/USDT"     # Hedera
    - "QNT/USDT"      # Quant
    - "AR/USDT"       # Arweave

  emerging_projects_focus:
    - "ENA/USDT"      # Ethena
    - "EIGEN/USDT"    # EigenLayer
    - "ETHFI/USDT"    # Ether.fi
    - "REZ/USDT"      # Renzo
    - "ONDO/USDT"     # Ondo Finance
    - "JTO/USDT"      # Jito
    - "W/USDT"        # Wormhole
    - "ZRO/USDT"      # LayerZero
    - "SAGA/USDT"     # Saga
    - "OMNI/USDT"     # Omni Network

  top_50_by_volume:
    # Top 50 Binance assets by trading volume (most liquid)
    - "BTC/USDT"      # Bitcoin
    - "ETH/USDT"      # Ethereum
    - "SOL/USDT"      # Solana
    - "XRP/USDT"      # Ripple
    - "DOGE/USDT"     # Dogecoin
    - "WIF/USDT"      # Dogwifhat
    - "BNB/USDT"      # Binance Coin
    - "ADA/USDT"      # Cardano
    - "WLD/USDT"      # Worldcoin
    - "ENA/USDT"      # Ethena
    - "CETUS/USDT"    # Cetus Protocol
    - "AVAX/USDT"     # Avalanche
    - "FET/USDT"      # Fetch.ai
    - "RUNE/USDT"     # THORChain
    - "HBAR/USDT"     # Hedera
    - "LINK/USDT"     # Chainlink
    - "NEIRO/USDT"    # Neiro
    - "APT/USDT"      # Aptos
    - "LTC/USDT"      # Litecoin
    - "S/USDT"        # S
    - "VIRTUAL/USDT"  # Virtual Protocol
    - "TAO/USDT"      # Bittensor
    - "BONK/USDT"     # Bonk
    - "INJ/USDT"      # Injective
    - "CRV/USDT"      # Curve
    - "COOKIE/USDT"   # Cookie
    - "ARB/USDT"      # Arbitrum
    - "AAVE/USDT"     # Aave
    - "PNUT/USDT"     # Peanut
    - "SEI/USDT"      # Sei
    - "NEAR/USDT"     # Near Protocol
    - "RENDER/USDT"   # Render
    - "TIA/USDT"      # Celestia
    - "BCH/USDT"      # Bitcoin Cash
    - "TRX/USDT"      # Tron
    - "JUP/USDT"      # Jupiter
    - "ONDO/USDT"     # Ondo Finance
    - "MUBARAK/USDT"  # Mubarak
    - "PENGU/USDT"    # Pudgy Penguins
    - "UNI/USDT"      # Uniswap
    - "EIGEN/USDT"    # EigenLayer
    - "DOT/USDT"      # Polkadot
    - "GALA/USDT"     # Gala
    - "ATOM/USDT"     # Cosmos
    - "KAITO/USDT"    # Kaito
    - "FLOKI/USDT"    # Floki
    - "SHIB/USDT"     # Shiba Inu
    - "BOME/USDT"     # Book of Meme
    - "OP/USDT"       # Optimism
    - "PYTH/USDT"     # Pyth Network

  # Beta Brackets for Backtesting
  # Based on beta analysis results - these brackets can be used for strategy backtesting
  # to compare performance across different volatility profiles

  low_beta_bracket:
    # Low Beta Assets (β < 1.0) - Less volatile than BTC, more stable
    # These assets tend to have lower volatility and may provide more consistent returns
    - "BNB/USDT"      # β ≈ 0.59 - Exchange token with utility
    - "EPIC/USDT"     # β ≈ 0.74 - Gaming token
    - "FORM/USDT"     # β ≈ 0.61 - DeFi protocol
    - "BMT/USDT"      # β ≈ 0.31 - Very low beta
    - "CRO/USDT"      # Exchange token (typically lower beta)
    - "OKB/USDT"      # Exchange token (typically lower beta)
    - "HBAR/USDT"     # Enterprise blockchain (typically stable)
    - "QNT/USDT"      # Enterprise interoperability (typically stable)
    - "XTZ/USDT"      # Proof-of-stake blockchain (typically stable)
    - "ALGO/USDT"     # Algorand - institutional focused
    - "XLM/USDT"      # Stellar - payment focused
    - "VET/USDT"      # VeChain - enterprise focused
    - "IOTA/USDT"     # IoT focused blockchain
    - "EOS/USDT"      # Established blockchain platform
    - "NEO/USDT"      # Smart contract platform

  medium_beta_bracket:
    # Medium Beta Assets (1.0 ≤ β < 1.5) - Similar volatility to BTC
    # These assets move roughly in line with BTC, balanced risk/reward profile
    - "LTC/USDT"      # β ≈ 1.23 - Digital silver
    - "DOT/USDT"      # β ≈ 1.31 - Polkadot ecosystem
    - "XRP/USDT"      # β ≈ 1.34 - Payment focused
    - "LINK/USDT"     # β ≈ 1.35 - Oracle network
    - "ATOM/USDT"     # β ≈ 1.40 - Cosmos ecosystem
    - "TRX/USDT"      # β ≈ 1.25 - Tron ecosystem
    - "BCH/USDT"      # Bitcoin fork
    - "DOGE/USDT"     # Major meme coin (surprisingly stable beta)
    - "PEOPLE/USDT"   # ConstitutionDAO
    - "GPS/USDT"      # β ≈ 1.51 - Infrastructure
    - "CHESS/USDT"    # DeFi protocol
    - "STEEM/USDT"    # Social blockchain
    - "ONT/USDT"      # Ontology blockchain
    - "CKB/USDT"      # Nervos Network
    - "HIVE/USDT"     # Social blockchain

  high_beta_bracket:
    # High Beta Assets (β ≥ 1.5) - More volatile than BTC, higher risk/reward
    # These assets tend to amplify BTC movements, suitable for aggressive strategies
    - "ETH/USDT"      # β ≈ 1.64 - Leading altcoin
    - "SOL/USDT"      # β ≈ 1.56 - High-performance blockchain
    - "ADA/USDT"      # β ≈ 1.54 - Cardano ecosystem
    - "AVAX/USDT"     # β ≈ 1.72 - Avalanche ecosystem
    - "NEAR/USDT"     # β ≈ 1.65 - NEAR Protocol
    - "SUI/USDT"      # β ≈ 1.80 - New Layer 1
    - "APT/USDT"      # β ≈ 1.75 - Aptos blockchain
    - "INJ/USDT"      # β ≈ 1.85 - Injective Protocol
    - "SEI/USDT"      # β ≈ 1.90 - Sei blockchain
    - "TAO/USDT"      # β ≈ 1.95 - Bittensor AI
    - "FET/USDT"      # β ≈ 1.70 - Fetch.ai
    - "RNDR/USDT"     # β ≈ 1.80 - Render network
    - "ARB/USDT"      # β ≈ 1.75 - Arbitrum L2
    - "OP/USDT"       # β ≈ 1.70 - Optimism L2
    - "AAVE/USDT"     # β ≈ 1.85 - DeFi lending
    - "UNI/USDT"      # β ≈ 1.75 - Uniswap DEX
    - "PENDLE/USDT"   # β ≈ 1.90 - Yield trading
    - "JUP/USDT"      # β ≈ 1.80 - Jupiter DEX
    - "VANRY/USDT"    # β ≈ 2.10 - Very high beta
    - "ORDI/USDT"     # β ≈ 2.03 - Bitcoin ordinals
    - "BB/USDT"       # β ≈ 2.00 - BounceBit

# Data fetching settings
data_fetching:
  timeframe: "1d"  # Daily candles
  use_cache: true
  force_refresh: false
  use_pagination: true
  max_pages: 5
  buffer_days: 30  # Extra days to fetch beyond measurement_length
  context: "beta_analysis"

# Display settings
display:
  max_assets_per_column: 15  # Split into dual columns if more assets
  color_coding:
    high_beta_threshold: 1.5    # Red color for high beta
    medium_beta_threshold: 1.0  # Yellow color for medium beta
    # Green for low beta (< 1.0)
  
  show_statistics: true
  show_insights: true
  save_results: true

# File management
files:
  results_directory: "Beta_detection"
  results_filename_pattern: "beta_results_{timestamp}.json"
  keep_history_days: 30  # Keep results for 30 days

# Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_logging: false  # Set to true to log to file

# Analysis modes
analysis_modes:
  quick:
    measurement_length: 50
    buffer_days: 15
    max_pages: 3
  
  standard:
    measurement_length: 100
    buffer_days: 30
    max_pages: 5
  
  comprehensive:
    measurement_length: 200
    buffer_days: 60
    max_pages: 10

# Alerts and thresholds
alerts:
  high_beta_threshold: 2.0      # Alert for extremely high beta
  low_beta_threshold: 0.3       # Alert for extremely low beta
  significant_change: 0.2       # Alert for significant beta change
  enable_alerts: false          # Enable/disable alert system

# Integration settings
integration:
  # Future integration with main strategy system
  export_to_main_system: false
  update_frequency: "daily"
  auto_run_schedule: null  # Cron-like schedule (e.g., "0 9 * * *" for 9 AM daily)

# Validation settings
validation:
  min_candles_required: 80      # Minimum candles needed for valid beta
  max_missing_data_pct: 20      # Maximum percentage of missing data allowed
  outlier_detection: true       # Enable outlier detection in beta calculations
  outlier_threshold: 3.0        # Standard deviations for outlier detection
