#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.scoring import find_best_asset_for_day

# Test the tie-breaking fix
print("Testing tie-breaking fix...")

# Test case 1: BTC should be selected over TRX when tied (BTC comes first in order)
scores_btc_first = {
    'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 
    'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 
    'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 
    'BNB/EUR': 11.0, 'DOT/EUR': 1.0
}

print(f"Test 1 - BTC first in order:")
print(f"Scores: {scores_btc_first}")
print(f"Dictionary order: {list(scores_btc_first.keys())}")
best_asset_1 = find_best_asset_for_day(scores_btc_first)
print(f"Selected asset: {best_asset_1}")
print(f"Expected: BTC/EUR, Got: {best_asset_1}, Correct: {best_asset_1 == 'BTC/EUR'}")
print()

# Test case 2: TRX should be selected over BTC when TRX comes first in order
scores_trx_first = {
    'ETH/EUR': 8.0, 'TRX/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 
    'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 
    'LINK/EUR': 6.0, 'BTC/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 
    'BNB/EUR': 11.0, 'DOT/EUR': 1.0
}

print(f"Test 2 - TRX first in order:")
print(f"Scores: {scores_trx_first}")
print(f"Dictionary order: {list(scores_trx_first.keys())}")
best_asset_2 = find_best_asset_for_day(scores_trx_first)
print(f"Selected asset: {best_asset_2}")
print(f"Expected: TRX/EUR, Got: {best_asset_2}, Correct: {best_asset_2 == 'TRX/EUR'}")
print()

# Test case 3: No tie - highest score should be selected
scores_no_tie = {
    'ETH/EUR': 8.0, 'BTC/EUR': 13.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 
    'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 
    'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 
    'BNB/EUR': 11.0, 'DOT/EUR': 1.0
}

print(f"Test 3 - No tie (BTC has highest score):")
print(f"Scores: {scores_no_tie}")
best_asset_3 = find_best_asset_for_day(scores_no_tie)
print(f"Selected asset: {best_asset_3}")
print(f"Expected: BTC/EUR, Got: {best_asset_3}, Correct: {best_asset_3 == 'BTC/EUR'}")
print()

print("All tests completed!")
