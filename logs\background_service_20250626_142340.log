2025-06-26 14:23:40,709 - root - INFO - Loaded environment variables from .env file
2025-06-26 14:23:41,507 - root - INFO - Loaded 33 trade records from logs/trades\trade_log_********.json
2025-06-26 14:23:41,509 - root - INFO - Loaded 21 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 14:23:41,509 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 14:23:42,440 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:42,452 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:42,458 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:42,460 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:42,460 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:42,475 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:42,475 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 14:23:42,476 - root - INFO - Notification configuration loaded successfully.
2025-06-26 14:23:43,441 - root - INFO - Telegram command handlers registered
2025-06-26 14:23:43,442 - root - INFO - Telegram bot polling started
2025-06-26 14:23:43,442 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 14:23:43,442 - root - INFO - Telegram notification channel initialized
2025-06-26 14:23:43,443 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 14:23:43,443 - root - INFO - Loaded 24 templates from file
2025-06-26 14:23:43,444 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 14:23:43,444 - root - INFO - Notification manager initialized
2025-06-26 14:23:43,445 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 14:23:43,445 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 14:23:43,445 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 14:23:43,445 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 14:23:43,447 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 14:23:43,448 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 14:23:43,448 - root - INFO - Recovery manager initialized
2025-06-26 14:23:43,452 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:43,462 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:43,463 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 14:23:43,464 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 14:23:43,464 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 14:23:43,465 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 14:23:43,465 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 14:23:43,465 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 14:23:43,465 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 14:23:43,465 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 14:23:43,465 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:43,472 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:43,502 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 14:23:43,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 14:23:43,517 - telegram.ext.Application - INFO - Application started
2025-06-26 14:23:43,774 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 14:23:43,784 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 14:23:43,784 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 14:23:43,784 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 14:23:43,784 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 14:23:43,784 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:43,791 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:43,791 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:43,791 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:43,791 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 14:23:43,800 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:43,800 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 14:23:43,807 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 14:23:43,807 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 14:23:43,807 - root - INFO - Trading mode: paper
2025-06-26 14:23:43,807 - root - INFO - Trading enabled: True
2025-06-26 14:23:43,807 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 14:23:43,807 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 14:23:43,808 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 14:23:43,808 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 14:23:43,808 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:43,810 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:44,112 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 14:23:44,112 - root - INFO - Trading enabled in paper mode
2025-06-26 14:23:44,112 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 14:23:44,112 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 14:23:44,112 - root - INFO - Reset paper trading account to initial balance
2025-06-26 14:23:44,112 - root - INFO - Generated run ID: ********_142344
2025-06-26 14:23:44,112 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 14:23:44,112 - root - INFO - Background service initialized
2025-06-26 14:23:44,112 - root - INFO - Network watchdog started
2025-06-26 14:23:44,112 - root - INFO - Network watchdog started
2025-06-26 14:23:44,123 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 14:23:44,125 - root - INFO - Background service started
2025-06-26 14:23:44,125 - root - INFO - Executing strategy (run #1)...
2025-06-26 14:23:44,125 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 14:23:44,126 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 14:23:44,127 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 14:23:44,127 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 14:23:44,194 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 14:23:50,147 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 14:23:50,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 14:23:50,244 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 14:23:50,245 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 14:23:50,245 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 14:23:50,247 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 14:23:50,272 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,273 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,273 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,273 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 14:23:50,274 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,286 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,286 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,286 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,287 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 14:23:50,288 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,301 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,302 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,302 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,303 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 14:23:50,303 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,312 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,313 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,314 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,314 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 14:23:50,315 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,328 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,328 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,329 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,329 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 14:23:50,330 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,340 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,340 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,341 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,341 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 14:23:50,342 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,354 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,355 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,355 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,355 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 14:23:50,356 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,371 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,372 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,372 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,372 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 14:23:50,373 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,386 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,387 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,387 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,387 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 14:23:50,388 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,402 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,402 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,403 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,403 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 14:23:50,403 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,410 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,410 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,411 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,411 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 14:23:50,412 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,426 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,426 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,427 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,427 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 14:23:50,428 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,445 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,445 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,447 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,447 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 14:23:50,448 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,461 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,461 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,462 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,462 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 14:23:50,464 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,466 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 14:23:50,466 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 14:23:50,466 - root - INFO -   - Number of indicators: 8
2025-06-26 14:23:50,467 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 14:23:50,467 - root - INFO -   - Combination method: consensus
2025-06-26 14:23:50,467 - root - INFO -   - Long threshold: 0.1
2025-06-26 14:23:50,467 - root - INFO -   - Short threshold: -0.1
2025-06-26 14:23:50,467 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 14:23:50,467 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 14:23:50,467 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 14:23:50,467 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 14:23:50,467 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 14:23:50,467 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 14:23:50,468 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 14:23:50,468 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:50,475 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:50,476 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:50,481 - root - INFO - Configuration saved successfully.
2025-06-26 14:23:50,481 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 14:23:50,481 - root - INFO - Number of trend detection assets: 14
2025-06-26 14:23:50,481 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 14:23:50,482 - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 14:23:50,482 - root - INFO - Number of trading assets: 14
2025-06-26 14:23:50,482 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 14:23:50,701 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 14:23:50,709 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:50,715 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 14:23:50,721 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:50,721 - root - INFO - Execution context: backtesting
2025-06-26 14:23:50,721 - root - INFO - Execution timing: candle_close
2025-06-26 14:23:50,722 - root - INFO - Ratio calculation method: manual_inversion
2025-06-26 14:23:50,722 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 14:23:50,722 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 14:23:50,722 - root - INFO - MTPI combination method override: consensus
2025-06-26 14:23:50,722 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 14:23:50,722 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 14:23:50,723 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 14:23:50,723 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 14:23:50,724 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,725 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,725 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,726 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,726 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,727 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,727 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,727 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,729 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,729 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,729 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,730 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,731 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,731 - root - INFO - Loaded metadata for 48 assets
2025-06-26 14:23:50,731 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 14:23:50,747 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,748 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,749 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,749 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 14:23:50,763 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,765 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,766 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,766 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 14:23:50,779 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,781 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,782 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,782 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 14:23:50,788 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,789 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,789 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,789 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 14:23:50,805 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,806 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,806 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,806 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 14:23:50,819 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,820 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,821 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,821 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 14:23:50,836 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,837 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,837 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,838 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 14:23:50,851 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,852 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,853 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,853 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 14:23:50,866 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,867 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,867 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,869 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 14:23:50,882 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,883 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,884 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,884 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 14:23:50,890 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,892 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,892 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,893 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 14:23:50,907 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,910 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,911 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,911 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 14:23:50,925 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,927 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,927 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,928 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 14:23:50,941 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,942 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 14:23:50,943 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,943 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 14:23:50,944 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 14:23:50,944 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,945 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,945 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,946 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,946 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,946 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,946 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,947 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,947 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,947 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,948 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,948 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,948 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,949 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 14:23:50,962 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 14:23:50,964 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 14:23:50,964 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 14:23:50,965 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 14:23:50,965 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 14:23:50,970 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:50,971 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 14:23:50,971 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 14:23:50,971 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 14:23:50,972 - root - INFO - Override: combination_method = consensus
2025-06-26 14:23:50,972 - root - INFO - Override: long_threshold = 0.1
2025-06-26 14:23:50,972 - root - INFO - Override: short_threshold = -0.1
2025-06-26 14:23:50,972 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 14:23:50,972 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 14:23:50,973 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 14:23:50,973 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 14:23:50,987 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 14:23:50,988 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 14:23:50,988 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 14:23:50,988 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 14:23:50,989 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:50,989 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 14:23:51,014 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 14:23:51,014 - root - INFO - Generated pgo signals: 256 values
2025-06-26 14:23:51,014 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 14:23:51,015 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 14:23:51,024 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 14:23:51,025 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 14:23:51,025 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 14:23:51,388 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 14:23:51,388 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 14:23:51,437 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 14:23:51,438 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 14:23:51,438 - root - INFO - Generated DEMA Super Score signals
2025-06-26 14:23:51,438 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 14:23:51,537 - root - INFO - Generated DPSD signals
2025-06-26 14:23:51,537 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 14:23:51,538 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 14:23:51,538 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 14:23:51,548 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 14:23:51,549 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 14:23:51,549 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 14:23:51,637 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 14:23:51,637 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 14:23:51,637 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 14:23:51,808 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 14:23:51,813 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 14:23:51,814 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 14:23:51,814 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 14:23:51,815 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 14:23:51,815 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 14:23:51,817 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 14:23:51,821 - root - INFO - Configuration saved successfully.
2025-06-26 14:23:51,822 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 14:23:51,822 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 14:23:51,834 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:51,834 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 14:23:51,834 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 14:23:51,834 - root - INFO - Using ratio calculation method: manual_inversion
2025-06-26 14:23:51,853 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:51,877 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:51,903 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:51,903 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:51,926 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:51,933 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:51,950 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 14:23:51,950 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:51,966 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 14:23:51,971 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:51,990 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 14:23:51,990 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,005 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 14:23:52,013 - root - INFO - Calculated ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,032 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 14:23:52,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,049 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 14:23:52,055 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,074 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 14:23:52,074 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,092 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 14:23:52,101 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,122 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 14:23:52,122 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,143 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 14:23:52,151 - root - INFO - Calculated ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,174 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 14:23:52,174 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,196 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 14:23:52,202 - root - INFO - Calculated ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,222 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 14:23:52,222 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,240 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 14:23:52,250 - root - INFO - Calculated ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,270 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 14:23:52,271 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,295 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 14:23:52,304 - root - INFO - Calculated ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,327 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:52,327 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,347 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:52,353 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,371 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,392 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,410 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 14:23:52,410 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,428 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 14:23:52,433 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,451 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,470 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,486 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 14:23:52,487 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,502 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 14:23:52,508 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,526 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 14:23:52,527 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,543 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 14:23:52,551 - root - INFO - Calculated ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,567 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 14:23:52,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,582 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 14:23:52,589 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,605 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 14:23:52,606 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,620 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 14:23:52,626 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,643 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:52,643 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,659 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:52,660 - root - INFO - Calculated ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,676 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 14:23:52,676 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,692 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 14:23:52,692 - root - INFO - Calculated ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,709 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:52,709 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,726 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:52,726 - root - INFO - Calculated ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,742 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 14:23:52,742 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,759 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 14:23:52,759 - root - INFO - Calculated ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,776 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,792 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,826 - root - INFO - Calculated ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,854 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 14:23:52,854 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,869 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 14:23:52,881 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,901 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:52,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,914 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:52,926 - root - INFO - Calculated ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,944 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:52,944 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:52,959 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:52,968 - root - INFO - Calculated ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:52,986 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 14:23:52,986 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,001 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 14:23:53,007 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,020 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 14:23:53,020 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,037 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 14:23:53,043 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,060 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 14:23:53,060 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,076 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 14:23:53,078 - root - INFO - Calculated ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,094 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 14:23:53,094 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,111 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 14:23:53,111 - root - INFO - Calculated ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,126 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 14:23:53,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,143 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 14:23:53,152 - root - INFO - Calculated ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,160 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 14:23:53,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,178 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 14:23:53,188 - root - INFO - Calculated ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,204 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:53,204 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,208 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:53,218 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,236 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:53,237 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,243 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:53,253 - root - INFO - Calculated ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,260 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 14:23:53,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,283 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 14:23:53,287 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,303 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,324 - root - INFO - Calculated ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,339 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 14:23:53,339 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,344 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 14:23:53,357 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,371 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,385 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,403 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,419 - root - INFO - Calculated ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,435 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,435 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,443 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,453 - root - INFO - Calculated ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,477 - root - INFO - Calculated ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,510 - root - INFO - Calculated ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,527 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:53,527 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,543 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:53,543 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,560 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,560 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:23:53,576 - root - INFO - Calculated ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,600 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,610 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,636 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,638 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,643 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,655 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,669 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 14:23:53,670 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,676 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 14:23:53,687 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,693 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 14:23:53,693 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,710 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 14:23:53,719 - root - INFO - Calculated ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,727 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,727 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,743 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,743 - root - INFO - Calculated ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,760 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 14:23:53,760 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,776 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 14:23:53,776 - root - INFO - Calculated ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,793 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,810 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 14:23:53,819 - root - INFO - Calculated ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,827 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,844 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,870 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,890 - root - INFO - Calculated ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,904 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 14:23:53,907 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,920 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 14:23:53,926 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,944 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:53,944 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:53,961 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:53,961 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:53,986 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:53,988 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,003 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:54,009 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,081 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 14:23:54,081 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,095 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 14:23:54,099 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,123 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,143 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,162 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,182 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,193 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 14:23:54,193 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,210 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 14:23:54,219 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,243 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,260 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:54,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,278 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:54,278 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,303 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,320 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,340 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 14:23:54,340 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,354 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 14:23:54,359 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,376 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,393 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,408 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,427 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,443 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,468 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,477 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,503 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,520 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,540 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,557 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 14:23:54,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,570 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 14:23:54,575 - root - INFO - Calculated ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,594 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,610 - root - INFO - Calculated ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,628 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 14:23:54,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,642 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 14:23:54,646 - root - INFO - Calculated ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,660 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:54,660 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,676 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 14:23:54,676 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,693 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,718 - root - INFO - Calculated ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,733 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:54,733 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,749 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:54,753 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,788 - root - INFO - Calculated ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,804 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:54,804 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,822 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 14:23:54,827 - root - INFO - Calculated ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,843 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,860 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,877 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,895 - root - INFO - Calculated ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,931 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,948 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:54,949 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:54,964 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 14:23:54,971 - root - INFO - Calculated ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:54,988 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,008 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,023 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,044 - root - INFO - Calculated ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,062 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,083 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,102 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,120 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,156 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,176 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,201 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,216 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,237 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,255 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 14:23:55,256 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,274 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 14:23:55,282 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:55,299 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 14:23:55,319 - root - INFO - Calculated ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 14:23:56,948 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 14:23:56,948 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 14:23:56,948 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 14:23:56,951 - root - INFO - Date ranges for each asset:
2025-06-26 14:23:56,951 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,951 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,951 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,951 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,952 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,952 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,952 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,952 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,952 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,952 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,953 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,953 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,953 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,953 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,953 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 14:23:56,953 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 14:23:56,958 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 14:23:56,959 - root - INFO -    Execution Method: candle_close
2025-06-26 14:23:56,959 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 14:23:56,960 - root - INFO -    Signal generated and executed immediately
2025-06-26 14:23:56,965 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,965 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,965 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,965 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,966 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,967 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,967 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,967 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,967 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 14:23:56,967 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 14:23:56,967 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 14:23:56,967 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,967 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 14:23:56,967 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 14:23:56,968 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,968 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,969 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,970 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,971 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,971 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,971 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 14:23:56,971 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 14:23:56,971 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 14:23:56,971 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,971 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 14:23:56,971 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 14:23:56,971 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 14:23:56,972 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,972 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,973 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,973 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,973 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,973 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,973 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,973 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,974 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,974 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,974 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,974 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,974 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,975 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,975 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,975 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,975 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,975 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,975 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,976 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,976 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,976 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 14:23:56,981 - root - INFO - ASSET CHANGE DETECTED on 2025-02-25:
2025-06-26 14:23:56,981 - root - INFO -    Signal Date: 2025-02-24 (generated at 00:00 UTC)
2025-06-26 14:23:56,981 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-25 00:00 UTC (immediate)
2025-06-26 14:23:56,981 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,981 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 14:23:56,981 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 14:23:56,982 - root - INFO -    TRX/USDT buy price: $0.2309 (close price)
2025-06-26 14:23:56,984 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 14:23:56,984 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 14:23:56,984 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 14:23:56,984 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,984 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 14:23:56,984 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 14:23:56,984 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 14:23:56,986 - root - INFO - ASSET CHANGE DETECTED on 2025-03-10:
2025-06-26 14:23:56,987 - root - INFO -    Signal Date: 2025-03-09 (generated at 00:00 UTC)
2025-06-26 14:23:56,987 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-10 00:00 UTC (immediate)
2025-06-26 14:23:56,987 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,987 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 14:23:56,987 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 14:23:56,988 - root - INFO -    TRX/USDT buy price: $0.2292 (close price)
2025-06-26 14:23:56,990 - root - INFO - ASSET CHANGE DETECTED on 2025-03-16:
2025-06-26 14:23:56,990 - root - INFO -    Signal Date: 2025-03-15 (generated at 00:00 UTC)
2025-06-26 14:23:56,990 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-16 00:00 UTC (immediate)
2025-06-26 14:23:56,990 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,990 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 14:23:56,990 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 14:23:56,990 - root - INFO -    ADA/USDT buy price: $0.7049 (close price)
2025-06-26 14:23:56,991 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 14:23:56,991 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 14:23:56,991 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 14:23:56,991 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,992 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 14:23:56,992 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 14:23:56,992 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 14:23:56,995 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 14:23:56,996 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 14:23:56,996 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 14:23:56,996 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,996 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 14:23:56,996 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 14:23:56,996 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 14:23:56,998 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 14:23:56,998 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 14:23:56,998 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 14:23:56,998 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:56,998 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 14:23:56,998 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 14:23:56,998 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 14:23:57,000 - root - INFO - ASSET CHANGE DETECTED on 2025-04-04:
2025-06-26 14:23:57,000 - root - INFO -    Signal Date: 2025-04-03 (generated at 00:00 UTC)
2025-06-26 14:23:57,000 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-04 00:00 UTC (immediate)
2025-06-26 14:23:57,000 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,000 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 14:23:57,000 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 14:23:57,000 - root - INFO -    TRX/USDT buy price: $0.2390 (close price)
2025-06-26 14:23:57,005 - root - INFO - ASSET CHANGE DETECTED on 2025-04-20:
2025-06-26 14:23:57,005 - root - INFO -    Signal Date: 2025-04-19 (generated at 00:00 UTC)
2025-06-26 14:23:57,005 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-20 00:00 UTC (immediate)
2025-06-26 14:23:57,005 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,005 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 14:23:57,005 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 14:23:57,005 - root - INFO -    SOL/USDT buy price: $137.8600 (close price)
2025-06-26 14:23:57,006 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-26 14:23:57,007 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-26 14:23:57,007 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-26 14:23:57,007 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,007 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 14:23:57,007 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 14:23:57,007 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 14:23:57,008 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 14:23:57,008 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 14:23:57,008 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 14:23:57,008 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,009 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 14:23:57,009 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 14:23:57,009 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 14:23:57,014 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 14:23:57,014 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 14:23:57,014 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 14:23:57,015 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,015 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 14:23:57,015 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 14:23:57,016 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 14:23:57,019 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 14:23:57,019 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 14:23:57,019 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 14:23:57,019 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,019 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 14:23:57,019 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 14:23:57,020 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 14:23:57,020 - root - INFO - ASSET CHANGE DETECTED on 2025-05-22:
2025-06-26 14:23:57,020 - root - INFO -    Signal Date: 2025-05-21 (generated at 00:00 UTC)
2025-06-26 14:23:57,020 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-22 00:00 UTC (immediate)
2025-06-26 14:23:57,020 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,020 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 14:23:57,021 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 14:23:57,021 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 14:23:57,022 - root - INFO - ASSET CHANGE DETECTED on 2025-05-26:
2025-06-26 14:23:57,022 - root - INFO -    Signal Date: 2025-05-25 (generated at 00:00 UTC)
2025-06-26 14:23:57,023 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-26 00:00 UTC (immediate)
2025-06-26 14:23:57,023 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,023 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 14:23:57,023 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 14:23:57,023 - root - INFO -    AAVE/USDT buy price: $267.5200 (close price)
2025-06-26 14:23:57,031 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 14:23:57,031 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 14:23:57,031 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 14:23:57,031 - root - INFO -    Execution Delay: 0 hours
2025-06-26 14:23:57,032 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 14:23:57,032 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 14:23:57,032 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 14:23:57,065 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 14:23:57,066 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 14:23:57,066 - root - INFO - Swap trade at 2025-02-25 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 14:23:57,066 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 14:23:57,067 - root - INFO - Swap trade at 2025-03-10 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 14:23:57,067 - root - INFO - Swap trade at 2025-03-16 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 14:23:57,067 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 14:23:57,067 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: BNB/USDT -> PEPE/USDT
2025-06-26 14:23:57,068 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 14:23:57,068 - root - INFO - Swap trade at 2025-04-04 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 14:23:57,068 - root - INFO - Swap trade at 2025-04-20 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 14:23:57,068 - root - INFO - Swap trade at 2025-04-23 00:00:00+00:00: SOL/USDT -> PEPE/USDT
2025-06-26 14:23:57,068 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: PEPE/USDT -> SUI/USDT
2025-06-26 14:23:57,069 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 14:23:57,069 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 14:23:57,069 - root - INFO - Swap trade at 2025-05-22 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 14:23:57,069 - root - INFO - Swap trade at 2025-05-26 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 14:23:57,070 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 14:23:57,070 - root - INFO - Total trades: 18 (Entries: 1, Exits: 0, Swaps: 17)
2025-06-26 14:23:57,070 - root - INFO - Strategy execution completed in 0s
2025-06-26 14:23:57,071 - root - INFO - DEBUG: self.elapsed_time = 0.12178993225097656 seconds
2025-06-26 14:23:57,073 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 14:23:57,073 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 14:23:57,074 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 14:23:57,074 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 14:23:57,074 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 14:23:57,074 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 14:23:57,074 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 14:23:57,075 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 14:23:57,075 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 14:23:57,075 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 14:23:57,075 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 14:23:57,075 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 14:23:57,076 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 14:23:57,076 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 14:23:57,076 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 14:23:57,076 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 14:23:57,076 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 14:23:57,078 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,080 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,083 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,084 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,085 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,086 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,087 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,088 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,090 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,091 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,092 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,093 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,094 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,097 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 14:23:57,099 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 14:23:57,099 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 14:23:57,101 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 14:23:57,101 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 14:23:57,103 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 14:23:57,103 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 14:23:57,104 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 14:23:57,105 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 14:23:57,106 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 14:23:57,106 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 14:23:57,108 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 14:23:57,108 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 14:23:57,110 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 14:23:57,110 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 14:23:57,112 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 14:23:57,113 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 14:23:57,116 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 14:23:57,116 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 14:23:57,119 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 14:23:57,119 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 14:23:57,120 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 14:23:57,121 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 14:23:57,122 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 14:23:57,122 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 14:23:57,123 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 14:23:57,124 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 14:23:57,125 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 14:23:57,126 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 14:23:57,127 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 14:23:57,133 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:57,141 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 14:23:57,225 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 14:23:57,231 - root - INFO - Configuration loaded successfully.
2025-06-26 14:23:58,421 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,422 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,422 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,422 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,422 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,423 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,423 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,423 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,423 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,423 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,423 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,424 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,424 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,424 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 14:23:58,424 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 14:23:58,425 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,425 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,425 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,425 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,426 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,426 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,426 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,426 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,426 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,427 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,427 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,427 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,427 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,428 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 14:23:58,438 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 14:23:58,438 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 14:23:58,440 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 14:23:58,440 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 14:23:58,442 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 14:23:58,443 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 14:23:58,471 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 13.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 14:23:58,477 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_142344.csv
2025-06-26 14:23:58,477 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_142344.csv
2025-06-26 14:23:58,477 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 14:23:58,478 - root - INFO - Results type: <class 'dict'>
2025-06-26 14:23:58,478 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 14:23:58,479 - root - INFO - Success flag set to: True
2025-06-26 14:23:58,479 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 14:23:58,480 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 14:23:58,480 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 14:23:58,480 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 14:23:58,480 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 14:23:58,480 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 14:23:58,480 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 14:23:58,480 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 14:23:58,480 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 14:23:58,480 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 14:23:58,481 - root - INFO -   - success: <class 'bool'>
2025-06-26 14:23:58,481 - root - INFO -   - message: <class 'str'>
2025-06-26 14:23:58,481 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 14:23:58,481 - root - INFO - ASSET SELECTION DEBUG - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 14:23:58,482 - root - INFO - ASSET SELECTION DEBUG - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 14:23:58,498 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 14:23:58,499 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 14:23:58,499 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 14:23:58,499 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 14:23:58,500 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 14:23:58,500 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 14:23:58,500 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 14:23:58,500 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 14:23:58,500 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 14:23:58,778 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 14:23:58,778 - root - INFO - Available balance for EUR: 100.********
2025-06-26 14:23:58,786 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 14:23:58,786 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 14:23:58,786 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 14:23:58,786 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 14:23:58,786 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 14:23:58,789 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 14:23:58,789 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 14:23:58,789 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.********4914456, price: 0.23203
2025-06-26 14:23:58,789 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 14:23:58,790 - root - INFO - Order fee: 0.******** EUR
2025-06-26 14:23:58,790 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 14:23:58,792 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 14:23:58,792 - root - INFO -   Fee: 0.******** EUR
2025-06-26 14:23:58,792 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 14:23:58,806 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 14:23:58,806 - root - INFO -   Fee: 0.******** EUR
2025-06-26 14:23:58,807 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 14:23:58,807 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.88238589837521, 'price': 0.23203, 'order': {'id': 'paper-1750940638-TRX/EUR-buy-42.********4914456', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.********4914456, 'price': 0.23203, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.********4914456, 'remaining': 0, 'timestamp': 1750940638789, 'datetime': '2025-06-26T14:23:58.789080', 'trades': [], 'average': 0.23203, 'average_price': 0.23203}, 'filled_amount': 42.********4914456, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T14:23:58.790729'}
2025-06-26 14:23:58,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 14:23:58,882 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 14:23:58,883 - root - INFO - Asset scores (sorted by score):
2025-06-26 14:23:58,883 - root - INFO -   TRX/EUR: score=13.0, status=SELECTED, weight=1.00
2025-06-26 14:23:58,883 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,883 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,883 - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,883 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,883 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,884 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,884 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,884 - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,884 - root - INFO -   ADA/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,884 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,884 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,885 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,885 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 14:23:58,886 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 14:23:58,886 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 14:23:58,886 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 13.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 14:23:58,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 14:23:58,955 - root - INFO - Strategy execution completed successfully in 14.83 seconds
2025-06-26 14:23:58,958 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 14:24:03,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:24:13,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:24:23,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:24:33,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:24:43,657 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:24:53,672 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:25:03,687 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:25:13,693 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:25:23,715 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:25:33,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:25:43,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:25:53,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:26:03,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:26:13,796 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:26:23,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:26:33,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:26:43,871 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:26:53,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:27:03,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:27:13,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:27:23,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:27:33,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:27:43,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:27:54,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:28:04,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:28:14,043 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:28:24,052 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:28:34,077 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:28:44,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:28:54,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:29:04,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:29:14,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:29:24,163 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:29:34,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:29:44,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:29:54,322 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:30:04,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:30:14,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:30:24,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:30:34,384 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:30:44,388 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:30:54,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:31:04,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:31:14,435 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:31:24,472 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:31:34,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:31:44,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:31:54,535 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:32:04,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:32:14,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:32:24,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:32:34,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:32:44,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:32:54,618 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:33:04,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:33:14,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:33:24,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:33:34,677 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:33:44,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:33:52,062 - root - INFO - Received signal 2, shutting down...
2025-06-26 14:33:54,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 14:34:02,071 - root - INFO - Network watchdog stopped
2025-06-26 14:34:02,071 - root - INFO - Network watchdog stopped
2025-06-26 14:34:02,071 - root - INFO - Background service stopped
2025-06-26 14:34:02,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
