"""
Beta Detection System - Main Runner
Orchestrates beta calculation and display for crypto assets against BTC benchmark.
"""

import sys
import os
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
import pandas as pd

# Add parent directory to path to import from src
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.unified_data_fetcher import fetch_data
from Beta_detection.beta_calculator import BetaCalculator
from Beta_detection.beta_table_display import BetaTableDisplay
from Beta_detection.beta_config_manager import BetaConfigManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BetaDetectionRunner:
    """
    Main runner for beta detection system.
    Fetches data, calculates betas, and displays results.
    """

    def __init__(self, config_path: Optional[str] = None, measurement_length: Optional[int] = None):
        """
        Initialize beta detection runner.

        Args:
            config_path: Path to configuration file
            measurement_length: Override measurement length from config
        """
        # Load configuration
        self.config_manager = BetaConfigManager(config_path) if config_path else BetaConfigManager()

        # Use provided measurement_length or get from config
        self.measurement_length = measurement_length or self.config_manager.get_measurement_length()

        # Initialize components
        self.calculator = BetaCalculator(self.measurement_length)
        self.display = BetaTableDisplay()
        
    def run_beta_analysis(self,
                         symbols: Optional[List[str]] = None,
                         symbol_group: Optional[str] = None,
                         since: Optional[str] = None,
                         analysis_mode: str = 'standard',
                         save_results: Optional[bool] = None,
                         show_previous_comparison: bool = True,
                         show_gui: bool = False) -> Dict[str, Dict]:
        """
        Run complete beta analysis.

        Args:
            symbols: List of symbols to analyze (if None, uses config defaults)
            symbol_group: Use predefined symbol group from config
            since: Start date for data fetching (YYYY-MM-DD format)
            analysis_mode: Analysis mode ('quick', 'standard', 'comprehensive')
            save_results: Whether to save results to file (if None, uses config)
            show_previous_comparison: Whether to show comparison with previous results
            show_gui: Whether to show results in GUI window for better display

        Returns:
            Beta calculation results
        """
        logger.info("Starting beta detection analysis...")

        # Determine symbols to analyze
        if symbols is None:
            if symbol_group:
                symbols = self.config_manager.get_symbol_group(symbol_group)
                if not symbols:
                    logger.warning(f"Symbol group '{symbol_group}' not found, using defaults")
                    symbols = self.config_manager.get_default_symbols()
            else:
                symbols = self.config_manager.get_default_symbols()

        # Add BTC as benchmark if not present
        benchmark_symbol = self.config_manager.get_benchmark_symbol()
        if benchmark_symbol not in symbols:
            symbols.append(benchmark_symbol)

        # Apply analysis mode settings
        mode_config = self.config_manager.get_analysis_mode_config(analysis_mode)
        if 'measurement_length' in mode_config:
            self.calculator.measurement_length = mode_config['measurement_length']

        # Determine save_results setting
        if save_results is None:
            save_results = self.config_manager.should_save_results()
        
        logger.info(f"Analyzing {len(symbols)} symbols against {benchmark_symbol}")
        logger.info(f"Analysis mode: {analysis_mode}")

        # Fetch data
        logger.info("Fetching market data...")
        data_dict = self._fetch_market_data(symbols, since, analysis_mode)

        if not data_dict:
            logger.error("No data fetched, cannot proceed with beta analysis")
            return {}

        # Load previous results for comparison
        previous_betas = None
        if show_previous_comparison:
            previous_betas = self._load_latest_previous_results()

        # Calculate betas
        logger.info("Calculating beta values...")
        beta_results = self.calculator.calculate_multiple_betas(data_dict)

        if not beta_results:
            logger.error("No beta calculations completed")
            return {}

        # Display results
        logger.info("Displaying beta table...")
        self.display.create_beta_table(beta_results, previous_betas,
                                     self.config_manager.get_max_assets_per_column(),
                                     show_gui=show_gui)

        # Save results
        if save_results:
            filename = self.display.save_beta_results(beta_results)
            logger.info(f"Results saved to {filename}")

        # Print additional insights
        if self.config_manager.should_show_insights():
            self._print_insights(beta_results, data_dict)

        return beta_results
    
    def _fetch_market_data(self, symbols: List[str], since: Optional[str] = None,
                          analysis_mode: str = 'standard') -> Dict[str, pd.DataFrame]:
        """
        Fetch market data for all symbols.

        Args:
            symbols: List of symbols to fetch
            since: Start date for data fetching
            analysis_mode: Analysis mode for configuration

        Returns:
            Dictionary of DataFrames with OHLCV data
        """
        try:
            # Get data fetching configuration
            data_config = self.config_manager.get_data_fetching_config()
            mode_config = self.config_manager.get_analysis_mode_config(analysis_mode)

            # Calculate since date if not provided
            if since is None:
                since = self.config_manager.get_since_date(analysis_mode)

            logger.info(f"Fetching data since {since} (mode: {analysis_mode})")

            # Fetch data using unified data fetcher with config
            data_dict = fetch_data(
                symbols=symbols,
                timeframe=data_config.get('timeframe', '1d'),
                since=since,
                use_cache=data_config.get('use_cache', True),
                force_refresh=data_config.get('force_refresh', False),
                use_pagination=data_config.get('use_pagination', True),
                max_pages=mode_config.get('max_pages', data_config.get('max_pages', 5)),
                context=data_config.get('context', 'beta_analysis')
            )
            
            # Log data availability
            for symbol, df in data_dict.items():
                logger.info(f"{symbol}: {len(df)} candles from {df.index.min()} to {df.index.max()}")
            
            return data_dict
            
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return {}
    
    def _load_latest_previous_results(self) -> Optional[Dict[str, float]]:
        """Load the most recent previous beta results for comparison."""
        try:
            import glob
            
            # Find the most recent beta results file
            pattern = os.path.join("Beta_detection", "beta_results_*.json")
            files = glob.glob(pattern)
            
            if not files:
                logger.info("No previous beta results found")
                return None
            
            # Get the most recent file
            latest_file = max(files, key=os.path.getctime)
            logger.info(f"Loading previous results from {latest_file}")
            
            return self.display.load_previous_betas(latest_file)
            
        except Exception as e:
            logger.warning(f"Could not load previous results: {e}")
            return None
    
    def _print_insights(self, beta_results: Dict[str, Dict], data_dict: Dict[str, pd.DataFrame]) -> None:
        """Print additional insights about the beta analysis."""
        
        print("\n" + "="*80)
        print("BETA ANALYSIS INSIGHTS")
        print("="*80)
        
        # High beta assets (most volatile relative to BTC)
        high_beta_assets = [(symbol, data['beta']) for symbol, data in beta_results.items() 
                           if data['beta'] > 1.5]
        high_beta_assets.sort(key=lambda x: x[1], reverse=True)
        
        if high_beta_assets:
            print(f"\n🔥 HIGH BETA ASSETS (>1.5) - Most volatile relative to BTC:")
            for symbol, beta in high_beta_assets[:5]:  # Top 5
                print(f"   {symbol}: {beta:.3f}")
        
        # Low beta assets (less volatile than BTC)
        low_beta_assets = [(symbol, data['beta']) for symbol, data in beta_results.items() 
                          if data['beta'] < 1.0]
        low_beta_assets.sort(key=lambda x: x[1])
        
        if low_beta_assets:
            print(f"\n🛡️  LOW BETA ASSETS (<1.0) - Less volatile than BTC:")
            for symbol, beta in low_beta_assets[:5]:  # Bottom 5
                print(f"   {symbol}: {beta:.3f}")
        
        # Data quality summary
        print(f"\n📊 DATA QUALITY SUMMARY:")
        print(f"   Benchmark: {self.calculator.benchmark_symbol}")
        print(f"   Measurement Period: {self.measurement_length} days")
        print(f"   Assets Analyzed: {len(beta_results)}")
        print(f"   Assets Fetched: {len(data_dict)}")
        
        if self.calculator.benchmark_symbol in data_dict:
            btc_data = data_dict[self.calculator.benchmark_symbol]
            print(f"   BTC Data Points: {len(btc_data)}")
            print(f"   BTC Date Range: {btc_data.index.min().date()} to {btc_data.index.max().date()}")
        
        print("="*80)

def main():
    """Main entry point for beta detection system."""
    parser = argparse.ArgumentParser(description='Crypto Beta Detection System')
    parser.add_argument('--symbols', nargs='+', help='Symbols to analyze (default: TradingView list)')
    parser.add_argument('--since', type=str, help='Start date for analysis (YYYY-MM-DD)')
    parser.add_argument('--length', type=int, default=100, help='Beta measurement length in days')
    parser.add_argument('--no-save', action='store_true', help='Do not save results to file')
    parser.add_argument('--no-comparison', action='store_true', help='Do not show previous comparison')
    
    args = parser.parse_args()
    
    # Create runner
    runner = BetaDetectionRunner(measurement_length=args.length)
    
    # Run analysis
    try:
        results = runner.run_beta_analysis(
            symbols=args.symbols,
            since=args.since,
            save_results=not args.no_save,
            show_previous_comparison=not args.no_comparison
        )
        
        if results:
            print(f"\n✅ Beta analysis completed successfully for {len(results)} assets")
        else:
            print("\n❌ Beta analysis failed - no results generated")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Analysis interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error during beta analysis: {e}")
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
