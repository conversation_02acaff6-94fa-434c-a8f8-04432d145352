2025-06-26 17:53:39,942 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:53:40,665 - root - INFO - Loaded 51 trade records from logs/trades\trade_log_********.json
2025-06-26 17:53:40,672 - root - INFO - Loaded 30 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:53:40,672 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:53:41,640 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:41,654 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:41,659 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:41,661 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:41,661 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:41,673 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:41,673 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:53:41,673 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:53:42,471 - root - INFO - Telegram command handlers registered
2025-06-26 17:53:42,471 - root - INFO - Telegram bot polling started
2025-06-26 17:53:42,471 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:53:42,471 - root - INFO - Telegram notification channel initialized
2025-06-26 17:53:42,479 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:53:42,479 - root - INFO - Loaded 24 templates from file
2025-06-26 17:53:42,479 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:53:42,481 - root - INFO - Notification manager initialized
2025-06-26 17:53:42,481 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:53:42,481 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:53:42,481 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:53:42,481 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:53:42,486 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:53:42,486 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:53:42,487 - root - INFO - Recovery manager initialized
2025-06-26 17:53:42,487 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:53:42,487 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:53:42,487 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:42,491 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:42,491 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:53:42,491 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:53:42,491 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:53:42,491 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:53:42,491 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:53:42,491 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:53:42,491 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:53:42,499 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:53:42,499 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:42,505 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:42,531 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:53:42,541 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:53:42,543 - telegram.ext.Application - INFO - Application started
2025-06-26 17:53:42,856 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:53:42,857 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:53:42,857 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:53:42,857 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:53:42,858 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:53:42,858 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:42,861 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:42,866 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:42,873 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:42,873 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:53:42,879 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:42,879 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:53:42,881 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:53:42,881 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:53:42,881 - root - INFO - Trading mode: paper
2025-06-26 17:53:42,881 - root - INFO - Trading enabled: True
2025-06-26 17:53:42,881 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:53:42,881 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:53:42,881 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:53:42,881 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:53:42,881 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:42,888 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:43,136 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:53:43,136 - root - INFO - Trading enabled in paper mode
2025-06-26 17:53:43,139 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:53:43,139 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:53:43,139 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:53:43,139 - root - INFO - Generated run ID: ********_175343
2025-06-26 17:53:43,140 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:53:43,140 - root - INFO - Background service initialized
2025-06-26 17:53:43,141 - root - INFO - Network watchdog started
2025-06-26 17:53:43,141 - root - INFO - Network watchdog started
2025-06-26 17:53:43,143 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:53:43,143 - root - INFO - Background service started
2025-06-26 17:53:43,143 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:53:43,143 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:53:43,143 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:53:43,143 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:53:43,143 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:53:43,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:53:49,152 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:53:49,210 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:53:49,212 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:53:49,213 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:53:49,213 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:53:49,216 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:53:49,242 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,243 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,243 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,244 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:53:49,244 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,255 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,255 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,255 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,255 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 17:53:49,256 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,268 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,268 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,268 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,268 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:53:49,269 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,276 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,276 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,277 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,277 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:53:49,277 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,288 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,289 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,289 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,289 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:53:49,290 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,301 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,301 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,302 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,302 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:53:49,303 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,313 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,314 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,314 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,314 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:53:49,315 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,329 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,329 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,330 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,330 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:53:49,331 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,348 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,348 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,349 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,349 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:53:49,350 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,364 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,364 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,364 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,365 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:53:49,365 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,372 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,372 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,372 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,373 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:53:49,373 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,387 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,387 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,387 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,388 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:53:49,388 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,402 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,403 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,403 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,403 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:53:49,404 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,416 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,417 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,417 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,417 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:53:49,418 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,421 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 17:53:49,421 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:53:49,421 - root - INFO -   - Number of indicators: 8
2025-06-26 17:53:49,421 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:53:49,421 - root - INFO -   - Combination method: consensus
2025-06-26 17:53:49,421 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:53:49,422 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:53:49,422 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:53:49,422 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:53:49,422 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 17:53:49,422 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-26 17:53:49,423 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:53:49,423 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:53:49,423 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:53:49,423 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:53:49,423 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:53:49,423 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:53:49,423 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:53:49,423 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:49,431 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:49,432 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:49,436 - root - INFO - Configuration saved successfully.
2025-06-26 17:53:49,436 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:53:49,436 - root - INFO - Number of trend detection assets: 14
2025-06-26 17:53:49,436 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:53:49,436 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:53:49,437 - root - INFO - Number of trading assets: 14
2025-06-26 17:53:49,437 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:53:49,648 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:53:49,655 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:49,663 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:53:49,669 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:49,669 - root - INFO - Execution context: backtesting
2025-06-26 17:53:49,670 - root - INFO - Execution timing: candle_close
2025-06-26 17:53:49,670 - root - INFO - Ratio calculation method: independent
2025-06-26 17:53:49,670 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:53:49,670 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:53:49,670 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:53:49,670 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:53:49,671 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:53:49,671 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:53:49,671 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:53:49,672 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,673 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,674 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,674 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,675 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,676 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,676 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,677 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,679 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,679 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,679 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,680 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,680 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,681 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:53:49,681 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 17:53:49,695 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,697 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,697 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,698 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:53:49,711 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,712 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,713 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,713 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:53:49,726 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,727 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,728 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,728 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:53:49,734 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,735 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,736 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,736 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:53:49,750 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,751 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,751 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,752 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:53:49,762 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,763 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,764 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,764 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:53:49,774 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,776 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,776 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,777 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:53:49,789 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,791 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,791 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,792 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:53:49,804 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,805 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,805 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,806 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:53:49,819 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,820 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,820 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,820 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:53:49,827 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,828 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,829 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,829 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:53:49,842 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,843 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,844 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,844 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:53:49,857 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,859 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,860 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,860 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:53:49,871 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,871 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:53:49,872 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,872 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:53:49,872 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:53:49,873 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,873 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,873 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,873 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,874 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,874 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,874 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,874 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,875 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,875 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,875 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,876 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,876 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,876 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:53:49,889 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:53:49,889 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:53:49,889 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:53:49,889 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:53:49,890 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:53:49,896 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:49,897 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:53:49,897 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:53:49,897 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:53:49,898 - root - INFO - Override: combination_method = consensus
2025-06-26 17:53:49,898 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:53:49,898 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:53:49,898 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:53:49,899 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:53:49,899 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:53:49,899 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:53:49,911 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:53:49,911 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:53:49,913 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:53:49,913 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:53:49,913 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:53:49,913 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:53:49,935 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:53:49,935 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:53:49,935 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:53:49,936 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:53:49,945 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:53:49,946 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:53:49,946 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:53:50,263 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:53:50,264 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:53:50,301 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:53:50,302 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:53:50,302 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:53:50,302 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:53:50,368 - root - INFO - Generated DPSD signals
2025-06-26 17:53:50,369 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:53:50,369 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:53:50,369 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:53:50,377 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:53:50,377 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:53:50,377 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:53:50,421 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:53:50,421 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:53:50,422 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:53:50,493 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:53:50,498 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:53:50,498 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:53:50,499 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:53:50,499 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:53:50,499 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:53:50,504 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:53:50,510 - root - INFO - Configuration saved successfully.
2025-06-26 17:53:50,510 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:53:50,510 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:53:50,517 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:50,517 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:53:50,518 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 17:53:50,518 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:53:50,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,551 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:50,565 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:50,566 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,578 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:50,583 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:50,598 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:53:50,599 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,612 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:53:50,616 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:50,631 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:50,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,645 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:50,649 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:50,665 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:50,666 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,680 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:50,685 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:50,700 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:53:50,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,717 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:53:50,722 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:50,742 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:50,742 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,758 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:50,764 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:50,779 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:53:50,781 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,794 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:53:50,800 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:50,816 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:53:50,816 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,831 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:53:50,837 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:50,856 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:50,857 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,872 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:50,873 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:50,891 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:50,891 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,906 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:50,911 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:50,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,940 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:50,957 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:53:50,960 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:50,973 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:53:50,980 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:50,996 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:50,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,007 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:51,014 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:51,031 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,046 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:51,061 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:51,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,074 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:51,084 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:51,101 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:51,101 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,111 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:51,120 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:51,135 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:51,135 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,151 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:51,156 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:51,172 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:53:51,172 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,188 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:53:51,190 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:51,210 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:51,210 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,225 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:51,230 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:51,248 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:51,248 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,265 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:51,271 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:51,289 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:51,290 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,305 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:51,310 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:51,331 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:51,331 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,346 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:51,352 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:51,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,390 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:51,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,431 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:51,452 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:53:51,452 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,487 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:53:51,497 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:51,530 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:53:51,530 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,556 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:53:51,564 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:51,600 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,633 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:51,650 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:51,650 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,666 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:51,672 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:51,691 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:51,691 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,709 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:51,710 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:51,731 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:53:51,733 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,750 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:53:51,759 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:51,780 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:53:51,780 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,795 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:53:51,800 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:51,816 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:51,816 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,830 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:51,833 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:51,855 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:51,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,871 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:51,877 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:51,896 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:53:51,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,911 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:53:51,923 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:51,945 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:53:51,945 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:51,964 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:53:51,972 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:51,994 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:51,994 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,013 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:52,015 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:52,038 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:52,038 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,053 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:52,060 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:52,081 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:52,081 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,099 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:52,104 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:52,122 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:52,122 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,141 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:52,146 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:52,169 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,192 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:52,210 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,240 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:52,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,287 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:52,307 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:53:52,307 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,324 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:53:52,331 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:52,340 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,366 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:52,385 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,410 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:52,424 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,424 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,442 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,443 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:52,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,481 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:52,497 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,517 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:52,533 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:52,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,542 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:52,555 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:52,570 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,589 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:52,590 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:53:52,605 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,652 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:52,677 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:53:52,679 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,691 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:53:52,697 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:52,710 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,731 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:52,746 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,760 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:52,773 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:52,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,791 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:52,797 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:52,810 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,824 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,831 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:52,845 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:53:52,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,861 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:53:52,861 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:52,880 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:53:52,880 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,893 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:53:52,901 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:52,916 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,918 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,933 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,933 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:52,950 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:52,950 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:52,966 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:52,971 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:52,988 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:52,988 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,002 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:53:53,007 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:53,026 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,048 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:53,064 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,083 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:53,102 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:53:53,102 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,116 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:53:53,124 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:53,141 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,160 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:53,183 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,199 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:53,210 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,230 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:53,240 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,261 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:53,274 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,290 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:53,311 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:53,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,323 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:53,323 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:53,341 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:53,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,357 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:53,360 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:53,373 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:53,373 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,390 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:53,390 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:53,410 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,430 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:53,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,461 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:53,481 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:53:53,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,496 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:53:53,501 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:53,516 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,533 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:53,550 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:53,551 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,561 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:53,566 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:53,583 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,601 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:53,616 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,631 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:53,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,666 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:53,683 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,704 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:53,717 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,733 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:53,751 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:53:53,751 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,767 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:53:53,773 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:53,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,807 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:53,823 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:53:53,823 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,838 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:53:53,841 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:53,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,881 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:53,891 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,910 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:53,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,941 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:53,957 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:53,975 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:53,991 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,007 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:54,023 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,023 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,037 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,041 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:54,056 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,073 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:54,090 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,107 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:54,123 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:53:54,123 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,133 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:53:54,141 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:54,157 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,173 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:54,193 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:53:54,193 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,207 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:53:54,210 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:54,231 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:54,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,247 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:54,252 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:54,261 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:53:54,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,280 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:53:54,283 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:54,306 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,323 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:54,341 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:54,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,356 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:54,367 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:54,382 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:54,382 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,400 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:54,406 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:54,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,441 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:54,456 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,456 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,472 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,472 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:54,491 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,511 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:54,523 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,541 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:54,556 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,573 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:54,591 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,611 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:54,623 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,643 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:54,656 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:53:54,656 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,673 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:53:54,673 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:54,691 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,711 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:54,723 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,740 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:54,756 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,773 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:54,791 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,805 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,811 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:54,823 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,841 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:54,856 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,874 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:54,891 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,906 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:54,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,941 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:54,956 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:54,973 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:54,991 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:54,993 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,005 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:55,006 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:55,023 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,041 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:55,057 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:55,057 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,071 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:55,075 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:55,091 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:55,091 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,106 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:55,111 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:55,124 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:55,124 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,140 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:55,140 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:55,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,181 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:55,195 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:55,196 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,211 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:55,216 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:55,231 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:55,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,245 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:55,249 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:55,265 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,281 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:55,297 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,315 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:55,330 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,349 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:55,363 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,382 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:55,401 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,433 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:55,469 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,509 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:55,542 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,574 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:55,597 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,620 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:55,642 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:53:55,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,662 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:53:55,667 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:55,688 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:55,689 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,750 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:53:55,762 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:55,793 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:55,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,811 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:53:55,816 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:55,833 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:53:55,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,846 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:53:55,851 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:55,867 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,894 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:55,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,931 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:55,946 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,964 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:55,979 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:55,998 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:56,012 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,033 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:56,050 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,073 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:56,093 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,116 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:56,134 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:56,134 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,151 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:56,157 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:56,176 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:56,176 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,193 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:53:56,198 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:56,211 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:56,213 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,227 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:56,232 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:56,247 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:53:56,248 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,262 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:53:56,267 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:56,282 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:53:56,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,297 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:53:56,301 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:56,316 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:56,316 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,329 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:56,334 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:56,350 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:53:56,350 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,364 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:53:56,368 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:56,386 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:53:56,387 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,401 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:53:56,407 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:56,427 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,448 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:56,463 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:56,464 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,477 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:56,482 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:56,496 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,514 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:56,531 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,551 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:56,567 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:56,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,583 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:56,588 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:56,612 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:56,612 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,629 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:56,634 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:56,667 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:56,667 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,697 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:53:56,704 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:56,731 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:53:56,731 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,754 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:53:56,763 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:56,788 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:53:56,788 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,809 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:53:56,815 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:56,831 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:56,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,846 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:56,850 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:56,868 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:53:56,869 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,890 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:53:56,898 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:56,914 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:56,915 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,927 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:53:56,933 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:56,951 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:56,951 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:56,965 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:56,969 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:56,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,002 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:57,018 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,037 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:53:57,054 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,072 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:53:57,089 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,111 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:53:57,128 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,147 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:53:57,162 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:57,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,176 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:53:57,181 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:53:57,196 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:57,197 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,210 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:53:57,215 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:53:57,231 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:57,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,247 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:53:57,251 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:53:57,266 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:53:57,267 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,281 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:53:57,285 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:53:57,302 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:53:57,303 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,316 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:53:57,320 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:53:57,337 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:53:57,338 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,352 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:53:57,358 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:53:57,378 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,398 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:53:57,416 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:57,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,434 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:53:57,438 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:53:57,456 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,474 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:53:57,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:53:57,515 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:53:59,164 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:53:59,165 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 17:53:59,165 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:53:59,168 - root - INFO - Date ranges for each asset:
2025-06-26 17:53:59,169 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,169 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,170 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,170 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,170 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,170 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,170 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,171 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,171 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:53:59,171 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:53:59,176 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:53:59,176 - root - INFO -    Execution Method: candle_close
2025-06-26 17:53:59,176 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:53:59,177 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:53:59,181 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,181 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,181 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,182 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,183 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,183 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,183 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:53:59,183 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:53:59,183 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:53:59,183 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,184 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:53:59,184 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:53:59,184 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,185 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,186 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,186 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,186 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,186 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,186 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,186 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,187 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,188 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,188 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,188 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,188 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,188 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:53:59,188 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:53:59,188 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:53:59,188 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,188 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:53:59,189 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:53:59,189 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:53:59,190 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,191 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,192 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,194 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,195 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,195 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,195 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,195 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,195 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,195 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:53:59,198 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 17:53:59,198 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 17:53:59,198 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 17:53:59,198 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,198 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:53:59,198 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:53:59,198 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 17:53:59,199 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 17:53:59,199 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 17:53:59,200 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 17:53:59,200 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,200 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:53:59,200 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:53:59,200 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 17:53:59,200 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 17:53:59,200 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 17:53:59,201 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 17:53:59,201 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,201 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:53:59,201 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:53:59,201 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 17:53:59,203 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:53:59,203 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:53:59,203 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:53:59,203 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,203 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:53:59,203 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:53:59,204 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:53:59,207 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 17:53:59,207 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 17:53:59,207 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 17:53:59,207 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,208 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:53:59,208 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:53:59,208 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 17:53:59,209 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 17:53:59,209 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 17:53:59,209 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 17:53:59,209 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,209 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:53:59,209 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:53:59,210 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 17:53:59,210 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:53:59,210 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:53:59,210 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:53:59,211 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,211 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:53:59,211 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:53:59,211 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:53:59,212 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:53:59,212 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:53:59,212 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:53:59,212 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,212 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:53:59,212 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:53:59,212 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:53:59,214 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:53:59,214 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:53:59,214 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:53:59,214 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,214 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:53:59,214 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:53:59,214 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:53:59,215 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:53:59,215 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:53:59,215 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:53:59,215 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,215 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:53:59,215 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:53:59,215 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:53:59,216 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:53:59,216 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:53:59,216 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:53:59,216 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,216 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:53:59,216 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:53:59,216 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:53:59,219 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:53:59,219 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:53:59,219 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:53:59,219 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,219 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:53:59,219 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:53:59,219 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:53:59,219 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 17:53:59,219 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 17:53:59,219 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 17:53:59,219 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,219 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:53:59,219 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:53:59,219 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 17:53:59,226 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 17:53:59,226 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 17:53:59,226 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 17:53:59,226 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,226 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:53:59,227 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:53:59,227 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 17:53:59,229 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:53:59,229 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:53:59,229 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:53:59,229 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,229 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:53:59,229 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:53:59,229 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:53:59,234 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:53:59,234 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:53:59,234 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:53:59,235 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,235 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:53:59,235 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:53:59,235 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:53:59,238 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:53:59,239 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:53:59,239 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:53:59,239 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,239 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:53:59,239 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:53:59,240 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:53:59,241 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:53:59,241 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:53:59,241 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:53:59,241 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,241 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:53:59,242 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:53:59,242 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:53:59,244 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:53:59,244 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:53:59,244 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:53:59,244 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,244 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:53:59,244 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:53:59,244 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:53:59,252 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:53:59,252 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:53:59,252 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:53:59,253 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:53:59,253 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:53:59,253 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:53:59,253 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:53:59,285 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:53:59,285 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:53:59,286 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:53:59,286 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:53:59,287 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:53:59,287 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:53:59,287 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:53:59,287 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:53:59,288 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:53:59,288 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:53:59,288 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:53:59,288 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:53:59,288 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:53:59,289 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:53:59,289 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:53:59,289 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:53:59,289 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:53:59,289 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:53:59,289 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:53:59,290 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:53:59,290 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:53:59,290 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:53:59,290 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 17:53:59,293 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:53:59,293 - root - INFO - DEBUG: self.elapsed_time = 0.12830471992492676 seconds
2025-06-26 17:53:59,295 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:53:59,296 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:53:59,296 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:53:59,296 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 17:53:59,296 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:53:59,296 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:53:59,296 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:53:59,297 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:53:59,297 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:53:59,297 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:53:59,297 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:53:59,297 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:53:59,297 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:53:59,298 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:53:59,298 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:53:59,298 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:53:59,298 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:53:59,299 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,301 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,302 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,302 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,303 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,304 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,304 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,306 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,307 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,309 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,311 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,311 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,312 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,313 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:53:59,315 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:53:59,315 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:53:59,316 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 17:53:59,317 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 17:53:59,318 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:53:59,318 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:53:59,319 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:53:59,320 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:53:59,321 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:53:59,321 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:53:59,324 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:53:59,325 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:53:59,327 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:53:59,327 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:53:59,329 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:53:59,329 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:53:59,330 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:53:59,330 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:53:59,330 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:53:59,330 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:53:59,334 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:53:59,334 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:53:59,334 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:53:59,334 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:53:59,334 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:53:59,334 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:53:59,339 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:53:59,340 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:53:59,340 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:53:59,340 - root - INFO - Configuration loaded successfully.
2025-06-26 17:53:59,357 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:53:59,441 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:53:59,447 - root - INFO - Configuration loaded successfully.
2025-06-26 17:54:00,680 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:54:00,680 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 17:54:00,680 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,680 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,680 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,680 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,680 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,690 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,690 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,691 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,691 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,691 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,691 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,692 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,692 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,692 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:54:00,702 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:54:00,702 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:54:00,706 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:54:00,707 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:54:00,707 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:54:00,710 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:54:00,710 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:54:00,710 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:54:00,710 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:54:00,710 - root - ERROR - [DEBUG] CONVERSION - TIED USDT ASSETS: ['BTC/USDT', 'TRX/USDT'] (score: 12.0)
2025-06-26 17:54:00,710 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 12.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - TIED EUR ASSETS: ['BTC/EUR', 'TRX/EUR'] (score: 12.0)
2025-06-26 17:54:00,712 - root - ERROR - [DEBUG] CONVERSION - First EUR asset (should be selected): BTC/EUR
2025-06-26 17:54:00,712 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:54:00,718 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_175343.csv
2025-06-26 17:54:00,718 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_175343.csv
2025-06-26 17:54:00,718 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:54:00,720 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:54:00,720 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:54:00,720 - root - INFO - Success flag set to: True
2025-06-26 17:54:00,720 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:54:00,720 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:54:00,720 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 17:54:00,720 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:54:00,720 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:54:00,720 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:54:00,720 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:54:00,722 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:54:00,722 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:54:00,722 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:54:00,722 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:54:00,722 - root - INFO -   - message: <class 'str'>
2025-06-26 17:54:00,722 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:54:00,722 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 17:54:00,722 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 17:54:00,723 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 17:54:00,723 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 17:54:00,734 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 17:54:00,734 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:54:00,734 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:54:00,734 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 17:54:00,738 - root - INFO - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 17:54:00,738 - root - INFO - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 17:54:00,738 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-26 17:54:00,738 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-26 17:54:00,738 - root - WARNING - [DEBUG] TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 17:54:00,739 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:54:00,739 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:54:00,739 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:54:00,739 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:54:00,739 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:54:00,740 - root - INFO - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 17:54:00,740 - root - INFO - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 17:54:00,740 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:54:00,740 - root - INFO - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 17:54:00,741 - root - INFO - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-26 17:54:00,741 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-26 17:54:00,741 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 17:54:00,741 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 17:54:01,101 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 17:54:01,101 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-26 17:54:01,140 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-26 17:54:01,140 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1750953237544, 'datetime': '2025-06-26T15:53:57.544Z', 'high': 0.23507, 'low': 0.23076, 'bid': 0.23167, 'bidVolume': 2614.416654, 'ask': 0.23175, 'askVolume': 8274.279772, 'vwap': 0.23279518077661276, 'open': 0.23314, 'close': 0.23173, 'last': 0.23173, 'previousClose': None, 'change': -0.00141, 'percentage': -0.6047868233679334, 'average': 0.232435, 'baseVolume': 1379893.457226, 'quoteVolume': 321232.54682739184, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1750866837544, 'timestamp': 1750953237544, 'open': '0.23314', 'openTimestamp': 1750866860996, 'high': '0.23507', 'low': '0.23076', 'last': '0.23173', 'closeTimestamp': 1750953119459, 'bid': '0.2316700', 'bidSize': '2614.416654', 'ask': '0.2317500', 'askSize': '8274.279772', 'volume': '1379893.457226', 'volumeQuote': '321232.54682739184'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 17:54:01,149 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23173
2025-06-26 17:54:01,149 - root - INFO - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23173
2025-06-26 17:54:01,149 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 17:54:01,150 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 17:54:01,150 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 17:54:01,150 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:54:01,150 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:54:01,152 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:54:01,154 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 17:54:01,154 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 17:54:01,154 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:54:01,154 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:54:01,154 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:54:01,156 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:54:01,156 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.**************, price: 0.23173
2025-06-26 17:54:01,156 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:54:01,156 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:54:01,157 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:54:01,165 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:54:01,165 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:54:01,165 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:54:01,181 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:54:01,181 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:54:01,181 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:54:01,181 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.93790186855392, 'price': 0.23173, 'order': {'id': 'paper-1750953241-TRX/EUR-buy-42.**************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.**************, 'price': 0.23173, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.**************, 'remaining': 0, 'timestamp': 1750953241154, 'datetime': '2025-06-26T17:54:01.154315', 'trades': [], 'average': 0.23173, 'average_price': 0.23173}, 'filled_amount': 42.**************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:54:01.157485'}
2025-06-26 17:54:01,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:54:01,264 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:54:01,265 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:54:01,265 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,265 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:54:01,265 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,266 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,268 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,268 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,268 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,268 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:54:01,268 - root - INFO - Rejected assets:
2025-06-26 17:54:01,269 - root - INFO -   BTC/EUR: reason=Failed to trade, score=12.0, rank=1
2025-06-26 17:54:01,269 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 12.0) - Failed to trade
2025-06-26 17:54:01,269 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 17:54:01,269 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:54:01,270 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:54:01,314 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:54:01,317 - root - INFO - Strategy execution completed successfully in 18.17 seconds
2025-06-26 17:54:01,320 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:54:02,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:54:12,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:54:22,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:54:32,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:54:42,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:54:52,784 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:55:02,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:55:12,816 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:55:22,830 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:55:32,842 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:55:42,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:55:52,872 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:56:02,890 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:56:12,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:56:22,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:56:32,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:56:42,970 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:56:52,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:57:03,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:57:13,021 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:57:23,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:57:33,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:57:43,083 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:57:53,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:58:03,124 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:58:13,146 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:58:23,168 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:58:33,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:58:43,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:58:53,231 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:59:03,252 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:59:13,264 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:59:23,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:59:33,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:59:43,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:59:53,327 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:00:03,335 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:00:13,354 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:00:23,370 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:00:33,389 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:00:43,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:00:53,422 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:03,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:13,461 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:23,476 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:33,491 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:43,506 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:51,008 - root - INFO - Received signal 2, shutting down...
2025-06-26 18:01:53,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:01:56,018 - root - INFO - Received signal 2, shutting down...
2025-06-26 18:01:56,018 - root - WARNING - Service is not running
