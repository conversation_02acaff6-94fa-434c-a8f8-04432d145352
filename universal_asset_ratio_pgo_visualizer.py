#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Universal Asset Ratio PGO Visualizer

This script creates PGO visualizations for any asset ratios against different benchmark assets
(e.g., TRX/BTC, SOL/ETH, etc.) using the same layout as the BTC PGO visualizer.
Each ratio gets its own image for TradingView cross-checking.
Supports configurable asset selection via command line arguments.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec
import matplotlib.dates as mdates
import logging
import sys
import os
import argparse

# Add the project root to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import our modules
from src.data_fetcher import fetch_ohlcv_data
from src.config_manager import load_config
# Base indicators are imported in MTPI_signal_handler
from src.MTPI_signal_handler import calculate_pgo, generate_pgo_signal
import yaml

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_assets_from_yaml(config_path: str, group_name: str = None) -> list:
    """
    Load assets from YAML configuration file.

    Args:
        config_path: Path to the YAML configuration file
        group_name: Specific group name to load (e.g., 'default_symbols', 'defi_focus')
                   If None, tries to load from main config structure

    Returns:
        List of asset symbols
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        if group_name:
            # Load from specific group in assets section
            assets = config.get('assets', {}).get(group_name, [])
        else:
            # Try different common structures
            if 'assets' in config:
                if isinstance(config['assets'], dict):
                    # Try default_symbols first, then other common names
                    assets = (config['assets'].get('default_symbols') or
                             config['assets'].get('symbols') or
                             config['assets'].get('assets', []))
                else:
                    # Direct list
                    assets = config['assets']
            elif 'settings' in config and 'assets' in config['settings']:
                assets = config['settings']['assets']
            else:
                assets = []

        # Extract just the symbol part (remove /USDT, /USDC suffixes for processing)
        symbols = []
        for asset in assets:
            if isinstance(asset, str):
                # Extract base symbol (e.g., "BTC/USDT" -> "BTC")
                base_symbol = asset.split('/')[0]
                symbols.append(base_symbol)

        logging.info(f"Loaded {len(symbols)} assets from {config_path}" +
                    (f" (group: {group_name})" if group_name else ""))
        return symbols

    except Exception as e:
        logging.error(f"Error loading assets from {config_path}: {e}")
        return []

def get_available_asset_groups(config_path: str) -> list:
    """
    Get available asset groups from a YAML configuration file.

    Args:
        config_path: Path to the YAML configuration file

    Returns:
        List of available group names
    """
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)

        assets_section = config.get('assets', {})
        if isinstance(assets_section, dict):
            return list(assets_section.keys())
        else:
            return []

    except Exception as e:
        logging.error(f"Error reading asset groups from {config_path}: {e}")
        return []

def load_default_benchmark_assets() -> list:
    """
    Load default benchmark assets from the main config file.

    Returns:
        List of asset symbols to use as benchmarks
    """
    try:
        # Try to load from main config file
        config = load_config('config/settings.yaml')

        # Try different possible locations for assets
        settings = config.get('settings', {})

        # Look for trend_assets first, then assets, then fall back to hardcoded
        assets = (settings.get('trend_assets') or
                 settings.get('assets') or
                 config.get('assets', []))

        if assets:
            # Extract base symbols (remove /USDT, /USDC suffixes)
            symbols = []
            for asset in assets:
                if isinstance(asset, str) and '/' in asset:
                    base_symbol = asset.split('/')[0]
                    symbols.append(base_symbol)

            if symbols:
                logging.info(f"Loaded {len(symbols)} benchmark assets from config: {symbols[:5]}{'...' if len(symbols) > 5 else ''}")
                return symbols

        # Fallback to hardcoded list
        fallback = ['BTC', 'ETH', 'SOL', 'AAVE', 'AVAX', 'ADA', 'LINK', 'DOT', 'LTC', 'XRP']
        logging.info(f"Using fallback benchmark assets: {fallback}")
        return fallback

    except Exception as e:
        logging.warning(f"Error loading benchmark assets from config: {e}")
        # Fallback to hardcoded list
        fallback = ['BTC', 'ETH', 'SOL', 'AAVE', 'AVAX', 'ADA', 'LINK', 'DOT', 'LTC', 'XRP']
        logging.info(f"Using fallback benchmark assets: {fallback}")
        return fallback

def calculate_ratio_series(price_a: pd.Series, price_b: pd.Series) -> pd.Series:
    """Calculate the ratio between two price series."""
    # Align the series by their common index
    common_index = price_a.index.intersection(price_b.index)
    aligned_a = price_a.reindex(common_index)
    aligned_b = price_b.reindex(common_index)
    
    # Calculate ratio (avoid division by zero)
    ratio = aligned_a / aligned_b.replace(0, np.nan)
    return ratio.dropna()

def create_ratio_ohlcv(df_a: pd.DataFrame, df_b: pd.DataFrame) -> pd.DataFrame:
    """Create OHLCV data for the ratio between two assets."""
    # Align dataframes by common index
    common_index = df_a.index.intersection(df_b.index)
    df_a_aligned = df_a.reindex(common_index)
    df_b_aligned = df_b.reindex(common_index)

    # Calculate ratio OHLCV
    ratio_df = pd.DataFrame(index=common_index)
    ratio_df['open'] = df_a_aligned['open'] / df_b_aligned['open']
    ratio_df['high'] = df_a_aligned['high'] / df_b_aligned['high']
    ratio_df['low'] = df_a_aligned['low'] / df_b_aligned['low']
    ratio_df['close'] = df_a_aligned['close'] / df_b_aligned['close']
    ratio_df['volume'] = df_a_aligned['volume']  # Use volume from asset A

    return ratio_df.dropna()

def filter_data_by_end_date(df: pd.DataFrame, end_date: str) -> pd.DataFrame:
    """
    Filter DataFrame to include only data up to the specified end date.

    Args:
        df: DataFrame with datetime index
        end_date: End date in YYYY-MM-DD format

    Returns:
        Filtered DataFrame
    """
    if not end_date:
        return df

    try:
        # Convert end_date string to datetime
        end_datetime = pd.to_datetime(end_date)

        # Filter data up to and including the end date
        filtered_df = df[df.index <= end_datetime]

        logging.info(f"Filtered data from {len(df)} to {len(filtered_df)} rows (end date: {end_date})")
        return filtered_df

    except Exception as e:
        logging.warning(f"Error filtering data by end date '{end_date}': {e}. Using all available data.")
        return df

def plot_ratio_pgo(
    ratio_df: pd.DataFrame,
    ratio_name: str,
    pgo_length: int = 35,
    upper_threshold: float = 1.1,
    lower_threshold: float = -0.58,
    output_file: str = None,
    show_score: bool = True,
    end_date: str = None,
    vertical_line_at: str = None
):
    """
    Plots ratio price chart with PGO indicator in TradingView-like style.

    Args:
        ratio_df: DataFrame with ratio OHLCV data
        ratio_name: Name of the ratio (e.g., "TRX/BTC")
        pgo_length: The period length for PGO calculation
        upper_threshold: The threshold for long signals
        lower_threshold: The threshold for short signals
        output_file: Path to save the plot
        show_score: Whether to display the current score on the chart
        end_date: End date for analysis (used in chart title)
        vertical_line_at: Date to draw vertical line at (YYYY-MM-DD format)
    """
    # Calculate PGO
    pgo_values = calculate_pgo(ratio_df, length=pgo_length)
    
    # Generate PGO signal
    pgo_signal = generate_pgo_signal(
        df=ratio_df,
        length=pgo_length,
        upper_threshold=upper_threshold,
        lower_threshold=lower_threshold
    )
    
    # Set up the plot style to mimic TradingView dark theme
    plt.style.use('dark_background')
    
    # Create figure and subplots
    fig = plt.figure(figsize=(14, 10))
    gs = gridspec.GridSpec(2, 1, height_ratios=[3, 1])
    
    # Price chart subplot
    ax1 = plt.subplot(gs[0])
    ax2 = plt.subplot(gs[1], sharex=ax1)
    
    # Format dates on x-axis
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%b %y'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    
    # Set grid style
    ax1.grid(True, linestyle='--', alpha=0.2, color='gray')
    ax2.grid(True, linestyle='--', alpha=0.2, color='gray')
    
    # Set background color
    fig.patch.set_facecolor('#121212')
    ax1.set_facecolor('#121212')
    ax2.set_facecolor('#121212')
    
    # Plot ratio chart with colored bars based on signal
    for i in range(len(ratio_df)):
        # Get data for this bar
        date = ratio_df.index[i]
        open_price = ratio_df['open'].iloc[i]
        close_price = ratio_df['close'].iloc[i]
        high_price = ratio_df['high'].iloc[i]
        low_price = ratio_df['low'].iloc[i]
        signal = pgo_signal.iloc[i] if i < len(pgo_signal) else 0
        
        # Determine color based on signal
        color = '#0EFF7A' if signal == 1 else '#F10A3C' if signal == -1 else '#808080'
        
        # Plot candlestick
        # Body
        body_height = abs(close_price - open_price)
        body_bottom = min(open_price, close_price)
        
        # Plot the body as a rectangle
        if body_height > 0:
            ax1.add_patch(plt.Rectangle(
                (mdates.date2num(date) - 0.4, body_bottom),
                0.8, body_height,
                fill=True, color=color, alpha=0.8
            ))
        
        # Wick
        ax1.plot([mdates.date2num(date), mdates.date2num(date)],
                [low_price, high_price],
                color=color, linewidth=1)
    
    # Plot PGO indicator
    ax2.plot(ratio_df.index, pgo_values, color='#3A7BD5', linewidth=1.5)
    
    # Add horizontal lines for thresholds
    ax2.axhline(y=upper_threshold, color='#808080', linestyle='--', alpha=0.7)
    ax2.axhline(y=lower_threshold, color='#808080', linestyle='--', alpha=0.7)
    ax2.axhline(y=0, color='#808080', linestyle='-', alpha=0.5)
    
    # Fill areas above/below thresholds
    ax2.fill_between(ratio_df.index, pgo_values, upper_threshold,
                    where=(pgo_values > upper_threshold),
                    color='#0EFF7A', alpha=0.3)
    ax2.fill_between(ratio_df.index, pgo_values, lower_threshold,
                    where=(pgo_values < lower_threshold),
                    color='#F10A3C', alpha=0.3)
    
    # Set titles and labels
    title = f'{ratio_name} Price Chart with PGO Signal'
    if end_date:
        title += f' (up to {end_date})'
    ax1.set_title(title, fontsize=14, color='white')
    ax1.set_ylabel('Ratio Value', fontsize=12, color='white')
    
    ax2.set_title(f'PGO Indicator (Length: {pgo_length}, Thresholds: {upper_threshold}/{lower_threshold})',
                 fontsize=12, color='white')
    ax2.set_ylabel('PGO Value', fontsize=10, color='white')
    ax2.set_xlabel('Date', fontsize=12, color='white')
    
    # Style the tick labels
    ax1.tick_params(axis='both', colors='white')
    ax2.tick_params(axis='both', colors='white')
    
    # Rotate x-axis labels for better readability
    plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')
    plt.setp(ax2.get_xticklabels(), rotation=45, ha='right')
    
    # Create legend elements (will be used later, possibly with vertical line)
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='#0EFF7A', edgecolor='#0EFF7A', label='Long Signal (PGO > 1.1)'),
        Patch(facecolor='#F10A3C', edgecolor='#F10A3C', label='Short Signal (PGO < -0.58)')
    ]

    # Add score display on the image
    if show_score and len(pgo_signal) > 0:
        current_score = 1 if pgo_signal.iloc[-1] == 1 else 0
        score_color = '#0EFF7A' if current_score == 1 else '#F10A3C'
        score_text = f"Score: {current_score}"

        # Add score text box in upper right corner
        ax1.text(0.98, 0.95, score_text, transform=ax1.transAxes,
                fontsize=16, fontweight='bold', color=score_color,
                bbox=dict(boxstyle='round,pad=0.5', facecolor='black',
                         edgecolor=score_color, linewidth=2),
                horizontalalignment='right', verticalalignment='top')

    # Add vertical line if specified
    if vertical_line_at:
        try:
            vertical_date = pd.to_datetime(vertical_line_at)

            # Ensure vertical_date has the same timezone as the data index
            if ratio_df.index.tz is not None:
                if vertical_date.tz is None:
                    vertical_date = vertical_date.tz_localize(ratio_df.index.tz)
                else:
                    vertical_date = vertical_date.tz_convert(ratio_df.index.tz)
            elif vertical_date.tz is not None:
                vertical_date = vertical_date.tz_localize(None)

            # Check if the date is within the data range
            if vertical_date >= ratio_df.index.min() and vertical_date <= ratio_df.index.max():
                # Draw vertical line on both subplots
                ax1.axvline(x=vertical_date, color='yellow', linestyle='-', linewidth=2, alpha=0.8)
                ax2.axvline(x=vertical_date, color='yellow', linestyle='-', linewidth=2, alpha=0.8)

                # Add vertical line to legend
                legend_elements.append(Patch(facecolor='yellow', edgecolor='yellow', label=f'Analysis Date: {vertical_line_at}'))

                logging.info(f"Added vertical line at {vertical_line_at} for {ratio_name}")
            else:
                logging.warning(f"Date {vertical_line_at} is outside data range for {ratio_name} ({ratio_df.index.min().strftime('%Y-%m-%d')} to {ratio_df.index.max().strftime('%Y-%m-%d')})")
        except Exception as e:
            logging.warning(f"Error adding vertical line at {vertical_line_at}: {e}")

    # Add legend (with or without vertical line)
    ax1.legend(handles=legend_elements, loc='upper left', framealpha=0.7)

    # Adjust layout
    plt.tight_layout()
    
    # Save the plot
    if output_file:
        plt.savefig(output_file, facecolor=fig.get_facecolor(), dpi=150, bbox_inches='tight')
        logging.info(f"Saved {ratio_name} plot to {output_file}")
    
    plt.close(fig)
    
    return pgo_signal

def get_signal_at_date(ratio_df: pd.DataFrame, pgo_signal: pd.Series, target_date: str, pgo_length: int = 35) -> dict:
    """
    Get signal information at a specific date.

    Args:
        ratio_df: DataFrame with ratio OHLCV data
        pgo_signal: Series with PGO signals
        target_date: Target date in YYYY-MM-DD format
        pgo_length: PGO length parameter

    Returns:
        Dictionary with signal information at the target date
    """
    try:
        target_datetime = pd.to_datetime(target_date)

        # Ensure target_datetime has the same timezone as the data index
        if ratio_df.index.tz is not None:
            if target_datetime.tz is None:
                target_datetime = target_datetime.tz_localize(ratio_df.index.tz)
            else:
                target_datetime = target_datetime.tz_convert(ratio_df.index.tz)
        elif target_datetime.tz is not None:
            target_datetime = target_datetime.tz_localize(None)

        # Find the closest date in the data
        if target_datetime in ratio_df.index:
            exact_date = target_datetime
        else:
            # Find the closest date
            available_dates = ratio_df.index
            closest_date = min(available_dates, key=lambda x: abs((x - target_datetime).total_seconds()))
            exact_date = closest_date

        # Get the index position
        date_idx = ratio_df.index.get_loc(exact_date)

        # Get signal at that date
        signal_value = pgo_signal.iloc[date_idx] if date_idx < len(pgo_signal) else 0

        # Get PGO value at that date
        pgo_values = calculate_pgo(ratio_df, length=pgo_length)
        pgo_value = pgo_values.iloc[date_idx] if date_idx < len(pgo_values) else 0.0

        # Get ratio value at that date
        ratio_value = ratio_df['close'].iloc[date_idx]

        return {
            'target_date': target_date,
            'actual_date': exact_date.strftime('%Y-%m-%d'),
            'signal': signal_value,
            'pgo_value': pgo_value,
            'ratio_value': ratio_value,
            'signal_text': 'LONG' if signal_value == 1 else 'SHORT' if signal_value == -1 else 'NEUTRAL'
        }

    except Exception as e:
        logging.warning(f"Error getting signal at date {target_date}: {e}")
        return {
            'target_date': target_date,
            'actual_date': 'N/A',
            'signal': 0,
            'pgo_value': 0.0,
            'ratio_value': 0.0,
            'signal_text': 'ERROR'
        }

def log_ratio_signal_transitions(ratio_df, pgo_signal, ratio_name, pgo_length=35):
    """Log signal transitions for the ratio and show last PGO values."""
    if len(pgo_signal) == 0:
        return

    print(f"\n" + "="*80)
    print(f"{ratio_name} PGO SIGNAL ENTRY AND EXIT DATES")
    print("="*80)

    # Calculate and show last PGO values
    pgo_values = calculate_pgo(ratio_df, length=pgo_length)
    if len(pgo_values) > 0:
        last_pgo = pgo_values.iloc[-1]
        print(f"🔍 DEBUGGING - Last PGO value: {last_pgo:.6f}")

        # Show last 10 PGO values for better debugging insight
        num_values_to_show = min(10, len(pgo_values))
        print(f"🔍 DEBUGGING - Last {num_values_to_show} PGO values:")
        for i in range(num_values_to_show):
            idx = -(num_values_to_show-i)
            date = pgo_values.index[idx]
            value = pgo_values.iloc[idx]
            signal_at_date = pgo_signal.iloc[idx] if idx < len(pgo_signal) else 0
            signal_indicator = "📈 LONG" if signal_at_date == 1 else "📉 SHORT" if signal_at_date == -1 else "⚪ NEUTRAL"
            print(f"  {date.strftime('%Y-%m-%d')}: {value:+8.6f} | {signal_indicator}")

        # Show PGO statistics for debugging
        print(f"🔍 DEBUGGING - PGO Statistics:")
        print(f"  Min PGO: {pgo_values.min():+8.6f}")
        print(f"  Max PGO: {pgo_values.max():+8.6f}")
        print(f"  Mean PGO: {pgo_values.mean():+8.6f}")
        print(f"  Std PGO: {pgo_values.std():8.6f}")

    # Calculate market exposure
    in_market_days = sum(1 for s in pgo_signal if s == 1)
    total_days = len(pgo_signal)
    market_exposure = (in_market_days / total_days) * 100 if total_days > 0 else 0

    print(f"Market exposure: {in_market_days} out of {total_days} days ({market_exposure:.1f}%)")

    # Count entries and exits
    entries = sum(1 for i in range(1, len(pgo_signal)) if pgo_signal.iloc[i-1] != 1 and pgo_signal.iloc[i] == 1)
    exits = sum(1 for i in range(1, len(pgo_signal)) if pgo_signal.iloc[i-1] == 1 and pgo_signal.iloc[i] != 1)

    print(f"Number of entries: {entries}")
    print(f"Number of exits: {exits}")

    # Show current signal
    if len(pgo_signal) > 0:
        current_signal = pgo_signal.iloc[-1]
        current_date = ratio_df.index[-1]
        current_ratio = ratio_df['close'].iloc[-1]
        signal_text = "LONG" if current_signal == 1 else "SHORT" if current_signal == -1 else "NEUTRAL"
        print(f"Current signal ({current_date.strftime('%Y-%m-%d')}): {signal_text} (Ratio: {current_ratio:.6f})")

def main():
    """Main function to run the ratio visualizations"""
    # Load default benchmarks from config
    default_benchmarks = load_default_benchmark_assets()

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate asset ratio PGO visualizations')

    # Asset selection options
    asset_group = parser.add_mutually_exclusive_group(required=True)
    asset_group.add_argument('--asset', help='Primary asset symbol (e.g., TRX, SOL, ETH)')
    asset_group.add_argument('--config-assets', help='Load assets from YAML config file (path:group_name, e.g., config/settings.yaml:assets or Beta_detection/beta_config.yaml:default_symbols)')
    asset_group.add_argument('--all-config-assets', help='Process all assets from YAML config file as primary assets (path:group_name)')

    # Analysis parameters
    parser.add_argument('--start-date', help='Start date for analysis (YYYY-MM-DD). If not provided, fetches recent data')
    parser.add_argument('--end-date', help='End date for analysis (YYYY-MM-DD). If not provided, uses all available data up to present')
    parser.add_argument('--vertical-line-at', help='Add vertical line at specific date (YYYY-MM-DD) and show signal status on that date')
    parser.add_argument('--pgo-length', type=int, default=35, help='PGO length parameter (default: 35)')
    parser.add_argument('--upper-threshold', type=float, default=1.1, help='PGO upper threshold (default: 1.1)')
    parser.add_argument('--lower-threshold', type=float, default=-0.58, help='PGO lower threshold (default: -0.58)')

    # Benchmark configuration
    parser.add_argument('--benchmarks', nargs='+', default=default_benchmarks,
                       help=f'Benchmark assets to compare against (default: loaded from config - {" ".join(default_benchmarks[:5])}{"..." if len(default_benchmarks) > 5 else ""})')
    parser.add_argument('--quote-currency', default='USDT', help='Quote currency for pairs (default: USDT)')

    # Data fetching parameters
    parser.add_argument('--exchange', default='binance', help='Exchange to fetch data from (default: binance)')
    parser.add_argument('--timeframe', default='1d', help='Timeframe for analysis (default: 1d)')
    parser.add_argument('--limit', type=int, default=500, help='Number of candles to fetch when no start date is provided (default: 500)')

    # Display options
    parser.add_argument('--list-groups', help='List available asset groups in a YAML config file (provide path)')
    parser.add_argument('--no-score', action='store_true', help='Do not display score on images')
    
    args = parser.parse_args()

    # Handle list-groups option
    if args.list_groups:
        groups = get_available_asset_groups(args.list_groups)
        print(f"Available asset groups in {args.list_groups}:")
        for group in groups:
            print(f"  - {group}")
        return

    # Configuration
    exchange_id = args.exchange
    timeframe = args.timeframe
    start_date = args.start_date
    end_date = args.end_date
    vertical_line_at = args.vertical_line_at
    pgo_length = args.pgo_length
    upper_threshold = args.upper_threshold
    lower_threshold = args.lower_threshold
    quote_currency = args.quote_currency.upper()
    benchmarks = [b.upper() for b in args.benchmarks]
    limit = args.limit
    show_score = not args.no_score

    # Determine primary assets to process
    primary_assets = []

    if args.asset:
        # Single asset mode
        primary_assets = [args.asset.upper()]
    elif args.config_assets:
        # Load specific assets from config
        config_path, group_name = args.config_assets.split(':') if ':' in args.config_assets else (args.config_assets, None)
        assets = load_assets_from_yaml(config_path, group_name)
        if not assets:
            logging.error(f"No assets found in {config_path}" + (f" group {group_name}" if group_name else ""))
            return
        # Use first asset as primary, rest as benchmarks
        primary_assets = [assets[0].upper()]
        if len(assets) > 1:
            benchmarks = [a.upper() for a in assets[1:]]
            logging.info(f"Using {primary_assets[0]} as primary asset and {len(benchmarks)} assets as benchmarks")
    elif args.all_config_assets:
        # Process all assets from config as primary assets
        config_path, group_name = args.all_config_assets.split(':') if ':' in args.all_config_assets else (args.all_config_assets, None)
        assets = load_assets_from_yaml(config_path, group_name)
        if not assets:
            logging.error(f"No assets found in {config_path}" + (f" group {group_name}" if group_name else ""))
            return
        primary_assets = [a.upper() for a in assets]
        logging.info(f"Processing {len(primary_assets)} assets from config as primary assets")
    
    # Process each primary asset
    all_ratios_to_analyze = []
    all_primary_assets_processed = []

    for primary_asset in primary_assets:
        # Remove primary asset from benchmarks if it's there to avoid self-comparison
        asset_benchmarks = [b for b in benchmarks if b != primary_asset]

        if not asset_benchmarks:
            logging.warning(f"No valid benchmark assets found for {primary_asset} after removing self-comparison")
            continue

        # Define the ratios to analyze for this primary asset
        for benchmark in asset_benchmarks:
            primary_pair = f"{primary_asset}/{quote_currency}"
            benchmark_pair = f"{benchmark}/{quote_currency}"
            ratio_name = f"{primary_asset}/{benchmark}"
            all_ratios_to_analyze.append((primary_pair, benchmark_pair, ratio_name, primary_asset))

        all_primary_assets_processed.append(primary_asset)

    if not all_ratios_to_analyze:
        logging.error("No valid ratios to analyze")
        return

    # Get all unique symbols needed
    all_symbols = set()
    for asset_a, asset_b, _, _ in all_ratios_to_analyze:
        all_symbols.add(asset_a)
        all_symbols.add(asset_b)

    all_symbols = list(all_symbols)

    # Prepare fetch parameters
    fetch_params = {
        'exchange_id': exchange_id,
        'symbols': all_symbols,
        'timeframe': timeframe,
        'force_refresh': True,  # Force fresh data to match TradingView
        'max_cache_age_days': 0
    }

    # Handle start date vs limit
    if start_date:
        fetch_params['since'] = start_date
        fetch_params['ensure_data_since'] = True
        fetch_params['limit'] = None  # Don't limit when using since
        date_range_info = f"from {start_date}"
        if end_date:
            date_range_info += f" to {end_date}"
        logging.info(f"Fetching data for {len(all_symbols)} symbols {date_range_info}")
    else:
        fetch_params['limit'] = limit
        date_range_info = f"latest {limit} candles"
        if end_date:
            date_range_info += f" (filtered to end at {end_date})"
        logging.info(f"Fetching {date_range_info} for {len(all_symbols)} symbols")

    # Fetch data for all symbols
    data_dict = fetch_ohlcv_data(**fetch_params)

    if not data_dict:
        logging.error("Failed to fetch data. Exiting.")
        return

    # Track scores for summary by primary asset
    total_scores_by_asset = {}
    # Track PGO values for debugging summary
    pgo_debug_data = {}
    # Track signals at vertical line date
    vertical_line_signals = {}

    # Process each ratio
    for asset_a, asset_b, ratio_name, primary_asset in all_ratios_to_analyze:
        if asset_a not in data_dict or asset_b not in data_dict:
            logging.warning(f"Skipping {ratio_name} - missing data for {asset_a} or {asset_b}")
            continue

        logging.info(f"Processing {ratio_name}...")

        # Create ratio OHLCV data
        ratio_df = create_ratio_ohlcv(data_dict[asset_a], data_dict[asset_b])

        if ratio_df.empty:
            logging.warning(f"No ratio data available for {ratio_name}")
            continue

        # Apply end date filtering if specified
        ratio_df = filter_data_by_end_date(ratio_df, end_date)

        # Create output directory structure
        safe_primary_asset = primary_asset.upper()
        output_dir = os.path.join("Asset_Ratio_Visualisations", safe_primary_asset, "images")
        os.makedirs(output_dir, exist_ok=True)

        # Generate output filename (without timestamp for overwriting)
        safe_ratio_name = ratio_name.replace('/', '_')
        output_file = os.path.join(output_dir, f"{safe_ratio_name}_pgo.png")

        # Create visualization
        pgo_signal = plot_ratio_pgo(
            ratio_df=ratio_df,
            ratio_name=ratio_name,
            pgo_length=pgo_length,
            upper_threshold=upper_threshold,
            lower_threshold=lower_threshold,
            output_file=output_file,
            show_score=show_score,
            end_date=end_date,
            vertical_line_at=vertical_line_at
        )

        # Log signal information
        log_ratio_signal_transitions(ratio_df, pgo_signal, ratio_name, pgo_length)

        # Track current signal score (1 for long, 0 for not long)
        current_score = 1 if len(pgo_signal) > 0 and pgo_signal.iloc[-1] == 1 else 0

        # Calculate PGO for debugging data
        pgo_values = calculate_pgo(ratio_df, length=pgo_length)
        last_pgo = pgo_values.iloc[-1] if len(pgo_values) > 0 else 0.0

        # Initialize primary asset tracking if not exists
        if primary_asset not in total_scores_by_asset:
            total_scores_by_asset[primary_asset] = {}
            pgo_debug_data[primary_asset] = {}

        total_scores_by_asset[primary_asset][ratio_name] = current_score
        pgo_debug_data[primary_asset][ratio_name] = {
            'last_pgo': last_pgo,
            'score': current_score,
            'ratio_value': ratio_df['close'].iloc[-1] if not ratio_df.empty else 0.0
        }

        # Collect signal at vertical line date if specified
        if vertical_line_at:
            signal_info = get_signal_at_date(ratio_df, pgo_signal, vertical_line_at, pgo_length)
            if primary_asset not in vertical_line_signals:
                vertical_line_signals[primary_asset] = {}
            vertical_line_signals[primary_asset][ratio_name] = signal_info

    # Print total scores summary
    print(f"\n" + "="*80)
    print("RATIO CURRENT SIGNAL SCORES SUMMARY")
    print("="*80)

    total_ratios_processed = 0
    total_long_signals_all = 0

    for primary_asset, scores in total_scores_by_asset.items():
        print(f"\n{primary_asset} RATIOS:")
        print("-" * 40)

        total_long_signals = sum(scores.values())
        total_ratios = len(scores)
        total_ratios_processed += total_ratios
        total_long_signals_all += total_long_signals

        print(f"Long signals: {total_long_signals} out of {total_ratios} ratios ({(total_long_signals/total_ratios*100):.1f}%)")
        print("Individual scores (1=Long, 0=Not Long):")
        for ratio_name, score in scores.items():
            print(f"  {ratio_name}: {score}")

    # Overall summary
    if len(total_scores_by_asset) > 1:
        print(f"\nOVERALL SUMMARY:")
        print("-" * 40)
        print(f"Total long signals: {total_long_signals_all} out of {total_ratios_processed} ratios")
        print(f"Overall long signal percentage: {(total_long_signals_all/total_ratios_processed*100):.1f}%")

    # Debugging summary table with all last PGO values
    print(f"\n" + "="*100)
    print("🔍 DEBUGGING SUMMARY - LAST PGO VALUES FOR ALL RATIOS")
    print("="*100)
    print(f"{'Ratio':<15} {'Last PGO':<12} {'Score':<6} {'Ratio Value':<12} {'Status':<8}")
    print("-" * 100)

    for primary_asset, ratios in pgo_debug_data.items():
        if len(pgo_debug_data) > 1:
            print(f"\n{primary_asset} RATIOS:")
            print("-" * 50)

        # Sort ratios by PGO value for easier analysis
        sorted_ratios = sorted(ratios.items(), key=lambda x: x[1]['last_pgo'], reverse=True)

        for ratio_name, data in sorted_ratios:
            last_pgo = data['last_pgo']
            score = data['score']
            ratio_value = data['ratio_value']
            status = "LONG" if score == 1 else "NEUTRAL"
            status_emoji = "📈" if score == 1 else "⚪"

            print(f"{ratio_name:<15} {last_pgo:+8.6f}    {score:<6} {ratio_value:<12.6f} {status_emoji} {status}")

    # Vertical line signals summary
    if vertical_line_at and vertical_line_signals:
        print(f"\n" + "="*100)
        print(f"📍 SIGNALS AT VERTICAL LINE DATE: {vertical_line_at}")
        print("="*100)
        print(f"{'Ratio':<15} {'Actual Date':<12} {'PGO Value':<12} {'Signal':<8} {'Ratio Value':<12} {'Status':<8}")
        print("-" * 100)

        # Count signals for summary
        total_long_at_date = 0
        total_ratios_at_date = 0

        for primary_asset, ratios in vertical_line_signals.items():
            if len(vertical_line_signals) > 1:
                print(f"\n{primary_asset} RATIOS:")
                print("-" * 50)

            # Sort ratios by PGO value for easier analysis
            sorted_ratios = sorted(ratios.items(), key=lambda x: x[1]['pgo_value'], reverse=True)

            for ratio_name, signal_info in sorted_ratios:
                actual_date = signal_info['actual_date']
                pgo_value = signal_info['pgo_value']
                signal = signal_info['signal']
                ratio_value = signal_info['ratio_value']
                signal_text = signal_info['signal_text']
                status_emoji = "📈" if signal == 1 else "📉" if signal == -1 else "⚪"

                print(f"{ratio_name:<15} {actual_date:<12} {pgo_value:+8.6f}    {signal:<8} {ratio_value:<12.6f} {status_emoji} {signal_text}")

                if signal == 1:
                    total_long_at_date += 1
                total_ratios_at_date += 1

        # Summary for the vertical line date
        if total_ratios_at_date > 0:
            print(f"\nSUMMARY FOR {vertical_line_at}:")
            print("-" * 40)
            print(f"Long signals: {total_long_at_date} out of {total_ratios_at_date} ratios ({(total_long_at_date/total_ratios_at_date*100):.1f}%)")

    print(f"\n" + "="*80)
    print("RATIO PGO ANALYSIS COMPLETE")
    print("="*80)
    print(f"Processed {len(all_primary_assets_processed)} primary assets")
    print(f"Generated visualizations for {len(all_ratios_to_analyze)} ratios")
    print("Files saved in Asset_Ratio_Visualisations/{Asset}/images/ directories")
    print("You can now cross-check these with TradingView!")

if __name__ == "__main__":
    main()
