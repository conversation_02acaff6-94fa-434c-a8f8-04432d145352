#!/usr/bin/env python3
"""
Debug script to trace momentum logic on specific violation dates
"""
import logging
import sys
import pandas as pd

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# Add current directory to path
sys.path.append('.')

from src.momentum_scoring import find_top_n_assets_with_momentum

def debug_momentum_violation():
    """Debug the specific momentum violation on 2024-01-06"""
    
    print("=== DEBUGGING MOMENTUM VIOLATION ON 2024-01-06 ===\n")
    
    # The exact scenario from the violation
    current_scores = {'ETH/USDT': 1.0, 'BTC/USDT': 2.0, 'SOL/USDT': 3.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0}
    previous_scores = {'ETH/USDT': 1.0, 'BTC/USDT': 2.0, 'SOL/USDT': 4.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0}
    
    print(f"Current scores:  {current_scores}")
    print(f"Previous scores: {previous_scores}")
    print()
    
    # Test our momentum logic
    print("--- Testing momentum logic ---")
    result = find_top_n_assets_with_momentum(
        current_scores=current_scores,
        previous_scores=previous_scores,
        n=1,
        mtpi_signal=1
    )
    
    print(f"Momentum logic result: {result}")
    print(f"Expected: ['SUI/USDT'] (better momentum: 0 vs -1)")
    print()
    
    # Manual momentum calculation
    print("--- Manual momentum analysis ---")
    max_score = max(current_scores.values())
    tied_assets = [asset for asset, score in current_scores.items() if score == max_score]
    
    print(f"Max score: {max_score}")
    print(f"Tied assets: {tied_assets}")
    
    momentum_data = {}
    for asset in tied_assets:
        prev_score = previous_scores.get(asset, 0)
        current_score = current_scores[asset]
        momentum = current_score - prev_score
        momentum_data[asset] = {
            'prev': prev_score,
            'current': current_score,
            'momentum': momentum
        }
        print(f"{asset}: {prev_score} -> {current_score} (Δ={momentum:+.1f})")
    
    # Find best momentum
    best_momentum = max(data['momentum'] for data in momentum_data.values())
    best_momentum_assets = [asset for asset, data in momentum_data.items() 
                           if data['momentum'] == best_momentum]
    
    print(f"\nBest momentum: {best_momentum:+.1f}")
    print(f"Assets with best momentum: {best_momentum_assets}")
    
    # Check if our function matches manual calculation
    if result == best_momentum_assets:
        print("✓ Momentum function working correctly!")
    else:
        print(f"✗ Momentum function mismatch! Expected {best_momentum_assets}, got {result}")
    
    return result == best_momentum_assets

def check_csv_data():
    """Check what's actually in the CSV for this date"""
    print("\n=== CHECKING CSV DATA ===\n")
    
    try:
        df = pd.read_csv('allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv')
        
        # Find the row for 2024-01-06
        target_date = '2024-01-06 00:00:00+00:00'
        row = df[df['date'] == target_date]
        
        if not row.empty:
            row = row.iloc[0]
            print(f"Date: {row['date']}")
            print(f"Current holdings: {row['current_holdings']}")
            print(f"Scores: {row['scores']}")
            print(f"MTPI signal: {row['mtpi_signal']}")
            
            # Check previous day too
            prev_date = '2024-01-05 00:00:00+00:00'
            prev_row = df[df['date'] == prev_date]
            if not prev_row.empty:
                prev_row = prev_row.iloc[0]
                print(f"\nPrevious day ({prev_date}):")
                print(f"Current holdings: {prev_row['current_holdings']}")
                print(f"Scores: {prev_row['scores']}")
        else:
            print(f"Date {target_date} not found in CSV")
            
    except Exception as e:
        print(f"Error reading CSV: {e}")

if __name__ == "__main__":
    success = debug_momentum_violation()
    check_csv_data()
    
    if success:
        print("\n✓ Momentum logic is working correctly")
        print("The issue might be elsewhere in the system")
    else:
        print("\n✗ Momentum logic has issues")
