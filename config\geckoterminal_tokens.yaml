# GeckoTerminal Tokens Configuration
#
# This file contains the configuration for tokens to be fetched from GeckoTerminal API.
# Each token entry should include:
#   - network: The blockchain network (e.g., "solana", "eth", "bsc")
#   - token_address: The token's contract address on the blockchain
#   - symbol: The trading symbol to use (e.g., "BONK/USDT")
#   - name: A human-readable name for the token (optional)
#   - color: Hex color code for charts (optional)
#
# Example:
# - network: solana
#   token_address: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263
#   symbol: BONK/USDT
#   name: Bonk
#   color: "#FFD700"

tokens:
  # Solana memecoins
  - network: solana
    token_address: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263
    symbol: BONK/USDT
    name: Bonk
    color: "#FFD700"

  - network: solana
    token_address: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU
    symbol: SAMO/USDT
    name: Samoyedcoin
    color: "#FF5733"

  - network: solana
    token_address: BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump
    symbol: AUTISM/USDT
    name: Autism
    color: "#9370DB"

  - network: solana
    token_address: 4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R
    symbol: RAY/USDT
    name: Raydium
    color: "#3366CC"

  - network: solana
    token_address: EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v
    symbol: USDC/USDT
    name: USD Coin (Solana)
    color: "#2775CA"

  # Ethereum memecoins
  - network: eth
    token_address: ******************************************
    symbol: SHIB/USDT
    name: Shiba Inu
    color: "#E94B3C"

  - network: eth
    token_address: ******************************************
    symbol: PEPE/USDT
    name: Pepe
    color: "#90EE90"

  - network: eth
    token_address: ******************************************
    symbol: WIF/USDT
    name: Dogwifhat (ETH)
    color: "#FF9900"

  # BSC memecoins
  - network: bsc
    token_address: ******************************************
    symbol: WBNB/USDT
    name: Wrapped BNB
    color: "#F3BA2F"

  - network: bsc
    token_address: ******************************************
    symbol: FLOKI/USDT
    name: Floki Inu
    color: "#FF6B6B"

  # Additional memecoins from settings_memecoins.yaml
  - network: solana
    token_address: A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump
    symbol: FWOG/USDT
    name: FWOG
    color: "#00CC99"

  - network: solana
    token_address: EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm
    symbol: WIF/USDT
    name: Dogwifhat
    color: "#FF6600"

  - network: solana
    token_address: 63LfDmNb3MQ8mw9MtZ2To9bEA2M71kZUUGq5tiJxcqj9
    symbol: GIGA/USDT
    name: GIGA
    color: "#9932CC"

  - network: base
    token_address: "******************************************"
    symbol: TOSHI/USDT
    name: Toshi
    color: "#FF9966"

  - network: bsc
    token_address: "0x8263cd1601fe73c066bf49cc09841f35348e3be0"
    symbol: ALTURA/USDT
    name: Altura
    color: "#3399FF"

  - network: solana
    token_address: 5SVG3T9CNQsm2kEwzbRq6hASqh1oGfjqTtLXYUibpump
    symbol: SIGMA/USDT
    name: SIGMA
    color: "#FF4500"

  - network: solana
    token_address: 8Ki8DpuWNxu9VsS3kQbarsCWMcFGWkzzA8pUPto9zBd5
    symbol: LOCKIN/USDT
    name: LOCKIN
    color: "#32CD32"

  - network: solana
    token_address: J3NKxxXZcnNiMjKw9hYb2K4LUxgwB6t1FtPtQVsv3KFr
    symbol: SPX/USDT
    name: SPX
    color: "#FF1493"

  - network: base
    token_address: "******************************************"
    symbol: SKI/USDT
    name: SKI
    color: "#87CEEB"

  - network: solana
    token_address: Df6yfrKC8kZE3KNkrHERKzAetSxbrWeniQfyJY4Jpump
    symbol: CHILLGUY/USDT
    name: CHILLGUY
    color: "#20B2AA"

  - network: eth
    token_address: "******************************************"
    symbol: APU/USDT
    name: APU
    color: "#FFD700"

  - network: solana
    token_address: 9WPTUkh8fKuCnepRWoPYLH3aK9gSjPHFDenBq2X1Czdp
    symbol: SELFIE/USDT
    name: SELFIE
    color: "#FF69B4"

  - network: base
    token_address: "******************************************"
    symbol: ALIENBASE/USDT
    name: ALIENBASE
    color: "#00FF7F"

  - network: eth
    token_address: "******************************************"
    symbol: DOGEETH/USDT
    name: DOGEETH
    color: "#DAA520"

  - network: solana
    token_address: 5mbK36SZ7J19An8jFochhQS4of8g6BwUjbeCSxBSoWdp
    symbol: MICHI/USDT
    name: MICHI
    color: "#FF6347"

  - network: solana
    token_address: 2JcXacFwt9mVAwBQ5nZkYwCyXQkRcdsYrDXn6hj22SbP
    symbol: MINI/USDT
    name: MINI
    color: "#4169E1"

  - network: solana
    token_address: GtDZKAqvMZMnti46ZewMiXCa4oXF4bZxwQPoKzXPFxZn
    symbol: NUB/USDT
    name: NUB
    color: "#DC143C"

  - network: solana
    token_address: 7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr
    symbol: POPCAT/USDT
    name: POPCAT
    color: "#FF8C00"

  - network: solana
    token_address: ********************************************
    symbol: GIKO/USDT
    name: GIKO
    color: "#9370DB"

  - network: solana
    token_address: 8WnQQRbuEZ3CCDbH5MCVioBbw6o75NKANq9WdPhBDsWo
    symbol: COBY/USDT
    name: COBY
    color: "#00CED1"

  - network: solana
    token_address: DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2
    symbol: AURA/USDT
    name: AURA
    color: "#8A2BE2"

  - network: base
    token_address: "0x937a1cfaf0a3d9f5dc4d0927f72ee5e3e5f82a00"
    symbol: COCORO/USDT
    name: COCORO
    color: "#FF1493"

  - network: solana
    token_address: DPaQfq5sFnoqw2Sh9WMmmASFL9LNu6RdtDqwE1tab2tB
    symbol: SKIBIDI/USDT
    name: SKIBIDI
    color: "#00FF00"

  - network: solana
    token_address: 4YK1njyeCkBuXG6phNtidJWKCbBhB659iwGkUJx98P5Z
    symbol: DOLAN/USDT
    name: DOLAN
    color: "#FF4500"

  - network: solana
    token_address: 69kdRLyP5DTRkpHraaSZAQbWmAwzF9guKjZfzMXzcbAs
    symbol: USA/USDT
    name: USA
    color: "#B22222"

  - network: eth
    token_address: "******************************************"
    symbol: EPIK/USDT
    name: EPIK
    color: "#FF6B6B"

# Settings for token fetching
settings:
  use_pagination: true
  max_pages_per_token: 2
  page_delay_seconds: 1.0
  default_timeframe: 1d
  cache_enabled: true
  cache_max_age_days: 1
