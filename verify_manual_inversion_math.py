#!/usr/bin/env python3
"""
Verify the mathematical properties of manual inversion scoring.

This script checks:
1. Whether ties are mathematically possible with manual inversion
2. Whether the current implementation actually uses manual inversion
3. Whether the total points distributed matches expected values
"""

import sys
import os
import pandas as pd
import logging
from typing import Dict, List
from collections import Counter

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_manual_inversion_math():
    """Analyze the mathematical properties of manual inversion."""
    
    print("=" * 80)
    print("MATHEMATICAL ANALYSIS OF MANUAL INVERSION")
    print("=" * 80)
    
    # Read the CSV file
    csv_file = "allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-21.csv"
    
    if not os.path.exists(csv_file):
        print(f"❌ CSV file not found: {csv_file}")
        return
    
    df = pd.read_csv(csv_file)
    print(f"✅ Loaded CSV with {len(df)} rows")
    
    # Analyze a few specific dates with ties
    tie_examples = [
        "2023-10-25",  # SOL/LINK/PEPE all have 12.0
        "2023-11-05",  # SUI/XRP both have 5.0
        "2023-11-10",  # ETH/XRP/DOT all have 5.0
    ]
    
    for date_str in tie_examples:
        print(f"\n" + "=" * 60)
        print(f"ANALYZING DATE: {date_str}")
        print("=" * 60)
        
        # Find the row for this date
        matching_rows = df[df['date'].str.contains(date_str)]
        if matching_rows.empty:
            print(f"❌ No data found for {date_str}")
            continue
            
        row = matching_rows.iloc[0]
        scores_str = row['scores']
        
        # Parse the scores dictionary
        try:
            scores_dict = eval(scores_str)
            print(f"Scores: {scores_dict}")
            
            # Analyze mathematical properties
            analyze_scores_math(scores_dict, date_str)
            
        except Exception as e:
            print(f"❌ Error parsing scores: {e}")
    
    print(f"\n" + "=" * 80)

def analyze_scores_math(scores: Dict[str, float], date: str):
    """Analyze the mathematical properties of a set of scores."""
    
    assets = list(scores.keys())
    n_assets = len(assets)
    
    print(f"Number of assets: {n_assets}")
    print(f"Expected total points with manual inversion: {n_assets * (n_assets - 1) // 2}")
    
    # Calculate actual total points
    actual_total = sum(scores.values())
    expected_total = n_assets * (n_assets - 1) // 2
    
    print(f"Actual total points: {actual_total}")
    print(f"Expected total points: {expected_total}")
    
    if actual_total == expected_total:
        print("✅ Total points match manual inversion expectation")
    else:
        print("❌ Total points DON'T match - manual inversion may not be working!")
        print(f"   Difference: {actual_total - expected_total}")
    
    # Check for ties
    score_counts = Counter(scores.values())
    ties = {score: count for score, count in score_counts.items() if count > 1}
    
    if ties:
        print(f"🔍 Ties found: {ties}")
        for score, count in ties.items():
            tied_assets = [asset for asset, s in scores.items() if s == score]
            print(f"   Score {score}: {tied_assets}")
        
        # Check if ties are mathematically reasonable
        print("\n📊 Mathematical analysis of ties:")
        max_possible_score = n_assets - 1
        min_possible_score = 0
        
        for score, count in ties.items():
            if score == max_possible_score:
                print(f"   ⚠️  {count} assets have MAXIMUM score ({score}) - unusual but possible")
            elif score == min_possible_score:
                print(f"   ⚠️  {count} assets have MINIMUM score ({score}) - unusual but possible")
            else:
                print(f"   ✅ {count} assets have mid-range score ({score}) - mathematically normal")
    else:
        print("✅ No ties found")
    
    # Verify score range
    min_score = min(scores.values())
    max_score = max(scores.values())
    
    print(f"\nScore range: {min_score} to {max_score}")
    print(f"Expected range with manual inversion: 0 to {n_assets - 1}")
    
    if min_score >= 0 and max_score <= n_assets - 1:
        print("✅ Score range is within expected bounds")
    else:
        print("❌ Score range is outside expected bounds!")
        if max_score > n_assets - 1:
            print(f"   Max score {max_score} > {n_assets - 1} suggests double-counting or other issues")

def test_manual_inversion_theory():
    """Test the theoretical properties of manual inversion with a small example."""
    
    print(f"\n" + "=" * 60)
    print("THEORETICAL TEST: 4 ASSETS")
    print("=" * 60)
    
    # Simulate manual inversion with 4 assets
    assets = ['A', 'B', 'C', 'D']
    n = len(assets)
    
    print(f"Assets: {assets}")
    print(f"Number of pairwise comparisons: {n * (n-1) // 2} = {n*(n-1)//2}")
    print(f"Total points to distribute: {n * (n-1) // 2}")
    
    # Example scenario where ties occur
    print(f"\nExample scenario with ties:")
    print(f"A beats B, C, D → A gets 3 points")
    print(f"B beats C, D → B gets 2 points") 
    print(f"C beats D → C gets 1 point")
    print(f"D beats nobody → D gets 0 points")
    print(f"Total: 3+2+1+0 = 6 points ✅")
    
    print(f"\nAnother scenario with ties:")
    print(f"A beats B, C → A gets 2 points")
    print(f"B beats D → B gets 1 point")
    print(f"C beats D → C gets 1 point") 
    print(f"D beats A → D gets 1 point")
    print(f"Total: 2+1+1+1 = 5 points ❌ (Should be 6!)")
    print(f"This scenario is impossible with manual inversion!")
    
    print(f"\nCorrect scenario with ties:")
    print(f"A beats B, C → A gets 2 points")
    print(f"B beats D → B gets 1 point")
    print(f"C beats D → C gets 1 point")
    print(f"D beats A, B → Wait, this creates a cycle!")
    
    print(f"\nActual valid scenario with ties:")
    print(f"A beats B → A gets 1 point, B gets 0 from this")
    print(f"A beats C → A gets 1 point, C gets 0 from this") 
    print(f"A beats D → A gets 1 point, D gets 0 from this")
    print(f"B beats C → B gets 1 point, C gets 0 from this")
    print(f"B beats D → B gets 1 point, D gets 0 from this")
    print(f"C beats D → C gets 1 point, D gets 0 from this")
    print(f"Final: A=3, B=2, C=1, D=0. Total = 6 ✅")
    print(f"No ties in this case!")
    
    print(f"\nFor ties to occur naturally:")
    print(f"We need circular relationships like A>B>C>A")
    print(f"But manual inversion calculates each pair only once!")
    print(f"So ties should be RARE but not impossible.")

if __name__ == "__main__":
    analyze_manual_inversion_math()
    test_manual_inversion_theory()
