#!/usr/bin/env python3
"""
Beta Detection System - Example Usage
Demonstrates various ways to use the beta detection system.
"""

import sys
import os
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Beta_detection.beta_runner import BetaDetectionRunner
from Beta_detection.beta_config_manager import BetaConfigManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def example_1_basic_analysis():
    """Example 1: Basic beta analysis with default settings."""
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic Beta Analysis")
    print("="*60)
    
    # Initialize runner with default configuration
    runner = BetaDetectionRunner()
    
    # Run analysis with a small subset for demonstration
    test_symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'DOGE/USDT', 'LINK/USDT']
    
    print(f"Analyzing {len(test_symbols)} assets...")
    
    try:
        results = runner.run_beta_analysis(
            symbols=test_symbols,
            analysis_mode='quick',  # Use quick mode for faster demo
            since='2024-01-01'
        )
        
        if results:
            print(f"✅ Successfully calculated beta for {len(results)} assets")
            
            # Show some results
            for symbol, data in list(results.items())[:3]:
                print(f"   {symbol}: Beta = {data['beta']:.3f}")
        else:
            print("❌ No results generated")
            
    except Exception as e:
        print(f"❌ Error in basic analysis: {e}")

def example_2_defi_focus():
    """Example 2: Analyze DeFi tokens specifically."""
    print("\n" + "="*60)
    print("EXAMPLE 2: DeFi Token Beta Analysis")
    print("="*60)
    
    runner = BetaDetectionRunner()
    
    try:
        results = runner.run_beta_analysis(
            symbol_group='defi_focus',  # Use predefined DeFi group
            analysis_mode='standard',
            show_previous_comparison=False  # Skip comparison for demo
        )
        
        if results:
            print(f"✅ DeFi analysis completed for {len(results)} tokens")
            
            # Find highest and lowest beta DeFi tokens
            sorted_results = sorted(results.items(), key=lambda x: x[1]['beta'], reverse=True)
            
            print(f"\n🔥 Highest Beta DeFi Token:")
            if sorted_results:
                symbol, data = sorted_results[0]
                print(f"   {symbol}: {data['beta']:.3f} (most volatile)")
            
            print(f"\n🛡️  Lowest Beta DeFi Token:")
            if sorted_results:
                symbol, data = sorted_results[-1]
                print(f"   {symbol}: {data['beta']:.3f} (least volatile)")
        
    except Exception as e:
        print(f"❌ Error in DeFi analysis: {e}")

def example_3_custom_configuration():
    """Example 3: Using custom configuration."""
    print("\n" + "="*60)
    print("EXAMPLE 3: Custom Configuration")
    print("="*60)
    
    # Create custom configuration
    config = BetaConfigManager()
    
    # Show current configuration
    print("Current Configuration:")
    print(f"  Measurement Length: {config.get_measurement_length()} days")
    print(f"  Benchmark: {config.get_benchmark_symbol()}")
    print(f"  ROC Threshold: {config.get_roc_threshold()}")
    
    # Create runner with custom measurement length
    runner = BetaDetectionRunner(measurement_length=50)  # Shorter period
    
    # Custom symbol list
    custom_symbols = ['BTC/USDT', 'ETH/USDT', 'SOL/USDT', 'AVAX/USDT']
    
    try:
        results = runner.run_beta_analysis(
            symbols=custom_symbols,
            analysis_mode='quick',
            save_results=False,  # Don't save for demo
            since='2024-06-01'
        )
        
        if results:
            print(f"✅ Custom analysis completed with 50-day measurement period")
            print(f"📊 Results for {len(results)} assets")
        
    except Exception as e:
        print(f"❌ Error in custom analysis: {e}")

def example_4_configuration_management():
    """Example 4: Configuration management features."""
    print("\n" + "="*60)
    print("EXAMPLE 4: Configuration Management")
    print("="*60)
    
    config = BetaConfigManager()
    
    # Show available symbol groups
    print("📋 Available Symbol Groups:")
    groups = ['default_symbols', 'defi_focus', 'meme_focus', 'layer1_focus']
    
    for group in groups:
        if group == 'default_symbols':
            symbols = config.get_default_symbols()
        else:
            symbols = config.get_symbol_group(group)
        
        if symbols:
            print(f"  {group}: {len(symbols)} assets")
            print(f"    Sample: {', '.join(symbols[:3])}...")
    
    # Show analysis modes
    print(f"\n⚙️  Analysis Modes:")
    for mode in ['quick', 'standard', 'comprehensive']:
        mode_config = config.get_analysis_mode_config(mode)
        print(f"  {mode}: {mode_config.get('measurement_length', 'N/A')} days, "
              f"{mode_config.get('max_pages', 'N/A')} pages")
    
    # Validate configuration
    issues = config.validate_config()
    if issues:
        print(f"\n⚠️  Configuration Issues:")
        for issue in issues:
            print(f"    - {issue}")
    else:
        print(f"\n✅ Configuration is valid")

def example_5_programmatic_usage():
    """Example 5: Programmatic usage without CLI."""
    print("\n" + "="*60)
    print("EXAMPLE 5: Programmatic Usage")
    print("="*60)
    
    from Beta_detection.beta_calculator import BetaCalculator
    from Beta_detection.beta_table_display import BetaTableDisplay
    
    # Create sample data for demonstration
    import pandas as pd
    import numpy as np
    
    print("Creating sample data for demonstration...")
    
    # Generate sample price data
    dates = pd.date_range('2024-01-01', periods=120, freq='D')
    
    # Sample data dictionary
    sample_data = {}
    
    # BTC (benchmark)
    btc_prices = 50000 + np.cumsum(np.random.randn(120) * 1000)
    sample_data['BTC/USDT'] = pd.DataFrame({
        'close': btc_prices,
        'open': btc_prices * 0.99,
        'high': btc_prices * 1.02,
        'low': btc_prices * 0.98,
        'volume': np.random.randint(1000, 10000, 120)
    }, index=dates)
    
    # Sample altcoin (more volatile)
    alt_prices = 100 + np.cumsum(np.random.randn(120) * 8)
    sample_data['SAMPLE/USDT'] = pd.DataFrame({
        'close': alt_prices,
        'open': alt_prices * 0.99,
        'high': alt_prices * 1.05,
        'low': alt_prices * 0.95,
        'volume': np.random.randint(500, 5000, 120)
    }, index=dates)
    
    # Calculate beta
    calculator = BetaCalculator(measurement_length=100)
    beta_results = calculator.calculate_multiple_betas(sample_data)
    
    if beta_results:
        print(f"✅ Calculated beta for sample data:")
        for symbol, data in beta_results.items():
            print(f"   {symbol}: {data['beta']:.3f}")
        
        # Display results
        display = BetaTableDisplay()
        display.create_beta_table(beta_results)
    else:
        print("❌ No beta results calculated")

def main():
    """Run all examples."""
    print("🧪 Beta Detection System - Usage Examples")
    print("="*60)
    print("This script demonstrates various ways to use the beta detection system.")
    print("Note: Some examples may fail if market data is not available.")
    
    examples = [
        ("Basic Analysis", example_1_basic_analysis),
        ("DeFi Focus", example_2_defi_focus),
        ("Custom Configuration", example_3_custom_configuration),
        ("Configuration Management", example_4_configuration_management),
        ("Programmatic Usage", example_5_programmatic_usage),
    ]
    
    for name, example_func in examples:
        print(f"\n🔍 Running {name} Example...")
        try:
            example_func()
        except Exception as e:
            print(f"❌ {name} example failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*60)
    print("📋 Examples completed!")
    print("="*60)
    print("\nTo run the full beta analysis system:")
    print("  python Beta_detection/run_beta_analysis.py")
    print("\nTo see all available options:")
    print("  python Beta_detection/run_beta_analysis.py --help")

if __name__ == "__main__":
    main()
