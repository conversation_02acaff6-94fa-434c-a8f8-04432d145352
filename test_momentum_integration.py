#!/usr/bin/env python3
"""
Test script to verify momentum logic integration
"""
import logging
import sys
import os

# Set up logging to see debug messages
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# Add current directory to path
sys.path.append('.')

from main_program import AllocationTester

def test_momentum_integration():
    """Test if momentum logic is properly integrated"""
    
    print("=== TESTING MOMENTUM LOGIC INTEGRATION ===\n")
    
    # Create tester with momentum strategy
    tester = AllocationTester(
        analysis_start_date='2024-01-05',  # Start just before the violation
        analysis_end_date='2024-01-08',    # End just after
        tie_breaking_strategy='momentum',
        n_assets=1,
        selected_assets=['SOL/USDT', 'SUI/USDT'],
        use_cache=True
    )
    
    print(f"Tester created with tie_breaking_strategy: {tester.tie_breaking_strategy}")
    print(f"Selected assets: {tester.selected_assets}")
    print(f"Analysis period: {tester.analysis_start_date} to {tester.analysis_end_date}")
    
    try:
        # Fetch data
        print("\n--- Fetching data ---")
        tester.fetch_data()
        
        # Calculate scores
        print("\n--- Calculating scores ---")
        tester.calculate_scores()
        
        # Check if we have the expected scores for the test period
        print("\n--- Checking scores ---")
        if hasattr(tester, 'daily_scores_df') and tester.daily_scores_df is not None:
            print("Daily scores available:")
            for date in ['2024-01-05', '2024-01-06', '2024-01-07']:
                if date in tester.daily_scores_df.index:
                    scores = tester.daily_scores_df.loc[date].to_dict()
                    print(f"  {date}: {scores}")
                else:
                    print(f"  {date}: Not found in scores")
        else:
            print("No daily scores available")
            
        # Run strategy
        print("\n--- Running strategy ---")
        results = tester.run_strategy_with_detailed_logging()
        
        print("\n--- Results ---")
        if hasattr(tester, 'allocation_history') and tester.allocation_history:
            print("Allocation history:")
            for entry in tester.allocation_history[-5:]:  # Show last 5 entries
                date = entry.get('date', 'Unknown')
                holdings = entry.get('current_holdings', [])
                scores = entry.get('scores', {})
                print(f"  {date}: {holdings} | Scores: {scores}")
        else:
            print("No allocation history available")
            
        return True
        
    except Exception as e:
        print(f"Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_momentum_integration()
    if success:
        print("\n✓ Test completed successfully")
    else:
        print("\n✗ Test failed")
