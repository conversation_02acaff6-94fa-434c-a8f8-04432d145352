#!/usr/bin/env python3
"""
Test script to verify that background_service_memecoins.py works correctly with all approaches:
- manual_inversion vs independent calculation methods
- momentum vs incumbent tie-breaking strategies
"""

import os
import sys
import logging
import tempfile
import shutil
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Setup logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def create_test_memecoin_config(ratio_calculation='manual_inversion', tie_breaking_strategy='momentum'):
    """Create a temporary test configuration for memecoins."""
    config_content = f"""# Test configuration for memecoin background service
# This is a minimal configuration for testing purposes

settings:
  # Timeframe settings
  timeframe: "1d"
  mtpi_timeframe: "1d"

  # Asset selection
  n_assets: 1
  allocation_approach: "equal"

  # Signal generation settings
  use_mtpi_signal: true
  trend_method: "PGO For Loop"

  # Test parameters
  ratio_calculation: "{ratio_calculation}"
  tie_breaking_strategy: "{tie_breaking_strategy}"

  # MTPI PGO parameters
  mtpi_pgo_length: 35
  mtpi_upper_threshold: 1.1
  mtpi_lower_threshold: -0.58

  # Asset trend PGO parameters
  pgo_length: 35
  pgo_upper_threshold: 1.1
  pgo_lower_threshold: -0.58

  # Data settings
  analysis_start_date: "2023-10-20"
  force_refresh_cache: false
  use_cache: true
  initial_capital: 10000
  transaction_fee_rate: 0.001

# GeckoTerminal configuration for memecoin data
geckoterminal:
  enabled: true
  networks:
    - solana
  
  # Test tokens (small set for faster testing)
  tokens:
    solana:
      - address: "So11111111111111111111111111111111111111112"
        symbol: "SOL"
        name: "Wrapped SOL"
      - address: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        symbol: "USDC"
        name: "USD Coin"

  # Rate limiting
  rate_limit:
    requests_per_minute: 30
    burst_limit: 10

  # Cache settings
  cache:
    enabled: true
    ttl_minutes: 5
    max_entries: 1000

# Notification settings (disabled for testing)
notifications:
  enabled: false
"""
    
    # Create temporary config file
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
    temp_config.write(config_content)
    temp_config.close()
    
    return temp_config.name

def test_memecoin_service_approach(ratio_calculation, tie_breaking_strategy):
    """Test memecoin background service with specific approach."""
    print(f"\n{'='*60}")
    print(f"Testing Memecoins: ratio_calculation={ratio_calculation}, tie_breaking_strategy={tie_breaking_strategy}")
    print(f"{'='*60}")
    
    # Create test config
    config_path = create_test_memecoin_config(ratio_calculation, tie_breaking_strategy)
    
    try:
        # Import and test the memecoin background service
        from main_program import run_strategy_for_web
        
        print("Loading memecoin configuration...")
        from src.config_manager import load_config
        config = load_config(config_path)
        settings = config.get('settings', {})
        
        print(f"Configuration loaded:")
        print(f"  - ratio_calculation: {settings.get('ratio_calculation')}")
        print(f"  - tie_breaking_strategy: {settings.get('tie_breaking_strategy')}")
        print(f"  - timeframe: {settings.get('timeframe')}")
        
        print("\nRunning memecoin strategy...")
        
        # Prepare arguments similar to background_service_memecoins.py
        strategy_args = {
            'timeframe': settings.get('timeframe', '1d'),
            'mtpi_timeframe': settings.get('mtpi_timeframe', '1d'),
            'analysis_start_date': settings.get('analysis_start_date', '2023-10-20'),
            'n_assets': settings.get('n_assets', 1),
            'transaction_fee_rate': settings.get('transaction_fee_rate', 0.001),
            'selected_assets': [],  # No Binance assets for memecoin test
            'use_cache': settings.get('use_cache', True),
            'initial_capital': settings.get('initial_capital', 10000),
            'use_mtpi_signal': settings.get('use_mtpi_signal', True),
            'use_weighted_allocation': settings.get('allocation_approach') == 'weighted',
            'weights': settings.get('weights', []),
            'trend_method': settings.get('trend_method', 'PGO For Loop'),
            'force_refresh_cache': settings.get('force_refresh_cache', False),
            'ratio_calculation': settings.get('ratio_calculation', 'manual_inversion'),
            'tie_breaking_strategy': settings.get('tie_breaking_strategy', 'momentum'),
            'context': 'test_memecoin'
        }
        
        # Add GeckoTerminal tokens if available
        geckoterminal_config = config.get('geckoterminal', {})
        if geckoterminal_config.get('enabled', False):
            geckoterminal_assets = []
            tokens_config = geckoterminal_config.get('tokens', {})
            
            for network, tokens in tokens_config.items():
                for token in tokens:
                    geckoterminal_assets.append({
                        'network': network,
                        'address': token['address'],
                        'symbol': token['symbol'],
                        'name': token['name']
                    })
            
            strategy_args['geckoterminal_tokens'] = geckoterminal_assets
            print(f"  - geckoterminal_tokens: {len(geckoterminal_assets)} tokens")
        
        # Run the strategy exactly like background_service_memecoins.py does
        results = run_strategy_for_web(**strategy_args)
        
        if not results or 'error' in results:
            error_msg = results.get('error', 'Unknown error') if results else 'No results returned'
            print(f"✗ Test FAILED: {error_msg}")
            return False
        
        print("✓ Memecoin strategy execution completed successfully!")
        
        # Verify results structure
        expected_keys = ['strategy_equity', 'performance_metrics', 'success']
        missing_keys = [key for key in expected_keys if key not in results]
        
        if missing_keys:
            print(f"✗ Test FAILED: Missing result keys: {missing_keys}")
            return False
        
        print("✓ Results structure verified!")
        
        # Check if we have performance metrics
        if 'performance_metrics' in results:
            metrics = results['performance_metrics']
            if 'total_return' in metrics:
                print(f"  - Total return: {metrics['total_return']:.2%}")
            if 'latest_scores' in metrics:
                print(f"  - Latest scores: {metrics['latest_scores']}")
        
        print(f"✓ Test PASSED for {ratio_calculation}/{tie_breaking_strategy}")
        return True
        
    except Exception as e:
        print(f"✗ Test FAILED with exception: {e}")
        logging.error(f"Exception details:", exc_info=True)
        return False
        
    finally:
        # Clean up temporary config file
        try:
            os.unlink(config_path)
        except:
            pass

def main():
    """Main test function."""
    setup_logging()
    
    print("Testing background_service_memecoins.py with all approaches...")
    print(f"Test started at: {datetime.now()}")
    
    # Test all combinations
    test_cases = [
        ('manual_inversion', 'momentum'),
        ('manual_inversion', 'incumbent'),
        ('independent', 'momentum'),
        ('independent', 'incumbent')
    ]
    
    results = {}
    
    for ratio_calc, tie_breaking in test_cases:
        success = test_memecoin_service_approach(ratio_calc, tie_breaking)
        results[f"{ratio_calc}/{tie_breaking}"] = success
    
    # Summary
    print(f"\n{'='*60}")
    print("MEMECOIN SERVICE TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, success in results.items():
        status = "✓ PASSED" if success else "✗ FAILED"
        print(f"{test_name:30} {status}")
        if not success:
            all_passed = False
    
    print(f"\nOverall result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    print(f"Test completed at: {datetime.now()}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
