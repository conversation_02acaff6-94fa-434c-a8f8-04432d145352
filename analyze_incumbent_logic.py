#!/usr/bin/env python3
"""
Analyze incumbent tie-breaking logic from backtesting results.

This script examines the CSV output to verify that the incumbent approach
is working correctly: "Keep the current asset until a higher scorer surpasses it"

Similar to analyze_momentum_logic.py but focused on incumbent behavior.
"""

import sys
import os
import pandas as pd
import ast
from typing import Dict, List, Tuple, Optional
from collections import defaultdict

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def analyze_incumbent_logic():
    """Analyze incumbent logic from the CSV file."""
    
    print("=" * 80)
    print("ANALYZING INCUMBENT LOGIC")
    print("=" * 80)
    
    # Find the CSV file ending with 2023-10-20
    csv_files = [f for f in os.listdir('.') if f.endswith('2023-10-19.csv') and 'allocation_history' in f]
    
    if not csv_files:
        print("❌ No CSV file found ending with '2023-10-20.csv'")
        return
    
    csv_file = csv_files[0]
    print(f"📁 Analyzing file: {csv_file}")
    
    # Load the CSV
    try:
        df = pd.read_csv(csv_file)
        print(f"✅ Loaded {len(df)} rows of data")
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")
        return
    
    # Convert date column to datetime
    df['date'] = pd.to_datetime(df['date'])
    
    # Parse scores column
    df['parsed_scores'] = df['scores'].apply(safe_parse_scores)
    
    # Extract selected assets from top_assets column
    df['selected_asset'] = df['top_assets'].apply(extract_first_asset)
    
    print(f"📊 Date range: {df['date'].min().strftime('%Y-%m-%d')} to {df['date'].max().strftime('%Y-%m-%d')}")
    print(f"🎯 Strategy: incumbent tie-breaking with independent calculation")
    
    # Analyze incumbent behavior
    analyze_incumbent_behavior(df)
    
    # Look for specific incumbent violations
    find_incumbent_violations(df)
    
    # Analyze tie scenarios
    analyze_tie_scenarios(df)
    
    print("\n" + "=" * 80)

def safe_parse_scores(scores_str):
    """Safely parse the scores string."""
    try:
        if pd.isna(scores_str) or scores_str == '':
            return {}
        return ast.literal_eval(scores_str)
    except:
        return {}

def extract_first_asset(top_assets_str):
    """Extract the first asset from top_assets string."""
    try:
        if pd.isna(top_assets_str) or top_assets_str == '':
            return None
        # Parse the list string like "['SOL/USDT']"
        assets_list = ast.literal_eval(top_assets_str)
        return assets_list[0] if assets_list else None
    except:
        return None

def analyze_incumbent_behavior(df):
    """Analyze how well the incumbent logic is working."""
    
    print("\n" + "=" * 60)
    print("INCUMBENT BEHAVIOR ANALYSIS")
    print("=" * 60)
    
    incumbent_kept_count = 0
    incumbent_switched_count = 0
    incumbent_violations = []
    
    for i in range(1, len(df)):
        current_row = df.iloc[i]
        previous_row = df.iloc[i-1]
        
        current_scores = current_row['parsed_scores']
        previous_scores = previous_row['parsed_scores']
        current_asset = current_row['selected_asset']
        previous_asset = previous_row['selected_asset']
        
        if not current_scores or not previous_scores or not current_asset or not previous_asset:
            continue
        
        # Get the scores for current and previous assets
        current_asset_score = current_scores.get(current_asset, 0)
        previous_asset_score = current_scores.get(previous_asset, 0)
        
        if current_asset == previous_asset:
            # Asset was kept
            incumbent_kept_count += 1
        else:
            # Asset was switched
            incumbent_switched_count += 1
            
            # Check if this was a valid switch (new asset has higher score)
            if current_asset_score <= previous_asset_score:
                # This might be an incumbent violation
                violation = {
                    'date': current_row['date'],
                    'previous_asset': previous_asset,
                    'current_asset': current_asset,
                    'previous_asset_score': previous_asset_score,
                    'current_asset_score': current_asset_score,
                    'all_scores': current_scores
                }
                incumbent_violations.append(violation)
    
    print(f"📈 Incumbent kept: {incumbent_kept_count} times")
    print(f"🔄 Asset switched: {incumbent_switched_count} times")
    
    if incumbent_violations:
        print(f"\n⚠️  Found {len(incumbent_violations)} potential incumbent violations:")
        for i, violation in enumerate(incumbent_violations[:5], 1):  # Show first 5
            print(f"\n{i}. {violation['date'].strftime('%Y-%m-%d')}:")
            print(f"   Switched from {violation['previous_asset']} (score: {violation['previous_asset_score']})")
            print(f"   Switched to {violation['current_asset']} (score: {violation['current_asset_score']})")
            print(f"   ❌ New asset has LOWER/EQUAL score - potential violation!")
    else:
        print("✅ No incumbent violations found - all switches were to higher-scoring assets")

def find_incumbent_violations(df):
    """Find specific cases where incumbent logic should have kept the asset."""
    
    print("\n" + "=" * 60)
    print("DETAILED INCUMBENT VIOLATION ANALYSIS")
    print("=" * 60)
    
    violations = []
    
    for i in range(1, len(df)):
        current_row = df.iloc[i]
        previous_row = df.iloc[i-1]
        
        current_scores = current_row['parsed_scores']
        current_asset = current_row['selected_asset']
        previous_asset = previous_row['selected_asset']
        
        if not current_scores or not current_asset or not previous_asset:
            continue
        
        if current_asset != previous_asset:
            # Asset switched - check if incumbent should have been kept
            max_score = max(current_scores.values()) if current_scores else 0
            previous_asset_score = current_scores.get(previous_asset, 0)
            current_asset_score = current_scores.get(current_asset, 0)
            
            # Find all assets with max score
            max_score_assets = [asset for asset, score in current_scores.items() if score == max_score]
            
            # Check different violation scenarios
            violation_type = None
            
            if previous_asset_score == max_score and current_asset_score == max_score:
                # Both assets tied at max score - incumbent should have been kept
                violation_type = "TIE_AT_MAX_SCORE"
            elif previous_asset_score > current_asset_score:
                # Previous asset had higher score - should never switch
                violation_type = "HIGHER_SCORE_ABANDONED"
            elif previous_asset_score == current_asset_score and previous_asset_score < max_score:
                # Both assets tied but not at max - this might be acceptable
                violation_type = "TIE_BELOW_MAX_SCORE"
            
            if violation_type:
                violations.append({
                    'date': current_row['date'],
                    'type': violation_type,
                    'previous_asset': previous_asset,
                    'current_asset': current_asset,
                    'previous_score': previous_asset_score,
                    'current_score': current_asset_score,
                    'max_score': max_score,
                    'max_score_assets': max_score_assets,
                    'all_scores': current_scores
                })
    
    if violations:
        print(f"Found {len(violations)} potential violations:")
        
        # Group by violation type
        by_type = defaultdict(list)
        for v in violations:
            by_type[v['type']].append(v)
        
        for violation_type, type_violations in by_type.items():
            print(f"\n🚨 {violation_type}: {len(type_violations)} cases")
            
            for i, v in enumerate(type_violations[:3], 1):  # Show first 3 of each type
                print(f"\n   {i}. {v['date'].strftime('%Y-%m-%d')}:")
                print(f"      From: {v['previous_asset']} (score: {v['previous_score']})")
                print(f"      To: {v['current_asset']} (score: {v['current_score']})")
                print(f"      Max score: {v['max_score']} (assets: {v['max_score_assets']})")
                
                if violation_type == "TIE_AT_MAX_SCORE":
                    print(f"      ❌ VIOLATION: Both assets tied at max score, should keep incumbent!")
                elif violation_type == "HIGHER_SCORE_ABANDONED":
                    print(f"      ❌ VIOLATION: Abandoned higher-scoring asset!")
    else:
        print("✅ No incumbent violations found")

def analyze_tie_scenarios(df):
    """Analyze how ties are handled."""

    print("\n" + "=" * 60)
    print("TIE SCENARIO ANALYSIS")
    print("=" * 60)

    tie_cases = []
    max_score_tie_cases = []

    for i, row in df.iterrows():
        scores = row['parsed_scores']
        if not scores:
            continue

        # Find ties
        score_counts = defaultdict(list)
        for asset, score in scores.items():
            score_counts[score].append(asset)

        ties = {score: assets for score, assets in score_counts.items() if len(assets) > 1}

        if ties:
            max_score = max(scores.values())
            max_score_ties = ties.get(max_score, [])

            tie_cases.append({
                'date': row['date'],
                'selected_asset': row['selected_asset'],
                'all_ties': ties,
                'max_score_ties': max_score_ties,
                'max_score': max_score,
                'all_scores': scores
            })

            # Specifically track cases with ties at max score
            if max_score_ties:
                max_score_tie_cases.append({
                    'date': row['date'],
                    'selected_asset': row['selected_asset'],
                    'max_score_ties': max_score_ties,
                    'max_score': max_score,
                    'all_scores': scores
                })

    print(f"Found {len(tie_cases)} days with ties (any level)")
    print(f"Found {len(max_score_tie_cases)} days with ties at MAX SCORE level")

    if max_score_tie_cases:
        print(f"\n🎯 CRITICAL: Days with ties at MAX SCORE (incumbent logic should apply):")
        for i, case in enumerate(max_score_tie_cases[:10], 1):  # Show first 10
            print(f"\n{i}. {case['date'].strftime('%Y-%m-%d')}:")
            print(f"   Selected: {case['selected_asset']}")
            print(f"   Max score ties: {case['max_score_ties']} (score: {case['max_score']})")

            if case['selected_asset'] in case['max_score_ties']:
                print(f"   ✅ Selected asset is among max score ties - GOOD")
            else:
                print(f"   ❌ Selected asset NOT among max score ties - PROBLEM!")
                # Show what should have been selected
                print(f"   🔍 All scores: {case['all_scores']}")
    else:
        print("ℹ️  No ties found at max score level - this is unusual with independent calculation!")

    # Also show some regular tie examples
    if tie_cases and not max_score_tie_cases:
        print(f"\n📊 Examples of ties at non-max levels:")
        for i, case in enumerate(tie_cases[:3], 1):
            print(f"\n{i}. {case['date'].strftime('%Y-%m-%d')}:")
            print(f"   Selected: {case['selected_asset']} (score: {case['all_scores'].get(case['selected_asset'], 'N/A')})")
            print(f"   Max score: {case['max_score']}")
            print(f"   Ties at other levels: {case['all_ties']}")

if __name__ == "__main__":
    analyze_incumbent_logic()
