#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import ccxt
from src.config_manager import load_config

def debug_bitvavo_markets():
    """Debug what markets are actually available on Bitvavo."""
    
    print("=" * 80)
    print("DEBUGGING BITVAVO AVAILABLE MARKETS")
    print("=" * 80)
    
    try:
        # Initialize Bitvavo exchange
        exchange = ccxt.bitvavo({
            'enableRateLimit': True,
        })
        
        print("Loading Bitvavo markets...")
        markets = exchange.load_markets()
        
        print(f"Total markets available: {len(markets)}")
        
        # Check for BTC pairs
        print("\n1. BTC TRADING PAIRS:")
        print("-" * 40)
        btc_pairs = [symbol for symbol in markets.keys() if symbol.startswith('BTC/')]
        btc_pairs.sort()
        
        for pair in btc_pairs:
            print(f"  ✅ {pair}")
        
        # Check for EUR pairs
        print("\n2. EUR TRADING PAIRS:")
        print("-" * 40)
        eur_pairs = [symbol for symbol in markets.keys() if symbol.endswith('/EUR')]
        eur_pairs.sort()
        
        for pair in eur_pairs:
            print(f"  ✅ {pair}")
        
        # Check for TRX pairs
        print("\n3. TRX TRADING PAIRS:")
        print("-" * 40)
        trx_pairs = [symbol for symbol in markets.keys() if symbol.startswith('TRX/')]
        trx_pairs.sort()
        
        for pair in trx_pairs:
            print(f"  ✅ {pair}")
        
        # Check specific pairs from our configuration
        print("\n4. CHECKING CONFIGURATION PAIRS:")
        print("-" * 40)
        
        config = load_config("config/settings_bitvavo_eur.yaml")
        settings = config.get('settings', {})
        trading_assets = settings.get('assets', [])
        
        print("Configuration trading assets:")
        for asset in trading_assets:
            if asset in markets:
                print(f"  ✅ {asset} - AVAILABLE")
            else:
                print(f"  ❌ {asset} - NOT AVAILABLE")
        
        # Check if BTC/EUR specifically exists
        print("\n5. SPECIFIC CHECKS:")
        print("-" * 40)
        
        test_pairs = ['BTC/EUR', 'TRX/EUR', 'BTC/USDT', 'TRX/USDT']
        
        for pair in test_pairs:
            if pair in markets:
                print(f"  ✅ {pair} - AVAILABLE")
                
                # Try to get price
                try:
                    ticker = exchange.fetch_ticker(pair)
                    price = ticker['last']
                    print(f"      Current price: {price}")
                except Exception as e:
                    print(f"      ❌ Error getting price: {e}")
            else:
                print(f"  ❌ {pair} - NOT AVAILABLE")
        
        print("\n6. ALTERNATIVE BTC PAIRS:")
        print("-" * 40)
        
        # Look for alternative BTC pairs that might work
        alternative_btc = [symbol for symbol in markets.keys() 
                          if symbol.startswith('BTC/') and symbol != 'BTC/EUR']
        
        print("Available BTC pairs (alternatives to BTC/EUR):")
        for pair in alternative_btc:
            print(f"  ✅ {pair}")
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_bitvavo_markets()
