#!/usr/bin/env python3
"""
Debug script to trace the complete asset selection process
"""
import logging
import sys
import pandas as pd
import ast
from datetime import datetime

# Set up detailed logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

# Add current directory to path
sys.path.append('.')

def debug_asset_selection_process():
    """Debug the complete asset selection process for violation dates"""
    
    print("=== DEBUGGING COMPLETE ASSET SELECTION PROCESS ===\n")
    
    # Read the CSV to get the actual data
    df = pd.read_csv('allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv')
    df['scores_dict'] = df['scores'].apply(lambda x: ast.literal_eval(x) if pd.notna(x) else {})
    
    # Focus on the violation date
    violation_date = '2024-01-06 00:00:00+00:00'
    prev_date = '2024-01-05 00:00:00+00:00'
    
    # Get the rows
    current_row = df[df['date'] == violation_date]
    prev_row = df[df['date'] == prev_date]
    
    if current_row.empty or prev_row.empty:
        print(f"Could not find data for {violation_date} or {prev_date}")
        return
    
    current_row = current_row.iloc[0]
    prev_row = prev_row.iloc[0]
    
    current_scores = current_row['scores_dict']
    previous_scores = prev_row['scores_dict']
    current_holdings = current_row['current_holdings']
    mtpi_signal = current_row['mtpi_signal']
    
    print(f"Date: {violation_date}")
    print(f"Current scores: {current_scores}")
    print(f"Previous scores: {previous_scores}")
    print(f"Current holdings: {current_holdings}")
    print(f"MTPI signal: {mtpi_signal}")
    print()
    
    # Test our momentum logic directly
    print("--- Testing momentum logic directly ---")
    from src.momentum_scoring import find_top_n_assets_with_momentum
    
    momentum_result = find_top_n_assets_with_momentum(
        current_scores=current_scores,
        previous_scores=previous_scores,
        n=1,
        mtpi_signal=mtpi_signal
    )
    
    print(f"Direct momentum result: {momentum_result}")
    print()
    
    # Test the standard scoring logic
    print("--- Testing standard scoring logic ---")
    from src.scoring_n import find_top_n_assets_for_day
    
    standard_result = find_top_n_assets_for_day(
        scores=current_scores,
        n=1,
        mtpi_signal=mtpi_signal
    )
    
    print(f"Standard scoring result: {standard_result}")
    print()
    
    # Simulate the main program logic
    print("--- Simulating main program logic ---")
    
    # Check conditions for momentum logic
    tie_breaking_strategy = 'momentum'  # This is what we set
    use_weighted_allocation = False     # We're using equal allocation
    
    print(f"tie_breaking_strategy: {tie_breaking_strategy}")
    print(f"use_weighted_allocation: {use_weighted_allocation}")
    print(f"previous_scores available: {bool(previous_scores)}")
    
    # This is the exact logic from main_program.py
    if not use_weighted_allocation:
        # Use equal allocation
        if tie_breaking_strategy == 'momentum' and previous_scores:
            print("✓ Should use momentum-based selection")
            top_assets = find_top_n_assets_with_momentum(
                current_scores,
                previous_scores,
                n=1,
                mtpi_signal=mtpi_signal
            )
            print(f"Momentum selection result: {top_assets}")
        else:
            print("✗ Using standard selection instead")
            top_assets = find_top_n_assets_for_day(current_scores, n=1, mtpi_signal=mtpi_signal)
            print(f"Standard selection result: {top_assets}")
        
        # Check if incumbent logic would be applied
        if tie_breaking_strategy == 'incumbent':
            print("Would apply incumbent tie-breaking (but we're using momentum)")
        else:
            print("No additional tie-breaking applied")
    
    print()
    
    # Check what the CSV actually shows
    actual_selection = current_row['current_holdings']
    if isinstance(actual_selection, str) and actual_selection.startswith('['):
        actual_selection = ast.literal_eval(actual_selection)
    
    print(f"Actual CSV selection: {actual_selection}")
    print(f"Expected momentum selection: {momentum_result}")
    
    if actual_selection == momentum_result:
        print("✓ CSV matches momentum logic!")
    else:
        print("✗ CSV does NOT match momentum logic!")
        print("This suggests momentum logic is not being used or is being overridden")

def check_main_program_conditions():
    """Check if there are any conditions that might prevent momentum logic from being used"""
    
    print("\n=== CHECKING MAIN PROGRAM CONDITIONS ===\n")
    
    # Import and create a tester to check the actual conditions
    from main_program import AllocationTester
    
    tester = AllocationTester(
        analysis_start_date='2024-01-05',
        analysis_end_date='2024-01-07',
        tie_breaking_strategy='momentum',
        n_assets=1,
        selected_assets=['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT'],
        use_cache=True
    )
    
    print(f"Tester tie_breaking_strategy: {tester.tie_breaking_strategy}")
    print(f"Tester use_weighted_allocation: {tester.use_weighted_allocation}")
    print(f"Tester n_assets: {tester.n_assets}")
    
    # Check if there are any other settings that might affect this
    print(f"Tester selected_assets: {tester.selected_assets}")

if __name__ == "__main__":
    debug_asset_selection_process()
    check_main_program_conditions()
