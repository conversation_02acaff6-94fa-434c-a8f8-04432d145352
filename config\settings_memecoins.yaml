# Memecoin Strategy Configuration
# This configuration is specifically for the GeckoTerminal memecoin signal strategy

strategy:
  name: "Memecoin Signal Strategy"
  description: "Signal-only strategy for GeckoTerminal memecoins"
  version: "1.0.0"

settings:
  # Timeframe settings
  timeframe: "1d"
  mtpi_timeframe: "12h"

  # Asset selection
  n_assets: 1
  allocation_approach: "equal"  # Options: "equal", "weighted"

  # Signal generation settings
  use_mtpi_signal: true
  trend_method: "PGO For Loop"

  # Ratio calculation method for asset scoring
  ratio_calculation: "manual_inversion"  # Options: "manual_inversion" or "independent"

  # MTPI PGO parameters (matching Binance strategy)
  mtpi_pgo_length: 35
  mtpi_upper_threshold: 1.1
  mtpi_lower_threshold: -0.58

  # Asset trend PGO parameters (for individual asset scoring)
  pgo_length: 35
  pgo_upper_threshold: 1.1
  pgo_lower_threshold: -0.58

  # Data settings
  analysis_start_date: "2025-02-10"
  force_refresh_cache: false
  use_cache: true

  # Direct asset list for memecoin strategy (manually selected)
  assets:
    # Original tokens
    - "gt:solana:A8C3xuqscfmyLrte3VmTqrAq8kgMASius9AFNANwpump:FWOG"  # FWOG (CORRECTED)
    - "gt:solana:DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263:BONK"  # BONK
    - "gt:eth:******************************************:PEPE"   # PEPE
    - "gt:solana:EKpQGSJtjMFqKZ9KQanSqYXRcF8fBopzLHYxdM65zcjm:WIF"     # WIF (CORRECTED)
    - "gt:solana:63LfDmNb3MQ8mw9MtZ2To9bEA2M71kZUUGq5tiJxcqj9:GIGA"   # GIGA

    # Additional popular memecoins from main_program.py metadata
    - "gt:solana:BkVeSP2GsXV3AYoRJBSZTpFE8sXmcuGnRQcFgoWspump:AUTISM"  # AUTISM
    - "gt:base:******************************************:TOSHI"      # TOSHI
    - "gt:bsc:******************************************:ALTURA"      # ALTURA
    - "gt:solana:5SVG3T9CNQsm2kEwzbRq6hASqh1oGfjqTtLXYUibpump:SIGMA"   # SIGMA
    - "gt:solana:8Ki8DpuWNxu9VsS3kQbarsCWMcFGWkzzA8pUPto9zBd5:LOCKIN" # LOCKIN
    - "gt:solana:J3NKxxXZcnNiMjKw9hYb2K4LUxgwB6t1FtPtQVsv3KFr:SPX"     # SPX
    - "gt:base:******************************************:SKI"         # SKI
    - "gt:solana:Df6yfrKC8kZE3KNkrHERKzAetSxbrWeniQfyJY4Jpump:CHILLGUY" # CHILLGUY
    - "gt:eth:******************************************:APU"         # APU
    - "gt:solana:9WPTUkh8fKuCnepRWoPYLH3aK9gSjPHFDenBq2X1Czdp:SELFIE"  # SELFIE
    - "gt:base:******************************************:ALIENBASE"  # ALIENBASE
    - "gt:eth:******************************************:DOGEETH"     # DOGEETH
    - "gt:solana:5mbK36SZ7J19An8jFochhQS4of8g6BwUjbeCSxBSoWdp:MICHI"   # MICHI
    - "gt:solana:2JcXacFwt9mVAwBQ5nZkYwCyXQkRcdsYrDXn6hj22SbP:MINI"    # MINI
    - "gt:solana:GtDZKAqvMZMnti46ZewMiXCa4oXF4bZxwQPoKzXPFxZn:NUB"     # NUB
    - "gt:solana:7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr:POPCAT"  # POPCAT
    - "gt:solana:********************************************:GIKO"    # GIKO
    - "gt:solana:8WnQQRbuEZ3CCDbH5MCVioBbw6o75NKANq9WdPhBDsWo:COBY"    # COBY
    - "gt:solana:DtR4D9FtVoTX2569gaL837ZgrB6wNjj6tkmnX9Rdk9B2:AURA"    # AURA
    - "gt:base:******************************************:COCORO"     # COCORO
    - "gt:solana:DPaQfq5sFnoqw2Sh9WMmmASFL9LNu6RdtDqwE1tab2tB:SKIBIDI" # SKIBIDI
    - "gt:solana:4YK1njyeCkBuXG6phNtidJWKCbBhB659iwGkUJx98P5Z:DOLAN"   # DOLAN
    - "gt:solana:69kdRLyP5DTRkpHraaSZAQbWmAwzF9guKjZfzMXzcbAs:USA"     # USA
    - "gt:eth:******************************************:EPIK"        # EPIK

  # Weights for weighted allocation (if allocation_approach is "weighted")
  weights: [0.8,0.2]  # Top 2 assets (40-25-20-10-5 split)

  # MTPI settings
  mtpi_warmup_period: 120  # days - increased for proper MTPI crossover detection

  # MTPI indicators configuration (matching main strategy)
  mtpi_indicators:
    aad_score:
      aad_mult: 1.2
      avg_type: SMA
      length: 22
      src_col: close
    bollinger_bands:
      heikin_src: close
      length: 33
      long_threshold: 76.0
      multiplier: 2.0
      short_threshold: 31.0
      use_heikin_ashi: false
    combination_method: consensus
    dema_super_score:
      atr_period: 19
      length: 17
      multiplier: 2.8
      src_col: close
    dpsd_score:
      dema_length: 9
      ema_length: 14
      percentile_length: 58
      percentile_lower: 45.0
      percentile_upper: 60.0
      sd_length: 27
      src_col: high
    dwma_score:
      atr_multiplier: 1.0
      atr_period: 12
      length: 17
      long_threshold: 30
      loop_end: 60
      loop_start: 1
      lower_sd_weight: 0.996
      ma_smooth_length: 12
      ma_type: EMA
      sd_length: 33
      short_threshold: 0
      smoothing_style: Weighted SD
      src_col: close
      upper_sd_weight: 1.031
    dynamic_ema_score:
      atr_multiplier: 1.2
      atr_period: 14
      ema_length: 12
      lower_sd_weight: 0.996
      median_length: 9
      median_src: close
      sd_length: 33
      smoothing_style: Weighted SD
      upper_sd_weight: 1.017
    enabled_indicators:
    - pgo
    - bollinger_bands
    - dwma_score
    - dema_super_score
    - dpsd_score
    - aad_score
    - dynamic_ema_score
    # - quantile_dema_score
    long_threshold: 0.1
    median_score:
      atr_period: 12
      median_length: 27
      multiplier: 1.45
      src_col: high
    pgo:
      length: 35
      lower_threshold: -0.58
      upper_threshold: 1.1
    quantile_dema_score:
      atr_length: 14
      dema_length: 30
      dema_st_length: 30
      mult_dn: 1.2
      mult_up: 1.2
      percentile_filter: 10
      percentile_length: 20
      sd_length: 30
      src_col: close
    short_threshold: -0.1

  # Transaction fee simulation (for backtesting purposes)
  transaction_fee_rate: 0.001

  # Initial capital for performance calculations
  initial_capital: 10000

execution:
  # Scheduling
  schedule_minute: 2  # Run at 2 minutes past the hour (offset from Binance strategy)
  signal_only: true   # No trading, signals only

  # Performance tracking
  track_performance: true
  generate_reports: true

  # Error handling
  max_retry_attempts: 3
  retry_delay_seconds: 30

notifications:
  enabled: true
  include_scores: true
  include_rankings: true
  include_performance_metrics: true

  # Notification frequency
  signal_change_notifications: true
  asset_rotation_notifications: true
  daily_summary: true

  # Risk alerts
  volatility_alerts: true
  extreme_movement_threshold: 0.2  # 20% daily movement

# Memecoin-specific settings
memecoin_settings:
  # Risk management
  high_volatility_threshold: 0.15  # 15% daily volatility
  extreme_movement_threshold: 0.25  # 25% daily movement

  # Signal sensitivity
  momentum_sensitivity: "high"  # Options: "low", "medium", "high"

  # Correlation tracking
  track_correlations: true
  correlation_lookback_days: 30

  # Performance tracking
  benchmark_asset: "BTC/USDT"  # For relative performance comparison

# Logging settings
logging:
  level: "INFO"
  file: "logs/memecoin_strategy.log"
  max_file_size_mb: 50
  backup_count: 5

  # Console output
  console_output: true
  detailed_execution_logs: true

# Cache settings
cache:
  enabled: true
  directory: "data/cache/memecoins"
  max_age_days: 7
  cleanup_on_start: false

# Performance metrics
metrics:
  directory: "Performance_Metrics/memecoins"
  save_charts: true
  chart_format: "png"
  include_benchmark: true
