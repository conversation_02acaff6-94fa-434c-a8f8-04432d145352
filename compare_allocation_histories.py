#!/usr/bin/env python3
"""
Script to compare allocation histories between main_program.py and allocation_report.py
to identify discrepancies in asset selection and holdings.
"""

import os
import sys
import pandas as pd
import logging
from datetime import datetime
import subprocess
import json

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def run_main_program_and_save_csv(args):
    """Run main_program.py with given arguments and save allocation history to CSV."""
    try:
        # Build the command
        cmd = [
            'python', 'main_program.py',
            '--save-allocation-history',  # This flag should trigger CSV saving
        ] + args
        
        logging.info(f"Running main_program.py with command: {' '.join(cmd)}")
        
        # Run the command
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='.')
        
        if result.returncode != 0:
            logging.error(f"main_program.py failed with return code {result.returncode}")
            logging.error(f"stderr: {result.stderr}")
            return None
        
        logging.info("main_program.py completed successfully")
        
        # Look for the generated CSV file
        # The main_program.py should generate a file like "allocation_history_*.csv"
        csv_files = [f for f in os.listdir('.') if f.startswith('allocation_history_') and f.endswith('.csv')]
        if csv_files:
            latest_csv = max(csv_files, key=os.path.getctime)
            logging.info(f"Found main_program allocation history CSV: {latest_csv}")
            return latest_csv
        else:
            logging.warning("No allocation history CSV found from main_program.py")
            return None
            
    except Exception as e:
        logging.error(f"Error running main_program.py: {e}")
        return None

def run_allocation_report_and_extract_csv(args):
    """Run allocation_report.py with given arguments and extract allocation history."""
    try:
        # Build the command
        cmd = ['python', 'allocation_report.py'] + args
        
        logging.info(f"Running allocation_report.py with command: {' '.join(cmd)}")
        
        # Run the command
        result = subprocess.run(cmd, capture_output=True, text=True, cwd='.')
        
        if result.returncode != 0:
            logging.error(f"allocation_report.py failed with return code {result.returncode}")
            logging.error(f"stderr: {result.stderr}")
            return None
        
        logging.info("allocation_report.py completed successfully")
        
        # Since allocation_report.py doesn't automatically save CSV, we need to modify it
        # or extract the data from the HTML report. For now, let's create a simple extraction.
        # We'll need to modify allocation_report.py to save CSV as well.
        
        return "allocation_report_history.csv"  # Placeholder
        
    except Exception as e:
        logging.error(f"Error running allocation_report.py: {e}")
        return None

def compare_allocation_histories(main_csv, report_csv):
    """Compare the allocation histories from both scripts."""
    try:
        # Read both CSV files
        main_df = pd.read_csv(main_csv)
        report_df = pd.read_csv(report_csv)
        
        logging.info(f"Main program history: {len(main_df)} rows")
        logging.info(f"Allocation report history: {len(report_df)} rows")
        
        # Convert date columns to datetime
        main_df['date'] = pd.to_datetime(main_df['date'])
        report_df['date'] = pd.to_datetime(report_df['date'])
        
        # Sort by date
        main_df = main_df.sort_values('date')
        report_df = report_df.sort_values('date')
        
        # Find common dates
        main_dates = set(main_df['date'])
        report_dates = set(report_df['date'])
        common_dates = main_dates.intersection(report_dates)
        
        logging.info(f"Common dates: {len(common_dates)}")
        logging.info(f"Main program only dates: {len(main_dates - report_dates)}")
        logging.info(f"Allocation report only dates: {len(report_dates - main_dates)}")
        
        # Compare holdings for common dates
        discrepancies = []
        
        for date in sorted(common_dates):
            main_row = main_df[main_df['date'] == date].iloc[0]
            report_row = report_df[report_df['date'] == date].iloc[0]
            
            # Compare top assets
            main_assets = eval(main_row['top_assets']) if isinstance(main_row['top_assets'], str) else main_row['top_assets']
            report_assets = eval(report_row['top_assets']) if isinstance(report_row['top_assets'], str) else report_row['top_assets']
            
            if main_assets != report_assets:
                discrepancies.append({
                    'date': date,
                    'main_assets': main_assets,
                    'report_assets': report_assets,
                    'main_mtpi': main_row['mtpi_signal'],
                    'report_mtpi': report_row['mtpi_signal'],
                    'main_return': main_row.get('portfolio_return', 0),
                    'report_return': report_row.get('portfolio_return', 0)
                })
        
        logging.info(f"Found {len(discrepancies)} discrepancies in asset selection")
        
        # Save discrepancies to CSV
        if discrepancies:
            discrepancy_df = pd.DataFrame(discrepancies)
            discrepancy_file = f"allocation_discrepancies_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            discrepancy_df.to_csv(discrepancy_file, index=False)
            logging.info(f"Saved discrepancies to {discrepancy_file}")
            
            # Print first few discrepancies
            print("\nFirst 10 discrepancies:")
            print("=" * 100)
            for i, disc in enumerate(discrepancies[:10]):
                print(f"{i+1}. Date: {disc['date']}")
                print(f"   Main assets: {disc['main_assets']}")
                print(f"   Report assets: {disc['report_assets']}")
                print(f"   MTPI signals: Main={disc['main_mtpi']}, Report={disc['report_mtpi']}")
                print(f"   Returns: Main={disc['main_return']:.4f}, Report={disc['report_return']:.4f}")
                print()
        
        return discrepancies
        
    except Exception as e:
        logging.error(f"Error comparing allocation histories: {e}")
        return None

def main():
    """Main function to run the comparison."""
    # Test arguments - same as the user's command
    test_args = [
        '--assets', 'ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 
        'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 
        'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT',
        '--trend-method', 'PGO For Loop',
        '--timeframe', '1d',
        '--analysis-start-date', '2023-10-19',
        '--n-assets', '1',
        '--transaction-fee', '0.001',
        '--mtpi-combination-method', 'consensus',
        '--mtpi-long-threshold', '0.1',
        '--mtpi-short-threshold', '-0.1',
        '--mtpi-indicators', 'pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 
        'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score',
        '--ratio-calculation', 'independent',
        '--tie-breaking-strategy', 'momentum'
    ]
    
    print("Starting allocation history comparison...")
    print("=" * 60)
    
    # First, we need to modify both scripts to save CSV files
    # Let's check if main_program.py has the --save-allocation-history flag
    
    print("This script needs to be enhanced to:")
    print("1. Modify main_program.py to save allocation history CSV")
    print("2. Modify allocation_report.py to save allocation history CSV")
    print("3. Then run the comparison")
    print("\nFor now, let's check what CSV files already exist...")
    
    # List existing CSV files
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    allocation_csvs = [f for f in csv_files if 'allocation' in f.lower()]
    
    print(f"\nFound {len(allocation_csvs)} allocation-related CSV files:")
    for csv_file in allocation_csvs:
        print(f"  - {csv_file}")
    
    if len(allocation_csvs) >= 2:
        print(f"\nWould you like to compare {allocation_csvs[0]} and {allocation_csvs[1]}? (y/n)")
        # For now, just print the suggestion
        print("Run this script interactively to proceed with comparison.")

if __name__ == "__main__":
    main()
