#!/usr/bin/env python3
"""
Debug script to check previous scores retrieval
"""
import pandas as pd
import ast

def debug_previous_scores():
    """Debug how previous scores are being retrieved"""
    
    print("=== DEBUGGING PREVIOUS SCORES RETRIEVAL ===\n")
    
    # Read the CSV
    df = pd.read_csv('allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv')
    df['scores_dict'] = df['scores'].apply(lambda x: ast.literal_eval(x) if pd.notna(x) else {})
    
    # Focus on the violation date
    violation_date = '2024-01-06 00:00:00+00:00'
    prev_date = '2024-01-05 00:00:00+00:00'
    
    violation_idx = df[df['date'] == violation_date].index[0]
    prev_idx = df[df['date'] == prev_date].index[0]
    
    print(f"Violation date: {violation_date} (index: {violation_idx})")
    print(f"Previous date: {prev_date} (index: {prev_idx})")
    print(f"Expected prev_idx = violation_idx - 1: {violation_idx - 1}")
    print(f"Actual prev_idx: {prev_idx}")
    print(f"Match: {prev_idx == violation_idx - 1}")
    print()
    
    # Simulate the main program logic for retrieving previous scores
    print("--- Simulating main program previous scores retrieval ---")
    
    # This simulates the main_program.py logic
    start_idx = 0  # Assuming start_idx is 0 for simplicity
    i = violation_idx
    tie_breaking_strategy = 'momentum'
    selected_assets = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']
    
    # Create a DataFrame similar to daily_scores_df
    dates = pd.to_datetime(df['date'])
    scores_data = {}
    for asset in selected_assets:
        scores_data[asset] = [row['scores_dict'].get(asset, 0) for _, row in df.iterrows()]
    
    daily_scores_df = pd.DataFrame(scores_data, index=dates)
    
    print(f"daily_scores_df shape: {daily_scores_df.shape}")
    print(f"daily_scores_df index type: {type(daily_scores_df.index[0])}")
    print(f"Sample dates: {daily_scores_df.index[:3].tolist()}")
    print()
    
    # Simulate the exact logic from main_program.py
    previous_scores = {}
    if i > start_idx and tie_breaking_strategy == 'momentum':
        print(f"Condition met: i ({i}) > start_idx ({start_idx}) and tie_breaking_strategy == 'momentum'")
        
        prev_score_date = daily_scores_df.index[i-1]
        print(f"prev_score_date: {prev_score_date}")
        print(f"prev_score_date type: {type(prev_score_date)}")
        
        for asset in selected_assets:
            if asset in daily_scores_df.columns and prev_score_date in daily_scores_df.index:
                previous_scores[asset] = daily_scores_df.loc[prev_score_date, asset]
                print(f"  {asset}: {previous_scores[asset]}")
            else:
                previous_scores[asset] = 0
                print(f"  {asset}: 0 (not found)")
    else:
        print(f"Condition NOT met: i ({i}) > start_idx ({start_idx}): {i > start_idx}, tie_breaking_strategy: {tie_breaking_strategy}")
    
    print(f"\nFinal previous_scores: {previous_scores}")
    
    # Compare with expected
    expected_previous_scores = df.iloc[prev_idx]['scores_dict']
    print(f"Expected previous_scores: {expected_previous_scores}")
    
    if previous_scores == expected_previous_scores:
        print("✓ Previous scores match expected!")
    else:
        print("✗ Previous scores do NOT match expected!")
        
        # Check what's different
        for asset in selected_assets:
            expected = expected_previous_scores.get(asset, 0)
            actual = previous_scores.get(asset, 0)
            if expected != actual:
                print(f"  {asset}: expected {expected}, got {actual}")

if __name__ == "__main__":
    debug_previous_scores()
