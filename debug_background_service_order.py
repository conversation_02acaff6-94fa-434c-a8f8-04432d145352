#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_background_service_order():
    """Debug the actual order used in background service execution."""
    
    print("=" * 80)
    print("DEBUGGING BACKGROUND SERVICE ASSET ORDER")
    print("=" * 80)
    
    # Simulate the background service logic
    from src.config_manager import load_config
    
    config_path = "config/settings_bitvavo_eur.yaml"
    config = load_config(config_path)
    settings = config.get('settings', {})
    
    # This is the exact logic from background_service.py line 1260
    trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
    
    print("1. TREND_ASSETS from background_service.py:")
    print("-" * 50)
    for i, asset in enumerate(trend_assets, 1):
        print(f"  {i:2d}. {asset}")
    
    # This simulates the data_dict creation order
    print("\n2. DATA_DICT KEYS ORDER (simulated):")
    print("-" * 50)
    data_dict_keys = list(trend_assets)  # This is what list(data_dict.keys()) would return
    for i, key in enumerate(data_dict_keys, 1):
        print(f"  {i:2d}. {key}")
    
    # This simulates the DataFrame column order
    print("\n3. DATAFRAME COLUMN ORDER (simulated):")
    print("-" * 50)
    for i, col in enumerate(data_dict_keys, 1):
        print(f"  {i:2d}. {col}")
    
    # This simulates the latest_scores dictionary order
    print("\n4. LATEST_SCORES DICTIONARY ORDER (simulated):")
    print("-" * 50)
    for i, key in enumerate(data_dict_keys, 1):
        print(f"  {i:2d}. {key}")
    
    # Simulate the conversion to EUR
    print("\n5. CONVERSION TO EUR:")
    print("-" * 50)
    
    # Get trading assets (EUR)
    trading_assets = settings.get('assets', [])
    
    # Create conversion mapping (same logic as main_program.py)
    usdt_to_trading_map = {}
    for usdt_asset in trend_assets:  # This uses selected_assets which is trend_assets
        if '/USDT' in usdt_asset:
            base_asset = usdt_asset.replace('/USDT', '')
            trading_equivalent = f"{base_asset}/EUR"
            if trading_equivalent in trading_assets:
                usdt_to_trading_map[usdt_asset] = trading_equivalent
    
    print("Conversion mapping:")
    for usdt, eur in usdt_to_trading_map.items():
        print(f"  {usdt} -> {eur}")
    
    # Simulate the conversion loop (main_program.py line 4006)
    print("\nConversion loop: for usdt_asset, score in latest_scores.items():")
    converted_scores = {}
    for i, usdt_asset in enumerate(data_dict_keys, 1):
        if usdt_asset in usdt_to_trading_map:
            trading_asset = usdt_to_trading_map[usdt_asset]
            converted_scores[trading_asset] = 12.0  # Dummy score
            print(f"  {i:2d}. {usdt_asset} -> {trading_asset}")
    
    print("\n6. FINAL EUR SCORES DICTIONARY ORDER:")
    print("-" * 50)
    final_keys = list(converted_scores.keys())
    for i, key in enumerate(final_keys, 1):
        marker = ""
        if key == 'BTC/EUR':
            marker = " ← BTC"
        elif key == 'TRX/EUR':
            marker = " ← TRX"
        print(f"  {i:2d}. {key}{marker}")
    
    # Check BTC vs TRX positions
    if 'BTC/EUR' in final_keys and 'TRX/EUR' in final_keys:
        btc_pos = final_keys.index('BTC/EUR') + 1
        trx_pos = final_keys.index('TRX/EUR') + 1
        print(f"\nBTC/EUR position: {btc_pos}")
        print(f"TRX/EUR position: {trx_pos}")
        print(f"BTC comes before TRX: {btc_pos < trx_pos}")
        
        if btc_pos < trx_pos:
            print("✅ BTC should be selected in tie-breaking")
        else:
            print("❌ TRX would be selected in tie-breaking")
            print("🚨 THIS WOULD EXPLAIN THE BUG!")
    
    # Test with actual tie-breaking
    print("\n7. TESTING TIE-BREAKING:")
    print("-" * 50)
    
    # Create test scores with BTC and TRX tied
    test_scores = {}
    for key in final_keys:
        if key in ['BTC/EUR', 'TRX/EUR']:
            test_scores[key] = 12.0  # Tied score
        else:
            test_scores[key] = 8.0   # Lower score
    
    print("Test scores (BTC and TRX tied):")
    for key, score in test_scores.items():
        marker = ""
        if key in ['BTC/EUR', 'TRX/EUR']:
            marker = " ← TIED"
        print(f"  {key}: {score}{marker}")
    
    from src.scoring import find_best_asset_for_day
    selected = find_best_asset_for_day(test_scores)
    
    print(f"\nSelected asset: {selected}")
    if selected == 'BTC/EUR':
        print("✅ BTC was selected")
    elif selected == 'TRX/EUR':
        print("❌ TRX was selected - BUG CONFIRMED!")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    debug_background_service_order()
