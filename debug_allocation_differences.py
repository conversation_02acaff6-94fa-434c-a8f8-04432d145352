#!/usr/bin/env python3
"""
Quick debugging script to compare allocation differences between main_program.py and allocation_report.py
"""

import pandas as pd
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def compare_csv_files():
    """Compare the two allocation history CSV files."""
    
    # Read the CSV files
    try:
        main_df = pd.read_csv('allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv')
        report_df = pd.read_csv('allocation_report_history_20250627_003719.csv')
        
        print(f"Main program CSV: {len(main_df)} rows")
        print(f"Allocation report CSV: {len(report_df)} rows")
        
        # Convert date columns
        main_df['date'] = pd.to_datetime(main_df['date'])
        report_df['date'] = pd.to_datetime(report_df['date'])
        
        # Sort by date
        main_df = main_df.sort_values('date')
        report_df = report_df.sort_values('date')
        
        # Find first few discrepancies
        discrepancies = []
        
        # Get common date range
        main_dates = set(main_df['date'])
        report_dates = set(report_df['date'])
        common_dates = sorted(main_dates.intersection(report_dates))
        
        print(f"Common dates: {len(common_dates)}")
        
        for date in common_dates[:50]:  # Check first 50 dates
            main_row = main_df[main_df['date'] == date]
            report_row = report_df[report_df['date'] == date]
            
            if main_row.empty or report_row.empty:
                continue
                
            main_row = main_row.iloc[0]
            report_row = report_row.iloc[0]
            
            # Parse top_assets (they might be strings)
            try:
                main_assets = eval(main_row['top_assets']) if isinstance(main_row['top_assets'], str) else main_row['top_assets']
                report_assets = eval(report_row['top_assets']) if isinstance(report_row['top_assets'], str) else report_row['top_assets']
            except:
                main_assets = main_row['top_assets']
                report_assets = report_row['top_assets']
            
            if main_assets != report_assets:
                discrepancies.append({
                    'date': date,
                    'main_assets': main_assets,
                    'report_assets': report_assets,
                    'main_mtpi': main_row.get('mtpi_signal', 'N/A'),
                    'report_mtpi': report_row.get('mtpi_signal', 'N/A'),
                    'main_return': main_row.get('portfolio_return', 0),
                    'report_return': report_row.get('portfolio_return', 0)
                })
        
        print(f"\nFound {len(discrepancies)} discrepancies in first 50 dates:")
        print("=" * 80)
        
        for i, disc in enumerate(discrepancies[:10]):
            print(f"{i+1}. Date: {disc['date'].strftime('%Y-%m-%d')}")
            print(f"   Main assets: {disc['main_assets']}")
            print(f"   Report assets: {disc['report_assets']}")
            print(f"   MTPI signals: Main={disc['main_mtpi']}, Report={disc['report_mtpi']}")
            print(f"   Returns: Main={disc['main_return']:.6f}, Report={disc['report_return']:.6f}")
            print()
        
        # Check if MTPI signals are different
        mtpi_differences = 0
        for disc in discrepancies:
            if disc['main_mtpi'] != disc['report_mtpi']:
                mtpi_differences += 1
        
        print(f"MTPI signal differences: {mtpi_differences} out of {len(discrepancies)} discrepancies")
        
        # Look for patterns
        if discrepancies:
            first_discrepancy_date = discrepancies[0]['date']
            print(f"\nFirst discrepancy occurs on: {first_discrepancy_date.strftime('%Y-%m-%d')}")
            
            # Check if it's consistent from the start or starts at a specific point
            early_discrepancies = [d for d in discrepancies if d['date'] <= pd.Timestamp('2023-11-01')]
            print(f"Discrepancies in October 2023: {len(early_discrepancies)}")
            
            if early_discrepancies:
                print("Early discrepancy pattern:")
                for disc in early_discrepancies[:5]:
                    print(f"  {disc['date'].strftime('%Y-%m-%d')}: Main={disc['main_assets']}, Report={disc['report_assets']}")
        
        return discrepancies
        
    except FileNotFoundError as e:
        print(f"Error: Could not find CSV file - {e}")
        print("Make sure both allocation history CSV files exist in the current directory")
        return None
    except Exception as e:
        print(f"Error comparing files: {e}")
        return None

def analyze_specific_date():
    """Analyze a specific date where we know there's a discrepancy."""

    # Use the first discrepancy date we found: 2023-12-07
    target_date = '2023-12-07'

    try:
        main_df = pd.read_csv('allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv')
        report_df = pd.read_csv('allocation_report_history_20250627_003719.csv')

        main_df['date'] = pd.to_datetime(main_df['date'])
        report_df['date'] = pd.to_datetime(report_df['date'])

        target_ts = pd.Timestamp(target_date)

        main_row = main_df[main_df['date'] == target_ts]
        report_row = report_df[report_df['date'] == target_ts]

        if not main_row.empty and not report_row.empty:
            main_row = main_row.iloc[0]
            report_row = report_row.iloc[0]

            print(f"\nDetailed analysis for {target_date}:")
            print("=" * 50)
            print(f"Main program:")
            print(f"  Top assets: {main_row['top_assets']}")
            print(f"  MTPI signal: {main_row.get('mtpi_signal', 'N/A')}")
            print(f"  Portfolio return: {main_row.get('portfolio_return', 0):.6f}")
            print(f"  Transaction cost: {main_row.get('transaction_cost', 0):.6f}")
            print(f"  Equity value: {main_row.get('equity_value', 0):.2f}")

            print(f"\nAllocation report:")
            print(f"  Top assets: {report_row['top_assets']}")
            print(f"  MTPI signal: {report_row.get('mtpi_signal', 'N/A')}")
            print(f"  Portfolio return: {report_row.get('portfolio_return', 0):.6f}")
            print(f"  Transaction cost: {report_row.get('transaction_cost', 0):.6f}")
            print(f"  Equity value: {report_row.get('equity_value', 0):.2f}")

            # Try to parse and compare scores if available
            try:
                main_scores = eval(main_row.get('scores', '{}')) if main_row.get('scores') else {}
                report_scores = eval(report_row.get('scores', '{}')) if report_row.get('scores') else {}

                if main_scores and report_scores:
                    print(f"\nScore comparison:")
                    all_assets = set(main_scores.keys()) | set(report_scores.keys())
                    for asset in sorted(all_assets):
                        main_score = main_scores.get(asset, 'N/A')
                        report_score = report_scores.get(asset, 'N/A')
                        match = "✓" if main_score == report_score else "✗"
                        print(f"  {asset}: Main={main_score}, Report={report_score} {match}")

                    # Check for ties
                    if main_scores:
                        max_main_score = max(main_scores.values())
                        tied_main = [k for k, v in main_scores.items() if v == max_main_score]
                        print(f"\nMain program tied assets (score {max_main_score}): {tied_main}")

                    if report_scores:
                        max_report_score = max(report_scores.values())
                        tied_report = [k for k, v in report_scores.items() if v == max_report_score]
                        print(f"Report tied assets (score {max_report_score}): {tied_report}")

            except Exception as e:
                print(f"Could not parse scores: {e}")

        else:
            print(f"Could not find data for {target_date} in one or both files")

    except Exception as e:
        print(f"Error analyzing specific date: {e}")

def main():
    print("Debugging allocation differences between main_program.py and allocation_report.py")
    print("=" * 80)
    
    # Compare the CSV files
    discrepancies = compare_csv_files()
    
    # Analyze a specific date
    analyze_specific_date()
    
    if discrepancies:
        print(f"\nSUMMARY:")
        print(f"- Found {len(discrepancies)} discrepancies")
        print(f"- This suggests the scripts are using different data or logic")
        print(f"- Key areas to investigate:")
        print(f"  1. Daily scores calculation differences")
        print(f"  2. MTPI signal differences")
        print(f"  3. Asset filtering logic differences")
        print(f"  4. Tie-breaking strategy differences")

if __name__ == "__main__":
    main()
