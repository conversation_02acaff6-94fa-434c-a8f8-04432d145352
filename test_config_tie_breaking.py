#!/usr/bin/env python3
"""
Test script to verify the tie-breaking configuration is being read correctly.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config_manager import load_config

def test_config_reading():
    """Test that the tie-breaking strategy is read from config files."""
    
    print("=" * 60)
    print("TESTING TIE-BREAKING CONFIGURATION")
    print("=" * 60)
    
    # Test main config
    print("1. MAIN CONFIG (settings.yaml):")
    print("-" * 40)
    try:
        config = load_config('config/settings.yaml')
        tie_breaking = config.get('settings', {}).get('tie_breaking_strategy', 'NOT FOUND')
        print(f"tie_breaking_strategy: {tie_breaking}")
    except Exception as e:
        print(f"Error loading main config: {e}")
    
    # Test Bitvavo config
    print(f"\n2. BITVAVO CONFIG (settings_bitvavo_eur.yaml):")
    print("-" * 40)
    try:
        config = load_config('config/settings_bitvavo_eur.yaml')
        tie_breaking = config.get('settings', {}).get('tie_breaking_strategy', 'NOT FOUND')
        print(f"tie_breaking_strategy: {tie_breaking}")
    except Exception as e:
        print(f"Error loading Bitvavo config: {e}")
    
    # Test default behavior
    print(f"\n3. DEFAULT BEHAVIOR TEST:")
    print("-" * 40)
    test_config = {'settings': {}}  # No tie_breaking_strategy defined
    default_strategy = test_config.get('settings', {}).get('tie_breaking_strategy', 'incumbent')
    print(f"Default when not specified: {default_strategy}")
    
    print(f"\n4. VALID VALUES:")
    print("-" * 40)
    print("✅ 'incumbent' - Conservative approach (keep current leader)")
    print("✅ 'momentum' - Aggressive approach (favor dictionary order)")
    print("❌ Any other value defaults to 'incumbent'")
    
    print("=" * 60)

if __name__ == "__main__":
    test_config_reading()
