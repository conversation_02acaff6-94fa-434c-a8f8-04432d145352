# Tie-Breaking Strategies Configuration

## Overview

When multiple assets have equal scores, the system needs to decide which asset to select. This document explains the two available tie-breaking strategies and how to configure them.

## Configuration

Add the following setting to your YAML configuration file:

```yaml
settings:
  # Tie-breaking strategy when assets have equal scores
  # 'incumbent' - Keep current leader until definitively beaten (conservative)
  # 'momentum' - Favor the asset that just reached the tied score (aggressive)
  tie_breaking_strategy: incumbent  # or 'momentum'
```

## Strategies Explained

### 1. Incumbent Approach (Conservative)
**Configuration**: `tie_breaking_strategy: incumbent`

**Philosophy**: "Don't change horses mid-race"

**Behavior**:
- When assets have tied scores, keep the current leader (incumbent)
- Only switch when another asset definitively beats the current score
- Reduces unnecessary switching and transaction costs

**Example**:
- TRX was selected for the last 5 days
- Today: BTC and TRX both have score 12.0 (tied)
- **Result**: Keep TRX (the incumbent)

**Advantages**:
- ✅ Reduces unnecessary switching
- ✅ Stable in choppy/sideways markets
- ✅ Lower transaction costs
- ✅ Less prone to whipsawing

**Disadvantages**:
- ❌ May miss early momentum signals
- ❌ Slower to react to emerging trends

### 2. Momentum Approach (Aggressive)
**Configuration**: `tie_breaking_strategy: momentum`

**Philosophy**: "Catch the rising star"

**Behavior**:
- When assets have tied scores, favor the asset that appears first in the configuration order
- This typically represents the "follower" that just caught up to the leader
- More responsive to recent changes

**Example**:
- TRX was selected for the last 5 days
- Today: BTC and TRX both have score 12.0 (tied)
- BTC appears before TRX in the configuration order
- **Result**: Switch to BTC (the momentum candidate)

**Advantages**:
- ✅ Catches momentum early
- ✅ More responsive to changes
- ✅ May capture breakouts faster
- ✅ Better for trending markets

**Disadvantages**:
- ❌ May cause more switching
- ❌ Higher transaction costs
- ❌ More prone to false signals

## Configuration Files

The setting can be added to any of these configuration files:
- `config/settings.yaml` (main configuration)
- `config/settings_bitvavo_eur.yaml` (Bitvavo-specific)
- `config/settings_kraken_eur.yaml` (Kraken-specific)

## Default Behavior

If `tie_breaking_strategy` is not specified, the system defaults to `incumbent` (conservative approach).

## Testing

You can test both strategies using the provided test scripts:

```bash
# Test the strategies conceptually
python test_tie_breaking_strategies.py

# Verify configuration is loaded correctly
python test_config_tie_breaking.py

# Verify current implementation
python verify_incumbent_approach.py
```

## Monitoring

When ties occur, the system logs detailed information:

```
[DEBUG] TIE DETECTED - 2 assets tied at score 12.0
[DEBUG] TIE DETECTED - Tied assets: ['BTC/EUR', 'TRX/EUR']
[DEBUG] INCUMBENT APPROACH - Keeping incumbent TRX/EUR
```

or

```
[DEBUG] TIE DETECTED - 2 assets tied at score 12.0
[DEBUG] TIE DETECTED - Tied assets: ['BTC/EUR', 'TRX/EUR']
[DEBUG] MOMENTUM APPROACH - Using current day tie-breaking: BTC/EUR
```

## Recommendations

### Use Incumbent Approach When:
- You want stability and lower transaction costs
- Market conditions are choppy or sideways
- You prefer a "buy and hold" style approach
- You want to reduce the impact of noise

### Use Momentum Approach When:
- You want to catch trends early
- Market conditions are trending
- You're comfortable with more frequent switching
- You want maximum responsiveness to changes

## Implementation Details

The tie-breaking logic is implemented in `background_service.py` and uses the following decision tree:

1. **Check for ties**: Find all assets with the maximum score
2. **If no tie**: Select the single winner
3. **If tie exists**:
   - **Incumbent approach**: Keep current leader if they're among tied assets
   - **Momentum approach**: Use dictionary order (first asset in configuration)
4. **Log the decision** for monitoring and debugging

This provides a clean, configurable way to handle the fundamental trade-off between stability and responsiveness in asset selection.
