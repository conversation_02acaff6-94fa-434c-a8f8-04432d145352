2025-06-26 18:10:59,611 - root - INFO - Loaded environment variables from .env file
2025-06-26 18:11:00,426 - root - INFO - Loaded 55 trade records from logs/trades\trade_log_********.json
2025-06-26 18:11:00,426 - root - INFO - Loaded 32 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 18:11:00,426 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 18:11:01,459 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:01,466 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:01,472 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:01,483 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:01,483 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:01,494 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:01,494 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 18:11:01,494 - root - INFO - Notification configuration loaded successfully.
2025-06-26 18:11:02,498 - root - INFO - Telegram command handlers registered
2025-06-26 18:11:02,499 - root - INFO - Telegram bot polling started
2025-06-26 18:11:02,499 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 18:11:02,499 - root - INFO - Telegram notification channel initialized
2025-06-26 18:11:02,501 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 18:11:02,501 - root - INFO - Loaded 24 templates from file
2025-06-26 18:11:02,501 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 18:11:02,501 - root - INFO - Notification manager initialized
2025-06-26 18:11:02,502 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 18:11:02,502 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 18:11:02,502 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 18:11:02,502 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 18:11:02,506 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 18:11:02,507 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 18:11:02,508 - root - INFO - Recovery manager initialized
2025-06-26 18:11:02,508 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 18:11:02,508 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 18:11:02,508 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:02,519 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:02,520 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:11:02,520 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:11:02,521 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:11:02,521 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:11:02,521 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:11:02,521 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:11:02,521 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:11:02,521 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:11:02,521 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:02,529 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:02,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 18:11:02,579 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 18:11:02,580 - telegram.ext.Application - INFO - Application started
2025-06-26 18:11:02,852 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 18:11:02,852 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:11:02,852 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:11:02,852 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:11:02,853 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:11:02,853 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:02,860 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:02,864 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:02,873 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:02,874 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:11:02,881 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:02,883 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 18:11:02,883 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 18:11:02,883 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 18:11:02,883 - root - INFO - Trading mode: paper
2025-06-26 18:11:02,883 - root - INFO - Trading enabled: True
2025-06-26 18:11:02,884 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:11:02,884 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:11:02,884 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:11:02,884 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:11:02,885 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:02,892 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:03,219 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 18:11:03,219 - root - INFO - Trading enabled in paper mode
2025-06-26 18:11:03,220 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:11:03,220 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 18:11:03,220 - root - INFO - Reset paper trading account to initial balance
2025-06-26 18:11:03,221 - root - INFO - Generated run ID: ********_181103
2025-06-26 18:11:03,221 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 18:11:03,221 - root - INFO - Background service initialized
2025-06-26 18:11:03,222 - root - INFO - Network watchdog started
2025-06-26 18:11:03,222 - root - INFO - Network watchdog started
2025-06-26 18:11:03,224 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 18:11:03,224 - root - INFO - Background service started
2025-06-26 18:11:03,225 - root - INFO - Executing strategy (run #1)...
2025-06-26 18:11:03,225 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 18:11:03,226 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 18:11:03,227 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 18:11:03,228 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 18:11:03,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:11:09,242 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 18:11:09,306 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:11:09,308 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 18:11:09,308 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 18:11:09,308 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 18:11:09,308 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 18:11:09,336 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,337 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,338 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,338 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 18:11:09,338 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,352 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,353 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,353 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,354 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 18:11:09,354 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,367 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,367 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,368 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,368 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 18:11:09,369 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,377 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,378 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,378 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,378 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 18:11:09,379 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,394 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,394 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,395 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,395 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 18:11:09,396 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,412 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,412 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,413 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,413 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 18:11:09,413 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,430 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,433 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,434 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,434 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 18:11:09,435 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,453 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,453 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,453 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,454 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 18:11:09,454 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,469 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,469 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,470 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,470 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 18:11:09,471 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,486 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,487 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,487 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,487 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 18:11:09,488 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,494 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,495 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,495 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,495 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 18:11:09,496 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,510 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,510 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,511 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,511 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 18:11:09,512 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,526 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,527 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,527 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,527 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 18:11:09,528 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,540 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,540 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,541 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:11:09,541 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 18:11:09,542 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,545 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 18:11:09,545 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 18:11:09,545 - root - INFO -   - Number of indicators: 8
2025-06-26 18:11:09,545 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:11:09,545 - root - INFO -   - Combination method: consensus
2025-06-26 18:11:09,545 - root - INFO -   - Long threshold: 0.1
2025-06-26 18:11:09,546 - root - INFO -   - Short threshold: -0.1
2025-06-26 18:11:09,546 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:11:09,546 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:09,546 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 18:11:09,546 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-26 18:11:09,547 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 18:11:09,547 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 18:11:09,547 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 18:11:09,547 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 18:11:09,547 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 18:11:09,547 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 18:11:09,547 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 18:11:09,547 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:09,555 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:09,555 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:09,561 - root - INFO - Configuration saved successfully.
2025-06-26 18:11:09,561 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:11:09,561 - root - INFO - Number of trend detection assets: 14
2025-06-26 18:11:09,561 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 18:11:09,561 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:09,561 - root - INFO - Number of trading assets: 14
2025-06-26 18:11:09,561 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 18:11:09,801 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:11:09,809 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:09,816 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:11:09,823 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:09,824 - root - INFO - Execution context: backtesting
2025-06-26 18:11:09,824 - root - INFO - Execution timing: candle_close
2025-06-26 18:11:09,824 - root - INFO - Ratio calculation method: independent
2025-06-26 18:11:09,824 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 18:11:09,824 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:11:09,824 - root - INFO - MTPI combination method override: consensus
2025-06-26 18:11:09,825 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 18:11:09,825 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 18:11:09,825 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 18:11:09,825 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:11:09,826 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,827 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,827 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,828 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,828 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,828 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,829 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,829 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,829 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,830 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,830 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,831 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,831 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,831 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:11:09,832 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 18:11:09,848 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,850 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,851 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,851 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 18:11:09,864 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,867 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,868 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,868 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:11:09,879 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,881 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,881 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,882 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 18:11:09,890 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,891 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,893 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,893 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 18:11:09,905 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,907 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,907 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,908 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 18:11:09,919 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,920 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,921 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,921 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 18:11:09,935 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,936 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,937 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,937 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 18:11:09,952 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,953 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,954 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,954 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 18:11:09,968 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,969 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,969 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,971 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 18:11:09,986 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,987 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,987 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,987 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 18:11:09,996 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:09,997 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:09,998 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:09,998 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 18:11:10,012 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:10,013 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:10,014 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:10,015 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 18:11:10,031 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:10,034 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:10,034 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:10,035 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 18:11:10,049 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:10,051 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:11:10,051 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:10,052 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 18:11:10,052 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:11:10,052 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,053 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,053 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,053 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,054 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,054 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,054 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,054 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,055 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,055 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,055 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,056 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,056 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,056 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:11:10,070 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 18:11:10,070 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:11:10,071 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 18:11:10,071 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:11:10,071 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:11:10,078 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:10,078 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 18:11:10,078 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:11:10,079 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:11:10,079 - root - INFO - Override: combination_method = consensus
2025-06-26 18:11:10,079 - root - INFO - Override: long_threshold = 0.1
2025-06-26 18:11:10,079 - root - INFO - Override: short_threshold = -0.1
2025-06-26 18:11:10,079 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 18:11:10,080 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 18:11:10,081 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 18:11:10,081 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 18:11:10,097 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:11:10,098 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:11:10,099 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:11:10,099 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:11:10,099 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:10,099 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 18:11:10,127 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 18:11:10,127 - root - INFO - Generated pgo signals: 256 values
2025-06-26 18:11:10,127 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 18:11:10,127 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 18:11:10,138 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 18:11:10,138 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 18:11:10,138 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 18:11:10,544 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 18:11:10,545 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 18:11:10,589 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 18:11:10,590 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 18:11:10,590 - root - INFO - Generated DEMA Super Score signals
2025-06-26 18:11:10,590 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 18:11:10,662 - root - INFO - Generated DPSD signals
2025-06-26 18:11:10,663 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 18:11:10,663 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 18:11:10,663 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 18:11:10,671 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 18:11:10,671 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 18:11:10,672 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 18:11:10,717 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 18:11:10,717 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 18:11:10,717 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 18:11:10,799 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 18:11:10,804 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 18:11:10,805 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:11:10,806 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 18:11:10,806 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:11:10,807 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 18:11:10,809 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 18:11:10,816 - root - INFO - Configuration saved successfully.
2025-06-26 18:11:10,817 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 18:11:10,817 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:11:10,825 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:10,825 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 18:11:10,825 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 18:11:10,825 - root - INFO - Using ratio calculation method: independent
2025-06-26 18:11:10,842 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:10,861 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:10,877 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:10,877 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:10,890 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:10,895 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:10,911 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:11:10,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:10,925 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:11:10,929 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:10,947 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:10,948 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:10,963 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:10,969 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:10,984 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:10,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,000 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:11,006 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:11,022 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:11:11,022 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,039 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:11:11,044 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:11,067 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:11,068 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,085 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:11,090 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:11,107 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:11:11,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,124 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:11:11,129 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:11,147 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:11:11,147 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,163 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:11:11,168 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:11,184 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:11,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,199 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:11,204 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:11,224 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:11,224 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,241 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:11,249 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:11,268 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,294 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:11,316 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:11:11,316 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,334 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:11:11,343 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:11,366 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:11,367 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,384 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:11,390 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:11,407 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,427 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:11,443 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:11,444 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,459 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:11,466 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:11,483 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:11,484 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,499 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:11,506 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:11,522 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:11,523 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,539 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:11,545 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:11,561 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:11:11,562 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,576 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:11:11,580 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:11,600 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:11,601 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,617 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:11,622 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:11,638 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:11,638 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,652 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:11,657 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:11,673 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:11,674 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,688 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:11,695 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:11,711 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:11,712 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,729 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:11,734 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:11,749 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,770 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:11,788 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,806 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:11,824 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:11:11,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,837 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:11:11,844 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:11,862 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:11:11,862 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,875 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:11:11,880 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:11,898 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,916 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:11,934 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:11,934 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,949 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:11,954 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:11,972 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:11,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:11,986 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:11,992 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:12,008 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:11:12,009 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,023 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:11:12,029 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:12,046 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:11:12,047 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,063 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:11:12,068 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:12,084 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:12,084 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,099 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:12,105 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:12,119 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:12,120 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,135 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:12,140 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:12,154 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:11:12,155 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,168 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:11:12,174 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:12,190 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:11:12,190 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,203 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:11:12,209 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:12,226 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:12,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,241 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:12,245 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:12,262 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:12,262 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,277 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:12,282 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:12,297 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:12,299 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,312 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:12,317 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:12,333 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:12,334 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,346 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:12,352 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:12,366 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,384 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:12,399 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,420 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:12,436 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,458 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:12,476 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:11:12,476 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,494 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:11:12,501 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:12,519 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,544 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:12,565 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,584 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:12,602 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:12,602 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,616 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:12,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:11:12,622 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:12,641 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,660 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:12,676 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,694 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:12,710 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:12,710 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,724 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:12,728 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:12,745 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,764 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:12,782 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,804 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:12,822 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:11:12,822 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,837 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:11:12,842 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:12,857 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,874 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:12,890 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,909 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:12,924 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:12,925 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,941 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:12,945 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:12,962 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:12,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:12,978 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:12,983 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:13,000 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:11:13,000 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,015 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:11:13,021 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:13,038 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:11:13,039 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,053 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:11:13,059 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:13,079 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:13,079 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,099 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:13,105 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:13,128 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:13,128 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,147 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:13,154 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:13,178 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:13,178 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,195 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:11:13,227 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:13,264 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,300 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:13,329 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,358 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:13,379 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:11:13,379 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,395 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:11:13,402 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:13,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,446 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:13,469 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,517 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:13,548 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,594 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:13,649 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,694 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:13,729 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,751 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:13,769 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:13,770 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,789 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:13,795 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:13,811 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:13,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,828 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:13,879 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:13,912 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:13,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,933 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:13,940 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:13,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:13,983 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:13,998 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,021 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:14,045 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:11:14,046 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,067 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:11:14,077 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:14,103 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,130 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:14,151 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:14,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,168 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:14,176 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:14,197 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,222 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:14,246 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,268 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:14,285 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,310 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:14,330 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,356 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:14,378 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,399 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:14,418 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:11:14,419 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,436 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:11:14,442 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:14,462 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,484 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:14,509 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:11:14,509 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,527 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:11:14,533 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:14,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,576 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:14,594 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,618 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:14,639 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,662 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:14,679 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,700 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:14,720 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,746 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:14,769 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:14,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,798 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:14,808 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:14,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,859 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:14,879 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,902 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:14,921 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:11:14,921 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:14,943 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:11:14,951 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:14,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,001 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:15,021 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:11:15,021 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,045 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:11:15,050 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:15,070 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:15,070 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,095 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:15,102 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:15,135 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:11:15,135 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,162 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:11:15,170 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:15,228 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,280 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:15,304 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:15,304 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,327 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:15,331 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:15,351 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:15,351 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,367 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:15,376 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:15,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,416 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:15,434 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:15,435 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,452 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:15,459 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:15,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,508 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:15,530 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,556 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:15,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,607 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:15,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,659 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:15,680 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,704 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:15,723 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:11:15,724 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,743 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:11:15,748 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:15,765 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,786 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:15,805 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,833 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:15,854 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,880 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:15,897 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:15,898 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,916 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:15,924 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:15,948 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:15,981 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:16,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,038 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:16,064 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,085 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:16,108 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,134 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:16,153 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,178 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:16,198 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:16,198 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,213 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:16,218 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:16,235 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,259 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:16,279 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:16,279 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,298 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:16,302 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:16,322 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:16,323 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,340 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:16,345 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:16,364 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:16,364 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,379 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:16,387 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:16,408 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,432 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:16,452 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:16,452 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,470 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:16,477 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:16,496 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:16,496 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,512 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:16,517 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:16,534 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,552 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:16,574 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,598 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:16,641 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,668 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:16,686 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,708 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:16,726 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,752 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:16,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,790 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:16,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,830 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:16,850 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,872 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:16,896 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:11:16,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,910 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:11:16,910 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:16,940 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:16,940 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:16,960 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:11:16,963 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:16,980 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:16,980 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,000 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:11:17,003 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:17,030 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:11:17,030 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,050 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:11:17,053 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:17,080 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,111 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:17,130 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,169 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:17,194 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,213 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:17,230 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,250 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:17,264 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,290 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:17,310 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,330 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:17,351 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,374 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:17,390 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:17,395 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,409 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:17,410 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:17,420 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:17,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,440 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:11:17,440 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:17,460 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:17,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,471 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:17,482 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:17,499 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:11:17,499 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,510 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:11:17,517 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:17,531 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:11:17,531 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,547 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:11:17,550 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:17,568 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:17,568 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,580 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:17,587 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:17,602 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:11:17,603 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,610 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:11:17,619 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:17,636 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:11:17,639 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,656 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:11:17,660 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:17,677 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,695 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:17,700 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:17,700 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,722 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:17,729 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:17,742 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,763 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:17,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,799 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:17,810 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:17,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,820 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:17,830 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:17,841 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:17,849 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,860 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:17,862 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:17,881 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:17,882 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,890 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:11:17,900 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:17,911 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:11:17,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:17,923 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:11:17,930 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:17,991 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:11:17,991 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,016 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:11:18,021 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:18,041 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:18,042 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,050 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:18,050 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:18,072 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:11:18,072 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,090 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:11:18,101 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:18,115 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:18,115 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,130 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:11:18,141 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:18,160 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:18,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,170 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:18,180 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:18,190 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,210 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:18,230 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,250 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:11:18,268 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,290 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:11:18,315 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,341 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:11:18,361 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,388 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:11:18,410 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:18,410 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,434 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:11:18,443 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:11:18,465 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:18,465 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,482 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:11:18,488 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:11:18,511 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:18,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,522 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:11:18,532 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:11:18,550 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:11:18,550 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,560 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:11:18,572 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:11:18,595 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:11:18,595 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,614 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:11:18,614 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:11:18,640 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:11:18,640 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,660 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:11:18,665 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:11:18,689 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,715 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:11:18,732 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:18,732 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,752 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:11:18,755 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:11:18,772 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,799 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:11:18,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:11:18,835 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:11:20,807 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 18:11:20,807 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 18:11:20,807 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 18:11:20,811 - root - INFO - Date ranges for each asset:
2025-06-26 18:11:20,811 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:11:20,811 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 18:11:20,817 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 18:11:20,817 - root - INFO -    Execution Method: candle_close
2025-06-26 18:11:20,817 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 18:11:20,817 - root - INFO -    Signal generated and executed immediately
2025-06-26 18:11:20,823 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 18:11:20,823 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 18:11:20,823 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 18:11:20,823 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,823 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:11:20,823 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 18:11:20,823 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,823 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 18:11:20,830 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 18:11:20,830 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 18:11:20,830 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,830 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:11:20,830 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:11:20,830 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 18:11:20,830 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,830 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,834 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:11:20,839 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 18:11:20,839 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 18:11:20,839 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 18:11:20,840 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,840 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:11:20,840 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:11:20,840 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 18:11:20,840 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 18:11:20,840 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 18:11:20,840 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 18:11:20,840 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,840 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:11:20,840 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:11:20,840 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 18:11:20,840 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 18:11:20,840 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 18:11:20,840 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 18:11:20,840 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,840 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:11:20,840 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:11:20,840 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 18:11:20,840 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 18:11:20,840 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 18:11:20,840 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 18:11:20,840 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,840 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:11:20,840 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:11:20,840 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 18:11:20,850 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 18:11:20,850 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 18:11:20,851 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 18:11:20,851 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,851 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:11:20,851 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:11:20,851 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 18:11:20,853 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 18:11:20,853 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 18:11:20,853 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 18:11:20,853 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,853 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:11:20,853 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:11:20,855 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 18:11:20,856 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 18:11:20,856 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 18:11:20,856 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 18:11:20,856 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,856 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:11:20,857 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:11:20,857 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 18:11:20,857 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 18:11:20,857 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 18:11:20,857 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 18:11:20,857 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,857 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:11:20,857 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 18:11:20,857 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 18:11:20,860 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 18:11:20,860 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 18:11:20,860 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 18:11:20,860 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,860 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 18:11:20,860 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:11:20,860 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 18:11:20,860 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 18:11:20,860 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 18:11:20,860 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 18:11:20,860 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,860 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:11:20,860 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 18:11:20,860 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 18:11:20,860 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 18:11:20,860 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 18:11:20,860 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 18:11:20,860 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,860 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 18:11:20,860 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:11:20,860 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:11:20,860 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 18:11:20,860 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 18:11:20,860 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 18:11:20,860 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,860 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:11:20,860 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:11:20,860 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 18:11:20,860 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 18:11:20,860 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 18:11:20,860 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 18:11:20,867 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,867 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:11:20,867 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:11:20,867 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 18:11:20,872 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 18:11:20,872 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 18:11:20,872 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 18:11:20,872 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,872 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:11:20,872 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 18:11:20,872 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 18:11:20,873 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 18:11:20,873 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 18:11:20,873 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 18:11:20,873 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,873 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 18:11:20,873 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 18:11:20,873 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 18:11:20,881 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 18:11:20,881 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 18:11:20,881 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 18:11:20,881 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,881 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 18:11:20,881 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:11:20,881 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:11:20,884 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 18:11:20,884 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 18:11:20,884 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 18:11:20,884 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,884 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:11:20,884 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:11:20,884 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 18:11:20,884 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 18:11:20,884 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 18:11:20,888 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 18:11:20,888 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,888 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:11:20,888 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:11:20,888 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:11:20,889 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 18:11:20,889 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 18:11:20,889 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 18:11:20,889 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,890 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:11:20,890 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:11:20,890 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 18:11:20,895 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 18:11:20,895 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 18:11:20,895 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 18:11:20,895 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:11:20,895 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:11:20,899 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:11:20,900 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 18:11:20,924 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 18:11:20,924 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:11:20,924 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:11:20,924 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:11:20,924 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:11:20,924 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:11:20,924 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 18:11:20,930 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:11:20,933 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 18:11:20,933 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 18:11:20,933 - root - INFO - Strategy execution completed in 0s
2025-06-26 18:11:20,933 - root - INFO - DEBUG: self.elapsed_time = 0.12674188613891602 seconds
2025-06-26 18:11:20,933 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 18:11:20,933 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 18:11:20,933 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 18:11:20,933 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 18:11:20,933 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 18:11:20,938 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 18:11:20,938 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 18:11:20,938 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 18:11:20,938 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 18:11:20,938 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 18:11:20,938 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 18:11:20,939 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 18:11:20,939 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 18:11:20,939 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 18:11:20,939 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 18:11:20,939 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 18:11:20,939 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 18:11:20,941 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,941 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,949 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,950 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,951 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,951 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,951 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,951 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:11:20,956 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 18:11:20,956 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 18:11:20,957 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 18:11:20,957 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 18:11:20,961 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 18:11:20,961 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 18:11:20,961 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 18:11:20,961 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 18:11:20,961 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 18:11:20,961 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 18:11:20,961 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 18:11:20,961 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 18:11:20,967 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 18:11:20,967 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 18:11:20,967 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 18:11:20,967 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 18:11:20,970 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 18:11:20,970 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 18:11:20,972 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 18:11:20,972 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 18:11:20,972 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 18:11:20,972 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 18:11:20,972 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 18:11:20,972 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 18:11:20,972 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 18:11:20,972 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 18:11:20,972 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 18:11:20,972 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 18:11:20,980 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:11:20,982 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:20,991 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 18:11:21,098 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:11:21,108 - root - INFO - Configuration loaded successfully.
2025-06-26 18:11:22,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:11:23,266 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 18:11:23,266 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 18:11:23,270 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,270 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,271 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,271 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,272 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,272 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,272 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:11:23,290 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 18:11:23,292 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:11:23,294 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 18:11:23,294 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 18:11:23,300 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 18:11:23,300 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - TIED USDT ASSETS: ['BTC/USDT', 'TRX/USDT'] (score: 12.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 12.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - TIED EUR ASSETS: ['BTC/EUR', 'TRX/EUR'] (score: 12.0)
2025-06-26 18:11:23,300 - root - ERROR - [DEBUG] CONVERSION - First EUR asset (should be selected): BTC/EUR
2025-06-26 18:11:23,305 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:11:23,310 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_181103.csv
2025-06-26 18:11:23,310 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_181103.csv
2025-06-26 18:11:23,310 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 18:11:23,310 - root - INFO - Results type: <class 'dict'>
2025-06-26 18:11:23,310 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 18:11:23,310 - root - INFO - Success flag set to: True
2025-06-26 18:11:23,310 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 18:11:23,313 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:11:23,314 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 18:11:23,314 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:11:23,314 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 18:11:23,316 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 18:11:23,316 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 18:11:23,316 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 18:11:23,316 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 18:11:23,316 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 18:11:23,316 - root - INFO -   - success: <class 'bool'>
2025-06-26 18:11:23,316 - root - INFO -   - message: <class 'str'>
2025-06-26 18:11:23,318 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 18:11:23,318 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 18:11:23,318 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:23,318 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: momentum
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 12.0
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] TIE DETECTED - 2 assets tied at score 12.0
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] TIE DETECTED - Tied assets: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:11:23,318 - root - ERROR - [DEBUG] TIE DETECTED - Current best_asset from series: TRX/EUR
2025-06-26 18:11:23,318 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-26 18:11:23,318 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:11:23,318 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:23,320 - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 12.0
2025-06-26 18:11:23,320 - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 12.0: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:11:23,320 - root - ERROR - [DEBUG] TIE DETECTED: 2 assets have the same maximum score of 12.0
2025-06-26 18:11:23,320 - root - ERROR - [DEBUG] Tied assets: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:11:23,320 - root - ERROR - [DEBUG] Dictionary iteration order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:23,320 - root - ERROR - [DEBUG] Tie-breaking: Selecting first asset in dictionary order: BTC/EUR
2025-06-26 18:11:23,320 - root - ERROR - [DEBUG] Complete asset ranking:
2025-06-26 18:11:23,320 - root - ERROR - [DEBUG]   1. BTC/EUR: 12.0 (TIED) <- SELECTED
2025-06-26 18:11:23,321 - root - ERROR - [DEBUG]   2. TRX/EUR: 12.0 (TIED)
2025-06-26 18:11:23,321 - root - ERROR - [DEBUG]   3. BNB/EUR: 11.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   4. XRP/EUR: 9.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   5. AAVE/EUR: 9.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   6. ETH/EUR: 8.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   7. SOL/EUR: 6.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   8. LINK/EUR: 6.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   9. AVAX/EUR: 3.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   10. ADA/EUR: 3.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   11. DOGE/EUR: 3.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   12. SUI/EUR: 2.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   13. DOT/EUR: 1.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG]   14. PEPE/EUR: 0.0
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG] SELECTED BEST ASSET: BTC/EUR (score: 12.0)
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG] MOMENTUM APPROACH - Using current day tie-breaking: BTC/EUR
2025-06-26 18:11:23,322 - root - ERROR - [DEBUG] TIE-BREAKING CHANGED SELECTION: TRX/EUR -> BTC/EUR
2025-06-26 18:11:23,350 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 18:11:23,350 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:11:23,350 - root - INFO - Single asset strategy with best asset: BTC/EUR
2025-06-26 18:11:23,350 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 18:11:23,350 - root - INFO - [DEBUG]   - Best asset selected: BTC/EUR
2025-06-26 18:11:23,350 - root - INFO - [DEBUG]   - Assets held: {'BTC/EUR': 1.0}
2025-06-26 18:11:23,350 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-26 18:11:23,350 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-26 18:11:23,355 - root - INFO - Executing single-asset strategy with best asset: BTC/EUR
2025-06-26 18:11:23,355 - root - INFO - Executing strategy signal: best_asset=BTC/EUR, mtpi_signal=1, mode=paper
2025-06-26 18:11:23,357 - root - INFO - Incremented daily trade counter for BTC/EUR: 1/5
2025-06-26 18:11:23,357 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: BTC/EUR
2025-06-26 18:11:23,357 - root - INFO - Attempting to enter position for BTC/EUR in paper mode
2025-06-26 18:11:23,357 - root - INFO - [DEBUG] TRADE - BTC/EUR: Starting enter_position attempt
2025-06-26 18:11:23,357 - root - INFO - [DEBUG] TRADE - BTC/EUR: Trading mode: paper
2025-06-26 18:11:23,357 - root - INFO - TRADE ATTEMPT - BTC/EUR: Getting current market price...
2025-06-26 18:11:23,357 - root - INFO - [DEBUG] PRICE - BTC/EUR: Starting get_current_price
2025-06-26 18:11:23,357 - root - INFO - [DEBUG] PRICE - BTC/EUR: Exchange ID: bitvavo
2025-06-26 18:11:23,357 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Initializing exchange bitvavo
2025-06-26 18:11:23,360 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange initialized successfully
2025-06-26 18:11:23,360 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange markets not loaded, loading now...
2025-06-26 18:11:23,670 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Symbol found after loading markets
2025-06-26 18:11:23,670 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Attempting to fetch ticker...
2025-06-26 18:11:23,710 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker fetched successfully
2025-06-26 18:11:23,710 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker data: {'symbol': 'BTC/EUR', 'timestamp': 1750954277220, 'datetime': '2025-06-26T16:11:17.220Z', 'high': 92720.0, 'low': 91111.0, 'bid': 91474.0, 'bidVolume': 0.01554966, 'ask': 91483.0, 'askVolume': 0.0354439, 'vwap': 91965.78679233725, 'open': 92270.0, 'close': 91515.0, 'last': 91515.0, 'previousClose': None, 'change': -755.0, 'percentage': -0.8182507857375094, 'average': 91892.5, 'baseVolume': 332.30009028, 'quoteVolume': 30560239.2537649, 'info': {'market': 'BTC-EUR', 'startTimestamp': 1750867877220, 'timestamp': 1750954277220, 'open': '9.227E+4', 'openTimestamp': 1750867903187, 'high': '9.272E+4', 'low': '91111', 'last': '91515', 'closeTimestamp': 1750954262859, 'bid': '91474', 'bidSize': '0.01554966', 'ask': '91483', 'askSize': '0.03544390', 'volume': '332.30009028', 'volumeQuote': '30560239.2537649'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 18:11:23,710 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Last price: 91515.0
2025-06-26 18:11:23,710 - root - INFO - [DEBUG] TRADE - BTC/EUR: get_current_price returned: 91515.0
2025-06-26 18:11:23,710 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price type: <class 'float'>
2025-06-26 18:11:23,710 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - not price: False
2025-06-26 18:11:23,710 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - price <= 0: False
2025-06-26 18:11:23,710 - root - INFO - TRADE ATTEMPT - BTC/EUR: Current price: 91515.********
2025-06-26 18:11:23,710 - root - INFO - Available balance for EUR: 100.********
2025-06-26 18:11:23,717 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 18:11:23,717 - root - INFO - Calculated position size for BTC/EUR: 0.******** (using 10% of 100, accounting for fees)
2025-06-26 18:11:23,717 - root - INFO - Calculated position size: 0.******** BTC
2025-06-26 18:11:23,717 - root - INFO - Entering position for BTC/EUR: 0.******** units at 91515.******** (value: 9.******** EUR)
2025-06-26 18:11:23,717 - root - INFO - Executing paper market buy order for BTC/EUR
2025-06-26 18:11:23,717 - root - INFO - Paper trading buy for BTC/EUR: original=0.********, adjusted=0.********, after_fee=0.********
2025-06-26 18:11:23,720 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:11:23,721 - root - INFO - Created paper market buy order: BTC/EUR, amount: 0.********353712506149, price: 91515.0
2025-06-26 18:11:23,721 - root - INFO - Filled amount: 0.******** BTC
2025-06-26 18:11:23,721 - root - INFO - Order fee: 0.******** EUR
2025-06-26 18:11:23,721 - root - INFO - Successfully entered position: BTC/EUR, amount: 0.********, price: 91515.********
2025-06-26 18:11:23,738 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91515.********, filled=0.********
2025-06-26 18:11:23,738 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:11:23,739 - root - INFO - TRADE SUCCESS - BTC/EUR: Successfully updated current asset
2025-06-26 18:11:23,744 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91515.********, filled=0.********
2025-06-26 18:11:23,744 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:11:23,744 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 18:11:23,750 - root - INFO - Trade executed: {'success': True, 'symbol': 'BTC/EUR', 'side': 'buy', 'amount': 0.0001087253455717642, 'price': 91515.0, 'order': {'id': 'paper-1750954283-BTC/EUR-buy-0.********353712506149', 'symbol': 'BTC/EUR', 'side': 'buy', 'type': 'market', 'amount': 0.********353712506149, 'price': 91515.0, 'cost': 9.90025********02, 'fee': {'cost': 0.****************01, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 0.********353712506149, 'remaining': 0, 'timestamp': 1750954283717, 'datetime': '2025-06-26T18:11:23.717354', 'trades': [], 'average': 91515.0, 'average_price': 91515.0}, 'filled_amount': 0.********353712506149, 'fee': {'cost': 0.****************01, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'BTC', 'timestamp': '2025-06-26T18:11:23.722931'}
2025-06-26 18:11:23,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:11:23,855 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 18:11:23,861 - root - INFO - Asset scores (sorted by score):
2025-06-26 18:11:23,861 - root - INFO -   BTC/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 18:11:23,861 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,863 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,864 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,865 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,867 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,868 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,868 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,868 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,869 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,869 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,869 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,870 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,870 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:11:23,871 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 18:11:23,871 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 18:11:23,874 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:11:23,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:11:23,932 - root - INFO - Strategy execution completed successfully in 20.71 seconds
2025-06-26 18:11:23,939 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 18:11:32,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:11:42,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:11:52,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:12:02,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:12:12,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:12:22,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:12:32,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:12:42,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:12:52,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:13:02,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:13:12,822 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:13:22,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:13:32,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:13:42,872 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:13:52,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:14:02,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:14:07,372 - root - INFO - Received signal 2, shutting down...
2025-06-26 18:14:12,928 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:14:17,382 - root - INFO - Network watchdog stopped
2025-06-26 18:14:17,382 - root - INFO - Network watchdog stopped
2025-06-26 18:14:17,382 - root - INFO - Background service stopped
2025-06-26 18:14:17,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
