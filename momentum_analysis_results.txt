=== COMPREHENSIVE MOMENTUM LOGIC ANALYSIS ===


[X] HOLD VIOLATION on 2024-01-06 00:00:00+00:00
   Allocation: SOL/USDT -> SOL/USDT
   Current scores: {'ETH/USDT': 1.0, 'BTC/USDT': 2.0, 'SOL/USDT': 3.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0}
   Previous scores: {'ETH/USDT': 1.0, 'BTC/USDT': 2.0, 'SOL/USDT': 4.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0}
Traceback (most recent call last):
  File "C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\analyze_momentum_logic.py", line 198, in <module>
    issues = analyze_momentum_logic(csv_file)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\analyze_momentum_logic.py", line 138, in analyze_momentum_logic
    print(f"   [!] VIOLATION: {violation_reason}")
  File "C:\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\u0394' in position 69: character maps to <undefined>
