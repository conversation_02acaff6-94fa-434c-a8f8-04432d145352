#!/usr/bin/env python3
"""
Debug script to verify the date indexing issue in momentum calculations.
This script will check if daily_scores_df.index[i-1] gives us the expected previous date.
"""

import pandas as pd
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_date_indexing():
    """Debug the date indexing issue in momentum calculations."""
    
    # Load the CSV file with momentum violations
    try:
        df = pd.read_csv('momentum_violations.csv')
        print(f"Loaded {len(df)} rows from momentum_violations.csv")
    except FileNotFoundError:
        print("ERROR: momentum_violations.csv not found. Please run analyze_momentum_logic.py first.")
        return
    
    # Focus on the first violation: 2024-01-06
    violation_date = '2024-01-06'
    violation_row = df[df['date'] == violation_date].iloc[0]
    
    print(f"\n=== DEBUGGING DATE INDEXING FOR {violation_date} ===")
    print(f"Expected previous date: 2024-01-05")
    print(f"SOL/USDT current score: {violation_row['scores_dict']['SOL/USDT']}")
    print(f"SUI/USDT current score: {violation_row['scores_dict']['SUI/USDT']}")
    
    # Create a mock daily_scores_df similar to what main_program.py would have
    # Get all dates from the CSV
    all_dates = pd.to_datetime(df['date']).sort_values()
    
    # Create scores data for all assets
    selected_assets = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']
    scores_data = {}
    
    for asset in selected_assets:
        scores_data[asset] = []
        for _, row in df.iterrows():
            scores_data[asset].append(row['scores_dict'].get(asset, 0))
    
    # Create the DataFrame with dates as index
    daily_scores_df = pd.DataFrame(scores_data, index=all_dates)
    
    print(f"\n=== DAILY_SCORES_DF INFO ===")
    print(f"Shape: {daily_scores_df.shape}")
    print(f"Index type: {type(daily_scores_df.index[0])}")
    print(f"Date range: {daily_scores_df.index.min()} to {daily_scores_df.index.max()}")
    
    # Find the violation date in the DataFrame
    violation_datetime = pd.to_datetime(violation_date)
    
    try:
        violation_idx = daily_scores_df.index.get_loc(violation_datetime)
        print(f"\nViolation date {violation_date} found at index: {violation_idx}")
    except KeyError:
        print(f"\nERROR: Violation date {violation_date} not found in DataFrame index!")
        return
    
    # Simulate the main_program.py logic
    print(f"\n=== SIMULATING MAIN_PROGRAM.PY LOGIC ===")
    i = violation_idx
    start_idx = 0
    
    print(f"i = {i}, start_idx = {start_idx}")
    print(f"Condition i > start_idx: {i > start_idx}")
    
    if i > start_idx:
        # This is the problematic line from main_program.py
        prev_score_date = daily_scores_df.index[i-1]
        print(f"prev_score_date from index[i-1]: {prev_score_date}")
        print(f"prev_score_date type: {type(prev_score_date)}")
        
        # What we EXPECT the previous date to be
        expected_prev_date = violation_datetime - timedelta(days=1)
        print(f"Expected previous date: {expected_prev_date}")
        
        # Check if they match
        dates_match = prev_score_date.date() == expected_prev_date.date()
        print(f"Dates match: {dates_match}")
        
        if not dates_match:
            print(f"🚨 DATE MISMATCH DETECTED!")
            print(f"   Index-based date: {prev_score_date.date()}")
            print(f"   Expected date: {expected_prev_date.date()}")
            print(f"   Difference: {(prev_score_date - expected_prev_date).days} days")
        
        # Get scores using the current (wrong) method
        print(f"\n=== SCORES USING CURRENT METHOD ===")
        prev_raw_scores = daily_scores_df.loc[prev_score_date]
        print(f"SOL/USDT previous score (current method): {prev_raw_scores['SOL/USDT']}")
        print(f"SUI/USDT previous score (current method): {prev_raw_scores['SUI/USDT']}")
        
        # Get scores using the correct method (if different)
        if not dates_match and expected_prev_date in daily_scores_df.index:
            print(f"\n=== SCORES USING CORRECT METHOD ===")
            correct_prev_scores = daily_scores_df.loc[expected_prev_date]
            print(f"SOL/USDT previous score (correct method): {correct_prev_scores['SOL/USDT']}")
            print(f"SUI/USDT previous score (correct method): {correct_prev_scores['SUI/USDT']}")
            
            # Calculate momentum with correct scores
            sol_current = violation_row['scores_dict']['SOL/USDT']
            sui_current = violation_row['scores_dict']['SUI/USDT']
            sol_prev_correct = correct_prev_scores['SOL/USDT']
            sui_prev_correct = correct_prev_scores['SUI/USDT']
            
            sol_momentum = sol_current - sol_prev_correct
            sui_momentum = sui_current - sui_prev_correct
            
            print(f"\n=== MOMENTUM CALCULATION WITH CORRECT SCORES ===")
            print(f"SOL/USDT: {sol_prev_correct} → {sol_current} (Δ={sol_momentum:+.1f})")
            print(f"SUI/USDT: {sui_prev_correct} → {sui_current} (Δ={sui_momentum:+.1f})")
            
            if sui_momentum > sol_momentum:
                print(f"✅ SUI/USDT has better momentum ({sui_momentum:+.1f} > {sol_momentum:+.1f})")
                print(f"✅ SUI/USDT should be selected (momentum approach)")
            else:
                print(f"❌ SOL/USDT has better momentum ({sol_momentum:+.1f} > {sui_momentum:+.1f})")
        
        # Show the DataFrame around the violation date
        print(f"\n=== DATAFRAME AROUND VIOLATION DATE ===")
        start_show = max(0, violation_idx - 2)
        end_show = min(len(daily_scores_df), violation_idx + 3)
        
        print(f"Showing rows {start_show} to {end_show-1}:")
        subset = daily_scores_df.iloc[start_show:end_show][['SOL/USDT', 'SUI/USDT']]
        for idx, (date, row) in enumerate(subset.iterrows(), start_show):
            marker = " <-- VIOLATION" if idx == violation_idx else ""
            marker += " <-- PREV (index-based)" if idx == violation_idx - 1 else ""
            print(f"  [{idx}] {date.date()}: SOL={row['SOL/USDT']}, SUI={row['SUI/USDT']}{marker}")

if __name__ == "__main__":
    debug_date_indexing()
