2025-06-26 17:04:03,262 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:04:04,194 - root - INFO - Loaded 35 trade records from logs/trades\trade_log_********.json
2025-06-26 17:04:04,195 - root - INFO - Loaded 22 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:04:04,195 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:04:05,597 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:05,604 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:05,605 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:05,616 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:05,616 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:05,625 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:05,625 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:04:05,627 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:04:06,913 - root - INFO - Telegram command handlers registered
2025-06-26 17:04:06,920 - root - INFO - Telegram bot polling started
2025-06-26 17:04:06,922 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:04:06,922 - root - INFO - Telegram notification channel initialized
2025-06-26 17:04:06,927 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:04:06,928 - root - INFO - Loaded 24 templates from file
2025-06-26 17:04:06,928 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:04:06,929 - root - INFO - Notification manager initialized
2025-06-26 17:04:06,929 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:04:06,929 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:04:06,929 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:04:06,930 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:04:06,931 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:04:06,936 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:04:06,936 - root - INFO - Recovery manager initialized
2025-06-26 17:04:06,936 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:06,948 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:06,949 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:06,949 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:06,949 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:06,949 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:06,949 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:06,949 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:06,950 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:06,950 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:06,950 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:06,958 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:06,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:04:07,011 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:04:07,012 - telegram.ext.Application - INFO - Application started
2025-06-26 17:04:07,306 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:04:07,306 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:07,306 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:07,306 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:07,307 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:07,307 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:07,313 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:07,316 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:07,325 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:07,326 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:07,332 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:07,332 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:04:07,333 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:04:07,333 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:04:07,333 - root - INFO - Trading mode: paper
2025-06-26 17:04:07,333 - root - INFO - Trading enabled: True
2025-06-26 17:04:07,333 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:07,333 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:07,334 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:07,334 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:07,334 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:07,341 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:07,531 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:04:07,532 - root - INFO - Trading enabled in paper mode
2025-06-26 17:04:07,533 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:04:07,533 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:04:07,533 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:04:07,533 - root - INFO - Generated run ID: ********_170407
2025-06-26 17:04:07,533 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:04:07,534 - root - INFO - Background service initialized
2025-06-26 17:04:07,534 - root - INFO - Network watchdog started
2025-06-26 17:04:07,535 - root - INFO - Network watchdog started
2025-06-26 17:04:07,537 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:04:07,537 - root - INFO - Background service started
2025-06-26 17:04:07,537 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:04:07,545 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:04:07,546 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:04:07,549 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:04:07,549 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:04:07,638 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:04:13,561 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:04:13,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:04:13,645 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:04:13,646 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:04:13,646 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:04:13,649 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:04:13,679 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,680 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,680 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,680 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:04:13,681 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,695 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,695 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,696 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,696 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 17:04:13,696 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,707 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,707 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,708 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,708 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:04:13,708 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,715 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,716 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,716 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,716 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:04:13,717 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,731 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,732 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,732 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,732 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:04:13,733 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,745 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,745 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,745 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,746 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:04:13,747 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,758 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,758 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,759 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,759 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:04:13,760 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,776 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,777 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,778 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,778 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:04:13,779 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,797 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,797 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,798 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,798 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:04:13,799 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,812 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,813 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,813 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,813 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:04:13,814 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,824 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,824 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,824 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,825 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:04:13,825 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,838 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,838 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,839 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,839 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:04:13,840 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,854 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,854 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,855 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,855 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:04:13,855 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,869 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:13,870 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,870 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:04:13,870 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:04:13,870 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:13,873 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 17:04:13,873 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:04:13,874 - root - INFO -   - Number of indicators: 8
2025-06-26 17:04:13,874 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:04:13,874 - root - INFO -   - Combination method: consensus
2025-06-26 17:04:13,874 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:04:13,874 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:04:13,946 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:04:13,946 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:04:13,946 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:04:13,946 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:04:13,946 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:04:13,946 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:04:13,947 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:04:13,947 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:13,953 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:13,953 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:13,959 - root - INFO - Configuration saved successfully.
2025-06-26 17:04:13,959 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:04:13,959 - root - INFO - Number of trend detection assets: 14
2025-06-26 17:04:13,959 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:04:13,960 - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:04:13,960 - root - INFO - Number of trading assets: 14
2025-06-26 17:04:13,960 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:04:14,186 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:14,195 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:14,202 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:14,209 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:14,209 - root - INFO - Execution context: backtesting
2025-06-26 17:04:14,209 - root - INFO - Execution timing: candle_close
2025-06-26 17:04:14,209 - root - INFO - Ratio calculation method: manual_inversion
2025-06-26 17:04:14,209 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:04:14,211 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:04:14,211 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:04:14,211 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:04:14,211 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:04:14,212 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:04:14,212 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:04:14,214 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,216 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,217 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,217 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,217 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,217 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,218 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,218 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,219 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,219 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,219 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,221 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,221 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,222 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:04:14,223 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 17:04:14,240 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,241 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,241 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,242 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:04:14,257 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,259 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,259 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,260 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:04:14,273 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,276 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,276 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,277 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:04:14,284 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,285 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,286 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,286 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:04:14,300 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,300 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,301 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,301 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:04:14,316 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,318 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,318 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,318 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:04:14,331 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,332 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,333 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,333 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:04:14,347 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,348 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,349 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,349 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:04:14,366 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,367 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,367 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,368 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:04:14,382 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,383 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,383 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,383 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:04:14,392 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,394 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,395 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,395 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:04:14,413 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,414 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,415 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,415 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:04:14,429 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,431 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,431 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,431 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:04:14,450 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,452 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:04:14,452 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,452 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:04:14,452 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:04:14,453 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,453 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,453 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,453 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,453 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,454 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,454 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,454 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,454 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,455 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,455 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,455 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,455 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,456 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:04:14,471 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:04:14,472 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:04:14,472 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:04:14,472 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:04:14,473 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:14,480 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:14,480 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:04:14,480 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:04:14,480 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:04:14,480 - root - INFO - Override: combination_method = consensus
2025-06-26 17:04:14,480 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:04:14,480 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:04:14,481 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:04:14,481 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:04:14,481 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:04:14,481 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:04:14,497 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:04:14,497 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:04:14,498 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:04:14,498 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:04:14,498 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:14,498 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:04:14,537 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:04:14,537 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:04:14,537 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:04:14,538 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:04:14,549 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:04:14,550 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:04:14,550 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:04:15,014 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:04:15,014 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:04:15,053 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:04:15,054 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:04:15,054 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:04:15,054 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:04:15,163 - root - INFO - Generated DPSD signals
2025-06-26 17:04:15,164 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:04:15,164 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:04:15,164 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:04:15,170 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:04:15,170 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:04:15,171 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:04:15,223 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:04:15,224 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:04:15,224 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:04:15,302 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:04:15,306 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:04:15,306 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:04:15,306 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:04:15,306 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:04:15,310 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:04:15,311 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:04:15,311 - root - INFO - Configuration saved successfully.
2025-06-26 17:04:15,311 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:04:15,311 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:15,331 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:15,331 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:04:15,331 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 17:04:15,331 - root - INFO - Using ratio calculation method: manual_inversion
2025-06-26 17:04:15,351 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,374 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,392 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:15,392 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,411 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:15,415 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,431 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:04:15,431 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,445 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:04:15,451 - root - INFO - Calculated ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,473 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:04:15,473 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,485 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:04:15,492 - root - INFO - Calculated ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,511 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:04:15,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,523 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:04:15,534 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,561 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:04:15,561 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,574 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:04:15,587 - root - INFO - Calculated ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,606 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:04:15,607 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,624 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:04:15,633 - root - INFO - Calculated ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,643 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:04:15,643 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,668 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:04:15,671 - root - INFO - Calculated ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,692 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:04:15,692 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,711 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:04:15,711 - root - INFO - Calculated ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,731 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:04:15,734 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,749 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:04:15,755 - root - INFO - Calculated ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,771 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:15,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,791 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:15,792 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,831 - root - INFO - Calculated ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,855 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:04:15,856 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,876 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:04:15,884 - root - INFO - Calculated ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,934 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:15,965 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:04:15,966 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:15,980 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:04:15,985 - root - INFO - Calculated ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,008 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:04:16,008 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,024 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:04:16,031 - root - INFO - Calculated ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,057 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:04:16,057 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,075 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:04:16,081 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,107 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:04:16,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,122 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:04:16,127 - root - INFO - Calculated ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,144 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:16,144 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,166 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:16,171 - root - INFO - Calculated ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,185 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:04:16,185 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,200 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:04:16,207 - root - INFO - Calculated ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,234 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:16,234 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,251 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:16,257 - root - INFO - Calculated ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,273 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:04:16,273 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,289 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:04:16,293 - root - INFO - Calculated ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,331 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,344 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,365 - root - INFO - Calculated ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,390 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:04:16,391 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,405 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:04:16,411 - root - INFO - Calculated ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,426 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:16,426 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,444 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:16,453 - root - INFO - Calculated ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,469 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:16,469 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,485 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:16,492 - root - INFO - Calculated ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,511 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:04:16,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,526 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:04:16,535 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,552 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:04:16,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,569 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:04:16,575 - root - INFO - Calculated ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,591 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:04:16,591 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,607 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:04:16,611 - root - INFO - Calculated ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,624 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:04:16,624 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,645 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:04:16,653 - root - INFO - Calculated ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,666 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:04:16,668 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,683 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:04:16,690 - root - INFO - Calculated ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,711 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:04:16,711 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,729 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:04:16,735 - root - INFO - Calculated ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,755 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:16,755 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,766 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:16,775 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,796 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:16,796 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,811 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:16,821 - root - INFO - Calculated ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,835 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:04:16,835 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,845 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:04:16,851 - root - INFO - Calculated ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,863 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,886 - root - INFO - Calculated ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,901 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:04:16,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,916 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:04:16,922 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,957 - root - INFO - Calculated ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:16,974 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:16,991 - root - INFO - Calculated ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,011 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,029 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,035 - root - INFO - Calculated ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,065 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,066 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:04:17,101 - root - INFO - Calculated ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,127 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,150 - root - INFO - Calculated ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,168 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:17,168 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,186 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:17,190 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,223 - root - INFO - Calculated ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,249 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,266 - root - INFO - Calculated ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,281 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,295 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,300 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,311 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:04:17,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,331 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:04:17,331 - root - INFO - Calculated ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,351 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:04:17,351 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,361 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:04:17,370 - root - INFO - Calculated ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,385 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,385 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,398 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,403 - root - INFO - Calculated ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,421 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:04:17,421 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,433 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:04:17,442 - root - INFO - Calculated ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,461 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,475 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:04:17,481 - root - INFO - Calculated ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,518 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,551 - root - INFO - Calculated ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,566 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:04:17,568 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,574 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:04:17,583 - root - INFO - Calculated ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,592 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:17,601 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,613 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:17,618 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,634 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:17,635 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,646 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:17,654 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,671 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:04:17,671 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,683 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:04:17,690 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,727 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,755 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,787 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,802 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:04:17,802 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,817 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:04:17,821 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,836 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,858 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,876 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:17,876 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,889 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:17,894 - root - INFO - Calculated ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,933 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,951 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:04:17,951 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:17,972 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:04:17,974 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:17,994 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,011 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,029 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,047 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,062 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,076 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,095 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,115 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,140 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,161 - root - INFO - Calculated ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,194 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:04:18,197 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,231 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:04:18,252 - root - INFO - Calculated ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,288 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,343 - root - INFO - Calculated ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,396 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:04:18,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,501 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:04:18,559 - root - INFO - Calculated ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,643 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:18,681 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,741 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:04:18,779 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,837 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:18,911 - root - INFO - Calculated ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:18,958 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:18,971 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,015 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:19,068 - root - INFO - Calculated ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:19,143 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,212 - root - INFO - Calculated ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:19,287 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:19,327 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,394 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:04:19,457 - root - INFO - Calculated ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:19,513 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,606 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:19,661 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,731 - root - INFO - Calculated ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:19,783 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,838 - root - INFO - Calculated ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:19,885 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:19,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:19,940 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:04:19,967 - root - INFO - Calculated ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:20,027 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:20,095 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:20,162 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:20,223 - root - INFO - Calculated ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:20,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:20,413 - root - INFO - Calculated ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:20,538 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:20,721 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:20,820 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:20,986 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:21,098 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:21,175 - root - INFO - Calculated ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:21,260 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:21,315 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:21,403 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:04:21,413 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:21,504 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:04:21,598 - root - INFO - Calculated ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:21,649 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:04:21,719 - root - INFO - Calculated ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data (manual inversion)
2025-06-26 17:04:25,731 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:04:25,731 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 17:04:25,731 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:04:25,735 - root - INFO - Date ranges for each asset:
2025-06-26 17:04:25,735 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,735 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,735 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,735 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,737 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,738 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,738 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,738 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,739 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:04:25,739 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:04:25,745 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:04:25,745 - root - INFO -    Execution Method: candle_close
2025-06-26 17:04:25,745 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:04:25,745 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:04:25,751 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,751 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,751 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,752 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,754 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:04:25,754 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:04:25,754 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:04:25,754 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,754 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:04:25,754 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:04:25,755 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,755 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,756 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,756 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,757 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,757 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,758 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,761 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,765 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,766 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,766 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,766 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,766 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,766 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:04:25,766 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:04:25,766 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:04:25,766 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,766 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:04:25,766 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:04:25,766 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:04:25,770 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,770 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,770 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,770 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,770 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,771 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,771 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,771 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,771 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,772 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,772 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,772 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,774 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,778 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,781 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,781 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 0.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,783 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:04:25,789 - root - INFO - ASSET CHANGE DETECTED on 2025-02-25:
2025-06-26 17:04:25,790 - root - INFO -    Signal Date: 2025-02-24 (generated at 00:00 UTC)
2025-06-26 17:04:25,791 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-25 00:00 UTC (immediate)
2025-06-26 17:04:25,791 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,791 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:04:25,791 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:04:25,793 - root - INFO -    TRX/USDT buy price: $0.2309 (close price)
2025-06-26 17:04:25,798 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:04:25,798 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:04:25,798 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:04:25,798 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,798 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:04:25,798 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:04:25,798 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:04:25,802 - root - INFO - ASSET CHANGE DETECTED on 2025-03-10:
2025-06-26 17:04:25,802 - root - INFO -    Signal Date: 2025-03-09 (generated at 00:00 UTC)
2025-06-26 17:04:25,802 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-10 00:00 UTC (immediate)
2025-06-26 17:04:25,802 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,802 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:04:25,802 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:04:25,802 - root - INFO -    TRX/USDT buy price: $0.2292 (close price)
2025-06-26 17:04:25,806 - root - INFO - ASSET CHANGE DETECTED on 2025-03-16:
2025-06-26 17:04:25,809 - root - INFO -    Signal Date: 2025-03-15 (generated at 00:00 UTC)
2025-06-26 17:04:25,809 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-16 00:00 UTC (immediate)
2025-06-26 17:04:25,811 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,811 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:04:25,811 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:04:25,811 - root - INFO -    ADA/USDT buy price: $0.7049 (close price)
2025-06-26 17:04:25,813 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:04:25,813 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:04:25,813 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:04:25,813 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,813 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:04:25,813 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:04:25,813 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:04:25,819 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:04:25,819 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:04:25,819 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:04:25,819 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,819 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:04:25,819 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:04:25,819 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:04:25,825 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:04:25,825 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:04:25,825 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:04:25,825 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,827 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:04:25,827 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:04:25,828 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:04:25,830 - root - INFO - ASSET CHANGE DETECTED on 2025-04-04:
2025-06-26 17:04:25,831 - root - INFO -    Signal Date: 2025-04-03 (generated at 00:00 UTC)
2025-06-26 17:04:25,831 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-04 00:00 UTC (immediate)
2025-06-26 17:04:25,831 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,831 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:04:25,831 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:04:25,831 - root - INFO -    TRX/USDT buy price: $0.2390 (close price)
2025-06-26 17:04:25,836 - root - INFO - ASSET CHANGE DETECTED on 2025-04-20:
2025-06-26 17:04:25,836 - root - INFO -    Signal Date: 2025-04-19 (generated at 00:00 UTC)
2025-06-26 17:04:25,836 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-20 00:00 UTC (immediate)
2025-06-26 17:04:25,836 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,836 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:04:25,836 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:04:25,836 - root - INFO -    SOL/USDT buy price: $137.8600 (close price)
2025-06-26 17:04:25,841 - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-06-26 17:04:25,841 - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-06-26 17:04:25,841 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-06-26 17:04:25,842 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,842 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:04:25,842 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:04:25,842 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:04:25,844 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:04:25,844 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:04:25,844 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:04:25,844 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,844 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:04:25,844 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:04:25,844 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:04:25,853 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:04:25,858 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:04:25,859 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:04:25,859 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,859 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:04:25,860 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:04:25,860 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:04:25,868 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:04:25,869 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:04:25,869 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:04:25,869 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,870 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:04:25,870 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:04:25,870 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:04:25,871 - root - INFO - ASSET CHANGE DETECTED on 2025-05-22:
2025-06-26 17:04:25,871 - root - INFO -    Signal Date: 2025-05-21 (generated at 00:00 UTC)
2025-06-26 17:04:25,875 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-22 00:00 UTC (immediate)
2025-06-26 17:04:25,875 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,875 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:04:25,875 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:04:25,875 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:04:25,880 - root - INFO - ASSET CHANGE DETECTED on 2025-05-26:
2025-06-26 17:04:25,880 - root - INFO -    Signal Date: 2025-05-25 (generated at 00:00 UTC)
2025-06-26 17:04:25,880 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-26 00:00 UTC (immediate)
2025-06-26 17:04:25,880 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,880 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:04:25,880 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:04:25,881 - root - INFO -    AAVE/USDT buy price: $267.5200 (close price)
2025-06-26 17:04:25,892 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:04:25,892 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:04:25,892 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:04:25,892 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:04:25,892 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:04:25,892 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:04:25,892 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:04:25,933 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:04:25,933 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:04:25,933 - root - INFO - Swap trade at 2025-02-25 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:04:25,933 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:04:25,933 - root - INFO - Swap trade at 2025-03-10 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:04:25,933 - root - INFO - Swap trade at 2025-03-16 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:04:25,933 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: BNB/USDT -> PEPE/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-04-04 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-04-20 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-04-23 00:00:00+00:00: SOL/USDT -> PEPE/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: PEPE/USDT -> SUI/USDT
2025-06-26 17:04:25,935 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:04:25,937 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:04:25,937 - root - INFO - Swap trade at 2025-05-22 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:04:25,937 - root - INFO - Swap trade at 2025-05-26 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:04:25,937 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:04:25,937 - root - INFO - Total trades: 18 (Entries: 1, Exits: 0, Swaps: 17)
2025-06-26 17:04:25,939 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:04:25,941 - root - INFO - DEBUG: self.elapsed_time = 0.20821452140808105 seconds
2025-06-26 17:04:25,944 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:04:25,944 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:04:25,944 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:04:25,944 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:04:25,944 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,944 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,952 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,954 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,958 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,961 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,963 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,963 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,963 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,967 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,968 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,968 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,971 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,973 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:04:25,975 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:04:25,977 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:04:25,977 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 17:04:25,979 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 17:04:25,981 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:04:25,981 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:04:25,981 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:04:25,981 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:04:25,981 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:04:25,985 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:04:25,986 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:04:25,986 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:04:25,991 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:04:25,991 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:04:25,994 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:04:25,994 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:04:25,996 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:04:25,996 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:04:25,998 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:04:25,998 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:04:25,998 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:04:25,998 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:04:26,001 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:04:26,001 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:04:26,001 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:04:26,006 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:04:26,009 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:04:26,009 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:04:26,011 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:26,018 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:26,031 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:04:26,131 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:26,141 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:27,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:04:27,743 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,743 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,743 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,743 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,743 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:04:27,751 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 17:04:27,751 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,751 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:04:27,767 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:04:27,767 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:04:27,772 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:04:27,772 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:04:27,775 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:04:27,775 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 7.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:04:27,817 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 13.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:04:27,833 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_170407.csv
2025-06-26 17:04:27,833 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_170407.csv
2025-06-26 17:04:27,833 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:04:27,833 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:04:27,833 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:04:27,833 - root - INFO - Success flag set to: True
2025-06-26 17:04:27,833 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:04:27,833 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:04:27,833 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 17:04:27,833 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:04:27,833 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:04:27,833 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:04:27,833 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:04:27,833 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:04:27,833 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:04:27,833 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:04:27,839 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:04:27,839 - root - INFO -   - message: <class 'str'>
2025-06-26 17:04:27,839 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:04:27,871 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 17:04:27,871 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:04:27,871 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:04:27,883 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:04:27,883 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:04:27,883 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:04:27,883 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:04:27,883 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:04:27,883 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:04:28,129 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:04:28,131 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:04:28,131 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:04:28,134 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 17:04:28,134 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 17:04:28,134 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:04:28,134 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:04:28,134 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:04:28,134 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:04:28,134 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.********2391215, price: 0.2321
2025-06-26 17:04:28,136 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:04:28,136 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:04:28,136 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:04:28,149 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:04:28,151 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:04:28,151 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:04:28,160 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:04:28,161 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:04:28,161 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:04:28,161 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.********205945, 'price': 0.2321, 'order': {'id': 'paper-1750950268-TRX/EUR-buy-42.********2391215', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.********2391215, 'price': 0.2321, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.********2391215, 'remaining': 0, 'timestamp': 1750950268134, 'datetime': '2025-06-26T17:04:28.134881', 'trades': [], 'average': 0.2321, 'average_price': 0.2321}, 'filled_amount': 42.********2391215, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:04:28.137580'}
2025-06-26 17:04:28,246 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:04:28,261 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:04:28,261 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:04:28,261 - root - INFO -   TRX/EUR: score=13.0, status=SELECTED, weight=1.00
2025-06-26 17:04:28,262 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,262 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,263 - root - INFO -   AAVE/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,263 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,263 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,263 - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   ADA/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:04:28,264 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 17:04:28,265 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:04:28,265 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 10.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 4.0, 'LINK/EUR': 7.0, 'TRX/EUR': 13.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:04:28,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:04:28,304 - root - INFO - Strategy execution completed successfully in 20.77 seconds
2025-06-26 17:04:28,308 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:04:37,126 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:04:37,976 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:04:47,149 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:04:47,998 - root - INFO - Network watchdog stopped
2025-06-26 17:04:47,999 - root - INFO - Network watchdog stopped
2025-06-26 17:04:47,999 - root - INFO - Background service stopped
2025-06-26 17:04:48,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
