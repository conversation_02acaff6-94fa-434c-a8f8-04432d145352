2025-06-26 17:04:54,064 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:04:54,728 - root - INFO - Loaded 37 trade records from logs/trades\trade_log_********.json
2025-06-26 17:04:54,729 - root - INFO - Loaded 23 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:04:54,729 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:04:55,694 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:55,703 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:55,709 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:55,716 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:55,716 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:55,724 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:55,724 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:04:55,724 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:04:56,576 - root - INFO - Telegram command handlers registered
2025-06-26 17:04:56,576 - root - INFO - Telegram bot polling started
2025-06-26 17:04:56,576 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:04:56,576 - root - INFO - Telegram notification channel initialized
2025-06-26 17:04:56,576 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:04:56,576 - root - INFO - Loaded 24 templates from file
2025-06-26 17:04:56,576 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:04:56,581 - root - INFO - Notification manager initialized
2025-06-26 17:04:56,581 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:04:56,581 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:04:56,581 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:04:56,581 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:04:56,586 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:04:56,590 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:04:56,590 - root - INFO - Recovery manager initialized
2025-06-26 17:04:56,590 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:56,594 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:56,594 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:56,594 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:56,594 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:56,594 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:56,594 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:56,594 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:56,594 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:56,594 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:56,594 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:56,606 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:56,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:04:56,641 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:04:56,641 - telegram.ext.Application - INFO - Application started
2025-06-26 17:04:56,926 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:04:56,926 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:56,926 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:56,926 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:56,926 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:56,926 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:56,936 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:56,939 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:56,946 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:56,946 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:04:56,951 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:56,951 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:04:56,955 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:04:56,955 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:04:56,955 - root - INFO - Trading mode: paper
2025-06-26 17:04:56,955 - root - INFO - Trading enabled: True
2025-06-26 17:04:56,956 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:04:56,956 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:04:56,956 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:04:56,956 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:04:56,957 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:04:56,961 - root - INFO - Configuration loaded successfully.
2025-06-26 17:04:57,161 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:04:57,161 - root - INFO - Trading enabled in paper mode
2025-06-26 17:04:57,165 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:04:57,165 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:04:57,165 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:04:57,165 - root - INFO - Generated run ID: ********_170457
2025-06-26 17:04:57,165 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:04:57,165 - root - INFO - Background service initialized
2025-06-26 17:04:57,165 - root - INFO - Network watchdog started
2025-06-26 17:04:57,165 - root - INFO - Network watchdog started
2025-06-26 17:04:57,165 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:04:57,165 - root - INFO - Background service started
2025-06-26 17:04:57,165 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:04:57,170 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:04:57,170 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:04:57,171 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:04:57,172 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:04:57,237 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:05:03,191 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:05:03,291 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:05:03,291 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:05:03,291 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:05:03,291 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:05:03,296 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:05:03,327 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,329 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,329 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,330 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:05:03,330 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,345 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,345 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,345 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,345 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 17:05:03,346 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,355 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,355 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,355 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,355 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:05:03,356 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,364 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,364 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,365 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,365 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:05:03,365 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,378 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,379 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,379 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,379 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:05:03,380 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,389 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,389 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,391 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,391 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:05:03,392 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,401 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,402 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,402 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,402 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:05:03,403 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,422 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,423 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,423 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,423 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:05:03,424 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,438 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,439 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,439 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,440 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:05:03,441 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,453 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,454 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,455 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,455 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:05:03,456 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,462 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,463 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,464 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,464 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:05:03,464 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,477 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,477 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,478 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,478 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:05:03,479 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,490 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,491 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,491 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,492 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:05:03,493 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,505 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,506 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,506 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,507 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:05:03,507 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,509 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 17:05:03,509 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:05:03,509 - root - INFO -   - Number of indicators: 8
2025-06-26 17:05:03,509 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:05:03,511 - root - INFO -   - Combination method: consensus
2025-06-26 17:05:03,511 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:05:03,511 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:05:03,516 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:05:03,516 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:05:03,517 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:05:03,517 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:05:03,517 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:05:03,517 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:05:03,517 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:05:03,517 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:05:03,524 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:03,524 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:05:03,529 - root - INFO - Configuration saved successfully.
2025-06-26 17:05:03,529 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:05:03,530 - root - INFO - Number of trend detection assets: 14
2025-06-26 17:05:03,530 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:05:03,530 - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:05:03,530 - root - INFO - Number of trading assets: 14
2025-06-26 17:05:03,530 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:05:03,697 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:05:03,704 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:03,712 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:05:03,718 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:03,718 - root - INFO - Execution context: backtesting
2025-06-26 17:05:03,718 - root - INFO - Execution timing: candle_close
2025-06-26 17:05:03,719 - root - INFO - Ratio calculation method: independent
2025-06-26 17:05:03,719 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:05:03,719 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:05:03,719 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:05:03,719 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:05:03,719 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:05:03,720 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:05:03,720 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:05:03,720 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,721 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,722 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,722 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,722 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,723 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,723 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,724 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,724 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,725 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,725 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,725 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,725 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,726 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:05:03,726 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 17:05:03,740 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,742 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,743 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,743 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:05:03,754 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,755 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,756 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,756 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:05:03,768 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,769 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,770 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,771 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:05:03,778 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,779 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,780 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,780 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:05:03,793 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,795 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,795 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,796 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:05:03,805 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,807 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,807 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,808 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:05:03,819 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,820 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,820 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,821 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:05:03,836 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,837 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,837 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,838 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:05:03,850 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,851 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,851 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,851 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:05:03,865 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,867 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,867 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,867 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:05:03,873 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,875 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,877 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,877 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:05:03,889 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,890 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,891 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,891 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:05:03,904 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,906 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,906 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,907 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:05:03,919 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,920 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:05:03,920 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,920 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:05:03,921 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:05:03,921 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,921 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,922 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,922 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,922 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,923 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,923 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,923 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,923 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,923 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,924 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,924 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,924 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,924 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:05:03,937 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:05:03,938 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:05:03,938 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:05:03,938 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:05:03,938 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:05:03,945 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:03,946 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:05:03,946 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:05:03,946 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:05:03,946 - root - INFO - Override: combination_method = consensus
2025-06-26 17:05:03,947 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:05:03,947 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:05:03,947 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:05:03,947 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:05:03,948 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:05:03,948 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:05:03,961 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:05:03,962 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:05:03,962 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:05:03,962 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:05:03,962 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:03,963 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:05:03,984 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:05:03,984 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:05:03,985 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:05:03,985 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:05:03,993 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:05:03,993 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:05:03,994 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:05:04,309 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:05:04,309 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:05:04,346 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:05:04,347 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:05:04,347 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:05:04,347 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:05:04,425 - root - INFO - Generated DPSD signals
2025-06-26 17:05:04,425 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:05:04,426 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:05:04,426 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:05:04,433 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:05:04,433 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:05:04,434 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:05:04,484 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:05:04,484 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:05:04,484 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:05:04,565 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:05:04,570 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:05:04,571 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:05:04,571 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:05:04,572 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:05:04,572 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:05:04,574 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:05:04,580 - root - INFO - Configuration saved successfully.
2025-06-26 17:05:04,580 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:05:04,580 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:05:04,587 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:04,587 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:05:04,587 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 17:05:04,588 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:05:04,603 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,621 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:04,637 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:04,637 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,650 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:04,656 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:04,672 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:05:04,672 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,687 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:05:04,693 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:04,708 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:04,708 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,721 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:04,725 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:04,742 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:04,742 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,755 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:04,760 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:04,776 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:05:04,777 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,788 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:05:04,794 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:04,809 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:04,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,823 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:04,828 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:04,842 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:05:04,844 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,855 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:05:04,861 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:04,876 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:05:04,877 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,890 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:05:04,894 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:04,909 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:04,909 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,922 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:04,928 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:04,941 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:04,943 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,955 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:04,961 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:04,973 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:04,991 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:05,006 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:05:05,009 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,022 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:05:05,027 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:05,041 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:05,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,056 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:05,061 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:05,072 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,096 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:05,111 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:05,111 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,123 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:05,127 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:05,141 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:05,141 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,156 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:05,161 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:05,173 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:05,173 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,193 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:05,193 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:05,211 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:05:05,213 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,231 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:05:05,233 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:05,249 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:05,251 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,262 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:05,266 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:05,282 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:05,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,291 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:05,298 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:05,311 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:05,315 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,328 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:05,332 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:05,345 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:05,345 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,356 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:05,365 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:05,379 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,391 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:05,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,431 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:05,441 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:05:05,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,456 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:05:05,461 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:05,473 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:05:05,473 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,491 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:05:05,491 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:05,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,528 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:05,547 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:05,547 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,571 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:05,578 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:05,609 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:05,609 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,631 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:05,641 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:05,671 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:05:05,671 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,695 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:05:05,701 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:05,719 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:05:05,719 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,732 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:05:05,741 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:05,761 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:05,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,773 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:05,782 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:05,799 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:05,800 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,811 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:05,815 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:05,831 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:05:05,833 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,841 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:05:05,849 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:05,861 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:05:05,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,882 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:05:05,885 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:05,906 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:05,906 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:05,926 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:05,934 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:05,967 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:05,985 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:06,029 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:06,070 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:06,115 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:06,149 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:06,227 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:06,283 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:06,366 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:06,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:06,474 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:06,536 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:06,628 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:06,680 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:06,684 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:05:06,737 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:06,816 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:06,894 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:06,978 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:07,106 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:05:07,132 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:07,191 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:05:07,253 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:07,306 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:07,366 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:07,450 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:07,523 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:07,611 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:07,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:07,750 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:07,835 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:07,909 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:07,963 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:08,003 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:08,055 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:08,136 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:08,165 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:08,220 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:08,251 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:08,310 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:08,378 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:08,444 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:08,515 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:08,585 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:05:08,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:08,750 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:05:08,835 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:08,909 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:09,033 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:09,094 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:09,311 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:09,448 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:09,468 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:09,530 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:09,559 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:09,596 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:09,597 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:09,670 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:09,703 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:09,778 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:05:09,790 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:09,849 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:05:09,883 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:10,004 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:05:10,031 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,154 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:05:10,216 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:10,267 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:10,271 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,317 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:10,349 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:10,395 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:10,397 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,427 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:10,436 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:10,497 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:10,500 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,536 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:05:10,570 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:10,625 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,670 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:10,714 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,760 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:10,789 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:05:10,790 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,815 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:05:10,824 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:10,862 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,902 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:10,936 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:10,986 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:11,015 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,055 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:11,097 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,143 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:11,177 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,220 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:11,258 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:11,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,302 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:11,313 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:11,353 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:11,353 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,386 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:11,396 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:11,428 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:11,428 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,457 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:11,467 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:11,499 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,533 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:11,614 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,657 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:11,693 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:05:11,694 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,722 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:05:11,731 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:11,775 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,810 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:11,845 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:11,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,888 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:11,899 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:11,940 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:11,987 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:12,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,079 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:12,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,147 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:12,179 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,214 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:12,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,281 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:12,311 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:05:12,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,339 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:05:12,347 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:12,382 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,421 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:12,450 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:05:12,450 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,480 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:05:12,490 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:12,524 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,560 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:12,598 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,639 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:12,674 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,711 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:12,745 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,781 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:12,815 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,855 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:12,896 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:12,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:12,923 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:12,933 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:12,965 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,008 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:13,043 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,084 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:13,148 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:05:13,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,184 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:05:13,199 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:13,233 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,317 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:13,353 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:05:13,353 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,395 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:05:13,404 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:13,463 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:13,467 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,573 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:13,685 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:13,853 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:05:13,871 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:13,905 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:05:13,914 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:13,951 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,004 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:14,039 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:14,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,072 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:14,081 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:14,128 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:14,128 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,159 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:14,169 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:14,213 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,307 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:14,372 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:14,377 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,413 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:14,424 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:14,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,501 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:14,545 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,613 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:14,694 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,780 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:14,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,898 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:14,927 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:14,959 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:14,990 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:05:14,991 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,029 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:05:15,039 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:15,069 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,110 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:15,135 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,174 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:15,207 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,246 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:15,293 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:15,294 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,326 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:15,337 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:15,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,417 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:15,450 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,505 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:15,634 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,679 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:15,708 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,750 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:15,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,841 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:15,925 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:15,925 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:15,961 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:15,971 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:16,018 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,114 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:16,159 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:16,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,193 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:16,206 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:16,255 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:16,265 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,315 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:16,333 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:16,377 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:16,379 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,406 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:16,417 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:16,451 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,486 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:16,515 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:16,515 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,535 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:16,546 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:16,574 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:16,576 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,603 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:16,611 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:16,646 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,680 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:16,711 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,759 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:05:16,768 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:16,827 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,865 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:16,898 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,931 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:16,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:16,995 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:17,025 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,060 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:17,089 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,133 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:17,182 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,215 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:17,245 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:05:17,245 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,275 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:05:17,281 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:17,317 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:17,317 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,343 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:05:17,351 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:17,389 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:17,390 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,419 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:05:17,429 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:17,462 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:05:17,463 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,491 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:05:17,497 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:17,526 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,557 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:17,581 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,605 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:17,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,706 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:17,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,806 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:17,864 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:17,913 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:17,957 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,014 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:18,040 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,078 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:18,107 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:18,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,138 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:18,149 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:18,230 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:18,232 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,273 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:05:18,285 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:18,321 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:18,322 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,347 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:18,357 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:18,380 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:05:18,381 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,405 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:05:18,412 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:18,440 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:05:18,440 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,459 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:05:18,463 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:18,491 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:18,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,507 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:18,513 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:18,534 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:05:18,534 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,558 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:05:18,565 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:18,583 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:05:18,589 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,609 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:05:18,615 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:18,639 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,667 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:18,689 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:18,690 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,711 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:18,719 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:18,748 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,778 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:18,801 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,832 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:18,859 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:18,860 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,883 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:18,892 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:18,918 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:18,919 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,944 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:18,951 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:18,976 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:18,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:18,999 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:05:19,008 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:19,032 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:05:19,032 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,048 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:05:19,051 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:19,077 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:05:19,077 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,093 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:05:19,101 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:19,124 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:19,124 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,142 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:19,147 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:19,173 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:05:19,173 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,193 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:05:19,199 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:19,225 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:19,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,247 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:05:19,257 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:19,281 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:19,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,371 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:19,409 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:19,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,505 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:19,529 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,558 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:05:19,583 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,610 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:05:19,633 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,665 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:05:19,683 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,712 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:05:19,735 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:19,735 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,756 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:05:19,761 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:05:19,799 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:19,799 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,826 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:05:19,835 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:05:19,862 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:19,862 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,889 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:05:19,897 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:05:19,925 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:05:19,925 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:19,951 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:05:19,963 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:05:19,995 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:05:19,995 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:20,017 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:05:20,024 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:05:20,049 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:05:20,050 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:20,068 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:05:20,076 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:05:20,108 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:20,133 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:05:20,158 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:20,158 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:20,175 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:05:20,184 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:05:20,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:20,241 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:05:20,267 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:05:20,291 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:05:23,253 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:05:23,253 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 17:05:23,253 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:05:23,253 - root - INFO - Date ranges for each asset:
2025-06-26 17:05:23,253 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,253 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,260 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:05:23,261 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:05:23,266 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:05:23,266 - root - INFO -    Execution Method: candle_close
2025-06-26 17:05:23,268 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:05:23,268 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:05:23,278 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,278 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,278 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,278 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,278 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,281 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,283 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:05:23,283 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:05:23,284 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:05:23,284 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,284 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:05:23,284 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:05:23,284 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,284 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,284 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,284 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,284 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,284 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,284 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,287 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,287 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,287 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,288 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,288 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,288 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,289 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,290 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,290 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,290 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,291 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,291 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,291 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,291 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,291 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,292 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:05:23,292 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:05:23,292 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:05:23,294 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,294 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:05:23,294 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:05:23,294 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:05:23,294 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,294 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,300 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,300 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,300 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,300 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,301 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:05:23,302 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 17:05:23,304 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 17:05:23,304 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 17:05:23,304 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,304 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:05:23,304 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:05:23,304 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 17:05:23,308 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 17:05:23,309 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 17:05:23,309 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 17:05:23,309 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,309 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:05:23,309 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:05:23,309 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 17:05:23,311 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 17:05:23,311 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 17:05:23,311 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 17:05:23,311 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,311 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:05:23,311 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:05:23,311 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 17:05:23,311 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:05:23,311 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:05:23,311 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:05:23,311 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,311 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:05:23,311 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:05:23,311 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:05:23,311 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 17:05:23,311 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 17:05:23,311 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 17:05:23,311 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,311 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:05:23,320 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:05:23,320 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 17:05:23,323 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 17:05:23,323 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 17:05:23,323 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 17:05:23,323 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,324 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:05:23,324 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:05:23,324 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 17:05:23,325 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:05:23,327 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:05:23,327 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:05:23,328 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,328 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:05:23,328 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:05:23,328 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:05:23,328 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:05:23,328 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:05:23,328 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:05:23,328 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,331 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:05:23,331 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:05:23,331 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:05:23,331 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:05:23,331 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:05:23,331 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:05:23,331 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,331 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:05:23,331 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:05:23,331 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:05:23,333 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:05:23,333 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:05:23,333 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:05:23,333 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,333 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:05:23,333 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:05:23,333 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:05:23,333 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:05:23,333 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:05:23,333 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:05:23,333 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,333 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:05:23,333 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:05:23,333 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:05:23,339 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:05:23,340 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:05:23,341 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:05:23,341 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,341 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:05:23,341 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:05:23,342 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:05:23,344 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 17:05:23,345 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 17:05:23,345 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 17:05:23,345 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,345 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:05:23,345 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:05:23,345 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 17:05:23,351 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 17:05:23,351 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 17:05:23,351 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 17:05:23,351 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,351 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:05:23,352 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:05:23,352 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 17:05:23,354 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:05:23,354 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:05:23,354 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:05:23,355 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,355 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:05:23,355 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:05:23,355 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:05:23,362 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:05:23,362 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:05:23,362 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:05:23,363 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,363 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:05:23,363 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:05:23,363 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:05:23,368 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:05:23,368 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:05:23,371 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:05:23,371 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,371 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:05:23,372 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:05:23,372 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:05:23,375 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:05:23,376 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:05:23,376 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:05:23,377 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,377 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:05:23,377 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:05:23,377 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:05:23,379 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:05:23,379 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:05:23,379 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:05:23,379 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,379 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:05:23,379 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:05:23,379 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:05:23,391 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:05:23,391 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:05:23,392 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:05:23,392 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:05:23,392 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:05:23,392 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:05:23,392 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:05:23,435 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:05:23,436 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:05:23,436 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:05:23,436 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:05:23,437 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:05:23,437 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:05:23,437 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:05:23,438 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:05:23,438 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:05:23,438 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:05:23,438 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:05:23,440 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:05:23,442 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:05:23,443 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:05:23,444 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:05:23,444 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:05:23,444 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:05:23,445 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:05:23,445 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:05:23,445 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:05:23,446 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:05:23,446 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:05:23,446 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 17:05:23,448 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:05:23,448 - root - INFO - DEBUG: self.elapsed_time = 0.19451475143432617 seconds
2025-06-26 17:05:23,450 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:05:23,450 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:05:23,451 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:05:23,451 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 17:05:23,451 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:05:23,451 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:05:23,451 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:05:23,451 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:05:23,452 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:05:23,452 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:05:23,452 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:05:23,452 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:05:23,452 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:05:23,453 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:05:23,453 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:05:23,453 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:05:23,453 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:05:23,457 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,460 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,461 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,462 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,462 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,464 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,465 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,466 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,467 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,468 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,469 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,471 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,473 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,475 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:05:23,477 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:05:23,477 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:05:23,479 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 17:05:23,479 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 17:05:23,482 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:05:23,482 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:05:23,483 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:05:23,484 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:05:23,485 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:05:23,486 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:05:23,488 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:05:23,488 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:05:23,490 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:05:23,491 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:05:23,493 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:05:23,493 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:05:23,495 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:05:23,495 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:05:23,497 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:05:23,497 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:05:23,498 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:05:23,499 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:05:23,501 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:05:23,501 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:05:23,503 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:05:23,503 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:05:23,505 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:05:23,509 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:05:23,511 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:05:23,521 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:23,532 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:05:23,653 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:05:23,668 - root - INFO - Configuration loaded successfully.
2025-06-26 17:05:25,756 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:05:25,756 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 17:05:25,756 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,756 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,756 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,761 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:05:25,781 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:05:25,781 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:05:25,784 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:05:25,784 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:05:25,790 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:05:25,790 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:05:25,831 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:05:25,840 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_170457.csv
2025-06-26 17:05:25,840 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_170457.csv
2025-06-26 17:05:25,840 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:05:25,840 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:05:25,841 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:05:25,841 - root - INFO - Success flag set to: True
2025-06-26 17:05:25,841 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:05:25,841 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:05:25,841 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 17:05:25,841 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:05:25,843 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:05:25,843 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:05:25,843 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:05:25,843 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:05:25,843 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:05:25,843 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:05:25,843 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:05:25,843 - root - INFO -   - message: <class 'str'>
2025-06-26 17:05:25,843 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:05:25,918 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 17:05:25,919 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:05:25,919 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:05:25,934 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:05:25,935 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:05:25,935 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:05:25,935 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:05:25,935 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:05:25,936 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:05:26,451 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:05:26,452 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:05:26,454 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:05:26,455 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 17:05:26,455 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 17:05:26,455 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:05:26,455 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:05:26,456 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:05:26,457 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:05:26,457 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.*************, price: 0.23211
2025-06-26 17:05:26,457 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:05:26,457 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:05:26,457 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:05:26,472 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:05:26,475 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:05:26,475 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:05:26,485 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:05:26,485 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:05:26,486 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:05:26,486 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.86760587652406, 'price': 0.23211, 'order': {'id': 'paper-1750950326-TRX/EUR-buy-42.*************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.*************, 'price': 0.23211, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.*************, 'remaining': 0, 'timestamp': 1750950326456, 'datetime': '2025-06-26T17:05:26.456428', 'trades': [], 'average': 0.23211, 'average_price': 0.23211}, 'filled_amount': 42.*************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:05:26.457498'}
2025-06-26 17:05:26,649 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:05:26,670 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:05:26,671 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:05:26,671 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,671 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:05:26,671 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,671 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,671 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,671 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,671 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,672 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,672 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,672 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,672 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,673 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,673 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,673 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:05:26,673 - root - INFO - Rejected assets:
2025-06-26 17:05:26,673 - root - INFO -   BTC/EUR: reason=Failed to trade, score=12.0, rank=1
2025-06-26 17:05:26,673 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 12.0) - Failed to trade
2025-06-26 17:05:26,674 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 17:05:26,674 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:05:26,674 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:05:26,733 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:05:26,734 - root - INFO - Strategy execution completed successfully in 29.57 seconds
2025-06-26 17:05:26,738 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:05:26,788 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:05:36,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:05:46,815 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:05:56,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:06:06,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:06:16,851 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:06:26,865 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:06:36,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:06:46,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:06:56,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:07:07,101 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:07:17,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:07:27,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:07:37,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:07:47,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:07:57,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:08:07,308 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:08:17,323 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:08:27,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:08:37,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:08:47,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:08:57,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:09:07,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:09:17,415 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:09:27,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:09:37,441 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:09:47,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:09:57,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:10:07,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:10:17,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:10:27,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:10:37,532 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:10:47,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:10:57,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:11:07,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:11:16,877 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:11:17,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:11:21,883 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:11:21,883 - root - WARNING - Service is not running
