2025-06-26 17:26:18,478 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:26:19,345 - root - INFO - Loaded 41 trade records from logs/trades\trade_log_********.json
2025-06-26 17:26:19,346 - root - INFO - Loaded 25 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:26:19,347 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:26:20,433 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:20,441 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:20,442 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:20,448 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:20,448 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:20,455 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:20,456 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:26:20,456 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:26:21,429 - root - INFO - Telegram command handlers registered
2025-06-26 17:26:21,429 - root - INFO - Telegram bot polling started
2025-06-26 17:26:21,431 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:26:21,431 - root - INFO - Telegram notification channel initialized
2025-06-26 17:26:21,432 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:26:21,432 - root - INFO - Loaded 24 templates from file
2025-06-26 17:26:21,433 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:26:21,433 - root - INFO - Notification manager initialized
2025-06-26 17:26:21,433 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:26:21,433 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:26:21,434 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:26:21,434 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:26:21,441 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:26:21,444 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:26:21,444 - root - INFO - Recovery manager initialized
2025-06-26 17:26:21,445 - root - ERROR - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:26:21,445 - root - ERROR - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:26:21,445 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:21,454 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:21,455 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:26:21,455 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:26:21,455 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:26:21,456 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:26:21,456 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:26:21,456 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:26:21,456 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:26:21,456 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:26:21,456 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:21,467 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:21,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:26:21,523 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:26:21,528 - telegram.ext.Application - INFO - Application started
2025-06-26 17:26:21,857 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:26:21,858 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:26:21,858 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:26:21,859 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:26:21,859 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:26:21,859 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:21,866 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:21,870 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:21,881 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:21,881 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:26:21,888 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:21,890 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:26:21,891 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:26:21,891 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:26:21,891 - root - INFO - Trading mode: paper
2025-06-26 17:26:21,891 - root - INFO - Trading enabled: True
2025-06-26 17:26:21,892 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:26:21,892 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:26:21,893 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:26:21,893 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:26:21,893 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:21,944 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:22,255 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:26:22,255 - root - INFO - Trading enabled in paper mode
2025-06-26 17:26:22,258 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:26:22,259 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:26:22,259 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:26:22,260 - root - INFO - Generated run ID: ********_172622
2025-06-26 17:26:22,260 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:26:22,261 - root - INFO - Background service initialized
2025-06-26 17:26:22,261 - root - INFO - Network watchdog started
2025-06-26 17:26:22,263 - root - INFO - Network watchdog started
2025-06-26 17:26:22,264 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:26:22,264 - root - INFO - Background service started
2025-06-26 17:26:22,264 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:26:22,265 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:26:22,265 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:26:22,266 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:26:22,267 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:26:22,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:26:28,283 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:26:28,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:26:28,378 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:26:28,378 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:26:28,379 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:26:28,380 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:26:28,409 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,410 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,410 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,410 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:26:28,411 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,427 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,427 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,428 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,428 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 17:26:28,429 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,442 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,442 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,443 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,443 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:26:28,444 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,453 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,453 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,453 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,454 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:26:28,454 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,471 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,471 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,471 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,472 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:26:28,473 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,487 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,487 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,488 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,488 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:26:28,489 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,503 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,503 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,504 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,504 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:26:28,504 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,519 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,521 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,521 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,521 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:26:28,524 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,542 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,542 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,544 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,544 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:26:28,545 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,559 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,559 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,560 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,560 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:26:28,562 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,569 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,570 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,570 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,570 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:26:28,571 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,586 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,587 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,587 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,587 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:26:28,588 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,601 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,603 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,603 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,603 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:26:28,603 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,617 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,618 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,618 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:26:28,618 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:26:28,619 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,621 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 17:26:28,621 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:26:28,621 - root - INFO -   - Number of indicators: 8
2025-06-26 17:26:28,622 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:26:28,622 - root - INFO -   - Combination method: consensus
2025-06-26 17:26:28,622 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:26:28,622 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:26:28,623 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:26:28,623 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/USDT', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:26:28,623 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 17:26:28,623 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-26 17:26:28,623 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:26:28,623 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:26:28,623 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:26:28,623 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:26:28,624 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:26:28,624 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:26:28,624 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:26:28,624 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:28,632 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:28,632 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:28,637 - root - INFO - Configuration saved successfully.
2025-06-26 17:26:28,637 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:26:28,637 - root - INFO - Number of trend detection assets: 14
2025-06-26 17:26:28,637 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:26:28,637 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/USDT', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:26:28,637 - root - INFO - Number of trading assets: 14
2025-06-26 17:26:28,637 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:26:28,861 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:26:28,867 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:28,876 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:26:28,882 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:28,883 - root - INFO - Execution context: backtesting
2025-06-26 17:26:28,883 - root - INFO - Execution timing: candle_close
2025-06-26 17:26:28,883 - root - INFO - Ratio calculation method: independent
2025-06-26 17:26:28,883 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:26:28,883 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:26:28,884 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:26:28,884 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:26:28,884 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:26:28,884 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:26:28,884 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:26:28,885 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,887 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,887 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,887 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,888 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,888 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,888 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,888 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,889 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,889 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,890 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,890 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,891 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,891 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:26:28,891 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 17:26:28,910 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,911 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,912 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,912 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:26:28,926 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,927 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,928 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,928 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:26:28,938 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,941 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,942 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,943 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:26:28,949 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,949 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,950 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,950 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:26:28,963 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,964 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,965 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,965 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:26:28,977 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,979 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,979 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,979 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:26:28,991 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:28,992 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:28,992 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:28,992 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:26:29,004 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,006 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,007 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,007 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:26:29,020 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,021 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,021 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,022 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:26:29,034 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,035 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,036 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,036 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:26:29,044 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,045 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,046 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,046 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:26:29,059 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,061 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,061 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,061 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:26:29,074 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,075 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,076 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,077 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:26:29,086 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,088 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:26:29,088 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,088 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:26:29,089 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:26:29,089 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,089 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,089 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,091 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,091 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,091 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,092 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,092 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,092 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,092 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,093 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,093 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,093 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,094 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:26:29,105 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:26:29,106 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:26:29,106 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:26:29,106 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:26:29,107 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:26:29,114 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:29,114 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:26:29,114 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:26:29,115 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:26:29,115 - root - INFO - Override: combination_method = consensus
2025-06-26 17:26:29,115 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:26:29,115 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:26:29,116 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:26:29,116 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:26:29,116 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:26:29,116 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:26:29,131 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:26:29,131 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:26:29,131 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:26:29,131 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:26:29,131 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:29,132 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:26:29,159 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:26:29,159 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:26:29,159 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:26:29,159 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:26:29,169 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:26:29,169 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:26:29,170 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:26:29,562 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:26:29,563 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:26:29,609 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:26:29,609 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:26:29,611 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:26:29,611 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:26:29,679 - root - INFO - Generated DPSD signals
2025-06-26 17:26:29,681 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:26:29,681 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:26:29,681 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:26:29,687 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:26:29,687 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:26:29,688 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:26:29,733 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:26:29,733 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:26:29,733 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:26:29,811 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:26:29,817 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:26:29,818 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:26:29,818 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:26:29,818 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:26:29,818 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:26:29,820 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:26:29,826 - root - INFO - Configuration saved successfully.
2025-06-26 17:26:29,827 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:26:29,827 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:26:29,833 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:29,833 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:26:29,834 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 17:26:29,834 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:26:29,851 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:29,871 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:29,892 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:29,892 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:29,911 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:29,919 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:29,939 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:26:29,939 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:29,960 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:26:29,965 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:29,979 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:29,981 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:29,994 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:29,999 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:30,015 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:30,015 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,029 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:30,033 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:30,050 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:26:30,051 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,065 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:26:30,069 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:30,087 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:30,088 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,103 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:30,108 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:30,125 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:26:30,126 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,145 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:26:30,151 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:30,172 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:26:30,173 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,198 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:26:30,205 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:30,231 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:30,232 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,255 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:30,262 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:30,283 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:30,284 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,297 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:30,303 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:30,318 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,338 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:30,355 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:26:30,355 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,369 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:26:30,375 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:30,393 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:30,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,412 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:30,418 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:30,437 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,464 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:30,485 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:30,486 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,502 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:30,509 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:30,531 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:30,532 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,549 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:30,557 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:30,576 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:30,576 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,592 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:30,598 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:30,622 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:26:30,623 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,642 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:26:30,647 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:30,664 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:30,665 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,679 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:30,684 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:30,701 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:30,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,716 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:30,721 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:30,738 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:30,739 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,758 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:30,763 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:30,781 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:30,781 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,798 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:30,802 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:30,819 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,839 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:30,859 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,880 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:30,896 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:26:30,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,914 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:26:30,919 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:30,943 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:26:30,943 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:30,961 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:26:30,967 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:30,988 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,015 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:31,033 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:31,034 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,048 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:31,053 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:31,069 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:31,070 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,084 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:31,089 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:31,106 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:26:31,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,121 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:26:31,126 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:31,141 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:26:31,142 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,155 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:26:31,160 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:31,175 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:31,176 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,188 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:31,193 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:31,208 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:31,209 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,222 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:31,227 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:31,242 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:26:31,242 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,255 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:26:31,259 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:31,276 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:26:31,276 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,294 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:26:31,299 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:31,316 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:31,317 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,332 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:31,337 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:31,357 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:31,359 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,372 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:31,377 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:31,394 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:31,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,408 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:31,414 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:31,428 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:31,428 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,441 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:31,446 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:31,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,479 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:31,494 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,512 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:31,527 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,545 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:31,561 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:26:31,561 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,576 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:26:31,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:26:31,581 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:31,600 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,620 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:31,637 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,660 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:31,679 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:31,679 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,695 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:31,701 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:31,720 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,742 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:31,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,776 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:31,791 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:31,792 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,804 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:31,809 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:31,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,847 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:31,863 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,880 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:31,896 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:26:31,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,910 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:26:31,915 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:31,929 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,946 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:31,962 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:31,978 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:31,993 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:31,993 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,008 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:32,013 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:32,026 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:32,027 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,042 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:32,046 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:32,059 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:26:32,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,074 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:26:32,079 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:32,093 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:26:32,093 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,107 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:26:32,112 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:32,126 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:32,127 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,139 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:32,146 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:32,163 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:32,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,179 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:32,183 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:32,201 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:32,201 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,216 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:26:32,221 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:32,244 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,265 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:32,281 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,299 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:32,313 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:26:32,314 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,328 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:26:32,332 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:32,347 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,365 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:32,379 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,397 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:32,414 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,432 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:32,447 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,466 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:32,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,498 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:32,513 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:32,514 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,529 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:32,535 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:32,552 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:32,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,567 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:32,571 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:32,592 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:32,593 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,609 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:32,614 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:32,632 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,649 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:32,666 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,683 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:32,700 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:26:32,700 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,714 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:26:32,719 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:32,735 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,755 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:32,774 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:32,775 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,793 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:32,798 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:32,815 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,835 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:32,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,878 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:32,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,917 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:32,934 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,951 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:32,965 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:32,984 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:32,998 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:26:32,998 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,013 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:26:33,017 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:33,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,050 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:33,064 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:26:33,064 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,077 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:26:33,082 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:33,097 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,114 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:33,132 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,151 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:33,166 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,183 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:33,199 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,217 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:33,232 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,249 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:33,265 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:33,265 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,277 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:33,281 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:33,297 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,313 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:33,329 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,345 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:33,359 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:26:33,361 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,374 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:26:33,378 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:33,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,413 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:33,427 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:26:33,428 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,441 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:26:33,445 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:33,459 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:33,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,474 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:33,479 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:33,493 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:26:33,493 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,505 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:26:33,511 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:33,526 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,545 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:33,559 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:33,559 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,573 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:33,578 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:33,593 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:33,594 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,607 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:33,612 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:33,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,645 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:33,659 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:33,661 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,675 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:33,679 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:33,693 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,710 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:33,724 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,742 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:33,758 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,773 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:33,788 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,807 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:33,821 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,839 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:33,854 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:26:33,855 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,867 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:26:33,872 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:33,887 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,905 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:33,921 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,942 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:33,958 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:33,979 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:33,996 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:33,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,011 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:34,016 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:34,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,054 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:34,071 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,092 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:34,106 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,124 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:34,138 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,158 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:34,171 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,190 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:34,206 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:34,207 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,220 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:34,225 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:34,241 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,259 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:34,275 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:34,275 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,286 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:34,293 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:34,308 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:34,308 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,321 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:34,326 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:34,341 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:34,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,354 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:34,359 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:34,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,393 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:34,409 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:34,409 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,422 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:34,426 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:34,441 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:34,442 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,454 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:34,460 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:34,475 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,493 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:34,507 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,524 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:34,538 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,558 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:34,571 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,589 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:34,604 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,621 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:34,635 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,653 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:34,667 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,687 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:34,703 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,720 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:34,735 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:26:34,735 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,749 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:26:34,754 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:34,768 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:34,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,782 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:26:34,787 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:34,801 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:34,801 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,815 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:26:34,819 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:34,831 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:26:34,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,846 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:26:34,851 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:34,868 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,887 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:34,903 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,920 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:34,935 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,952 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:34,966 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:34,983 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:34,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,016 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:35,030 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,047 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:35,062 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,079 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:35,095 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:35,095 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,108 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:35,113 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:35,129 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:35,130 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,144 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:26:35,151 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:35,166 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:35,167 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,182 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:35,187 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:35,210 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:26:35,210 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,231 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:26:35,236 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:35,253 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:26:35,253 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,269 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:26:35,274 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:35,289 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:35,291 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,310 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:35,318 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:35,342 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:26:35,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,361 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:26:35,369 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:35,393 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:26:35,393 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,413 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:26:35,420 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:35,437 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,457 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:35,473 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:35,474 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,487 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:35,492 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:35,507 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,525 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:35,539 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,558 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:35,573 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:35,574 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,587 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:35,591 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:35,608 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:35,608 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,619 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:35,626 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:35,641 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:35,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,654 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:26:35,659 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:35,675 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:26:35,675 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,687 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:26:35,692 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:35,706 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:26:35,707 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,720 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:26:35,725 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:35,741 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:35,741 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,753 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:35,759 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:35,777 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:26:35,778 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,791 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:26:35,795 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:35,809 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:35,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,822 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:26:35,826 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:35,843 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:35,844 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,856 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:35,861 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:35,876 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,894 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:35,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,930 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:26:35,946 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,963 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:26:35,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:35,994 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 17:26:36,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,029 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:26:36,045 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:36,046 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,059 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:26:36,065 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:26:36,082 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:36,082 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,095 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:26:36,101 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:26:36,116 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:36,117 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,131 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:26:36,136 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:26:36,155 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:26:36,155 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,176 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:26:36,181 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:26:36,199 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:26:36,200 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,214 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:26:36,219 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:26:36,236 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:26:36,236 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,251 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:26:36,257 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:26:36,275 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,299 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:26:36,321 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:36,321 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,343 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:26:36,348 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:26:36,366 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,394 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:26:36,420 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:26:36,445 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:26:38,301 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:26:38,301 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 17:26:38,301 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:26:38,305 - root - INFO - Date ranges for each asset:
2025-06-26 17:26:38,305 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,307 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,309 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:26:38,311 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:26:38,313 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:26:38,314 - root - INFO -    Execution Method: candle_close
2025-06-26 17:26:38,314 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:26:38,314 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:26:38,317 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,317 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,317 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,318 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,318 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,318 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,318 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,319 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:26:38,321 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:26:38,321 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:26:38,321 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,321 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:26:38,321 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:26:38,322 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,322 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,323 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,324 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,324 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,324 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,324 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:26:38,325 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:26:38,325 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:26:38,325 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,325 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:26:38,325 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:26:38,325 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:26:38,325 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,325 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,331 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,331 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,331 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,331 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,331 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:26:38,331 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 17:26:38,331 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 17:26:38,331 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 17:26:38,331 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,331 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:26:38,331 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:26:38,331 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 17:26:38,334 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 17:26:38,334 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 17:26:38,334 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 17:26:38,334 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,334 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:26:38,334 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:26:38,334 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 17:26:38,334 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 17:26:38,334 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 17:26:38,334 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 17:26:38,334 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,334 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:26:38,334 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:26:38,334 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 17:26:38,338 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:26:38,338 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:26:38,338 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:26:38,338 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,339 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:26:38,339 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:26:38,339 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:26:38,341 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 17:26:38,342 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 17:26:38,342 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 17:26:38,342 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,342 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:26:38,342 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:26:38,342 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 17:26:38,343 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 17:26:38,343 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 17:26:38,343 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 17:26:38,344 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,344 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:26:38,344 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:26:38,344 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 17:26:38,345 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:26:38,345 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:26:38,345 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:26:38,345 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,345 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:26:38,345 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:26:38,345 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:26:38,346 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:26:38,347 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:26:38,347 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:26:38,347 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,347 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:26:38,347 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:26:38,347 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:26:38,347 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:26:38,347 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:26:38,347 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:26:38,347 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,347 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:26:38,349 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:26:38,349 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:26:38,350 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:26:38,350 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:26:38,350 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:26:38,350 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,350 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:26:38,351 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:26:38,351 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:26:38,351 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:26:38,351 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:26:38,351 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:26:38,351 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,352 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:26:38,352 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:26:38,352 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:26:38,353 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:26:38,353 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:26:38,353 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:26:38,353 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,354 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:26:38,354 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:26:38,354 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:26:38,354 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 17:26:38,354 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 17:26:38,354 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 17:26:38,354 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,355 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:26:38,355 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:26:38,355 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 17:26:38,361 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 17:26:38,361 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 17:26:38,361 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 17:26:38,361 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,361 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:26:38,362 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:26:38,362 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 17:26:38,363 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:26:38,363 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:26:38,364 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:26:38,364 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,364 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:26:38,364 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:26:38,364 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:26:38,369 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:26:38,369 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:26:38,369 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:26:38,369 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,369 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:26:38,369 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:26:38,369 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:26:38,373 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:26:38,374 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:26:38,374 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:26:38,374 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,374 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:26:38,374 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:26:38,375 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:26:38,375 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:26:38,375 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:26:38,376 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:26:38,376 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,376 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:26:38,376 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:26:38,376 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:26:38,377 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:26:38,377 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:26:38,377 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:26:38,377 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,377 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:26:38,377 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:26:38,377 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:26:38,384 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:26:38,384 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:26:38,384 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:26:38,384 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:26:38,384 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:26:38,384 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:26:38,384 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:26:38,414 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:26:38,414 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:26:38,414 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:26:38,415 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:26:38,415 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:26:38,415 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:26:38,416 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:26:38,416 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:26:38,416 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:26:38,417 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:26:38,417 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:26:38,417 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:26:38,417 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:26:38,417 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:26:38,418 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:26:38,418 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:26:38,418 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:26:38,418 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:26:38,418 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:26:38,419 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:26:38,419 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:26:38,419 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:26:38,419 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 17:26:38,419 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:26:38,419 - root - INFO - DEBUG: self.elapsed_time = 0.11822175979614258 seconds
2025-06-26 17:26:38,422 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:26:38,422 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:26:38,422 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:26:38,422 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 17:26:38,422 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:26:38,423 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:26:38,424 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:26:38,424 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:26:38,424 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:26:38,424 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:26:38,425 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,426 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,427 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,428 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,429 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,430 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,431 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,431 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,432 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,433 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,434 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,435 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,436 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,437 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:26:38,438 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:26:38,438 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:26:38,439 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 17:26:38,441 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 17:26:38,442 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:26:38,443 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:26:38,444 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:26:38,444 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:26:38,445 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:26:38,446 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:26:38,447 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:26:38,447 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:26:38,449 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:26:38,449 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:26:38,450 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:26:38,451 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:26:38,452 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:26:38,452 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:26:38,453 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:26:38,454 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:26:38,455 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:26:38,455 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:26:38,457 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:26:38,457 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:26:38,458 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:26:38,459 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:26:38,459 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:26:38,461 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:26:38,461 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:26:38,468 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:38,477 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:26:38,556 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:26:38,561 - root - INFO - Configuration loaded successfully.
2025-06-26 17:26:39,876 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,876 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,876 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,876 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,876 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,876 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:26:39,881 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 17:26:39,881 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,881 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:26:39,897 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:26:39,898 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:26:39,899 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:26:39,900 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:26:39,901 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:26:39,902 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:26:39,936 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/USDT': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:26:39,943 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_172622.csv
2025-06-26 17:26:39,944 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_172622.csv
2025-06-26 17:26:39,944 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:26:39,944 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:26:39,944 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:26:39,944 - root - INFO - Success flag set to: True
2025-06-26 17:26:39,945 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:26:39,945 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:26:39,945 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 17:26:39,945 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:26:39,945 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:26:39,945 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:26:39,945 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:26:39,946 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:26:39,946 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:26:39,946 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:26:39,946 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:26:39,946 - root - INFO -   - message: <class 'str'>
2025-06-26 17:26:39,946 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:26:39,946 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 17:26:39,946 - root - ERROR - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 17:26:39,947 - root - ERROR - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 17:26:39,947 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 17:26:39,963 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/USDT', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 17:26:39,964 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/USDT', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:26:39,964 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:26:39,964 - root - ERROR - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 17:26:39,964 - root - ERROR - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 17:26:39,964 - root - ERROR - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 17:26:39,964 - root - ERROR - [DEBUG]   - MTPI signal: 1
2025-06-26 17:26:39,964 - root - ERROR - [DEBUG]   - Use MTPI signal: False
2025-06-26 17:26:39,964 - root - ERROR - [DEBUG] ERROR - TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 17:26:39,964 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:26:39,964 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:26:39,965 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:26:39,965 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:26:39,965 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:26:39,965 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 17:26:39,965 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 17:26:39,965 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:26:39,965 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 17:26:39,965 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-26 17:26:39,965 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-26 17:26:39,968 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 17:26:39,968 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 17:26:40,226 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 17:26:40,300 - root - ERROR - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23231
2025-06-26 17:26:40,300 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 17:26:40,300 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 17:26:40,300 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 17:26:40,300 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:26:40,300 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:26:40,302 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:26:40,303 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 17:26:40,303 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 17:26:40,303 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:26:40,303 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:26:40,303 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:26:40,304 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:26:40,304 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.**************, price: 0.23231
2025-06-26 17:26:40,304 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:26:40,304 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:26:40,304 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:26:40,311 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:26:40,311 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:26:40,311 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:26:40,317 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:26:40,317 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:26:40,317 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:26:40,318 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.830700357281216, 'price': 0.23231, 'order': {'id': 'paper-1750951600-TRX/EUR-buy-42.**************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.**************, 'price': 0.23231, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.**************, 'remaining': 0, 'timestamp': 1750951600303, 'datetime': '2025-06-26T17:26:40.303093', 'trades': [], 'average': 0.23231, 'average_price': 0.23231}, 'filled_amount': 42.**************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:26:40.304102'}
2025-06-26 17:26:40,401 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:26:40,417 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:26:40,417 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:26:40,417 - root - INFO -   BTC/USDT: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,418 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:26:40,418 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,418 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,418 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,418 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,418 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,418 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:26:40,419 - root - INFO - Rejected assets:
2025-06-26 17:26:40,419 - root - INFO -   BTC/USDT: reason=Failed to trade, score=12.0, rank=1
2025-06-26 17:26:40,419 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/USDT (rank 1, score 12.0) - Failed to trade
2025-06-26 17:26:40,421 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 17:26:40,421 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:26:40,421 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/USDT': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:26:40,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:26:40,545 - root - INFO - Strategy execution completed successfully in 18.28 seconds
2025-06-26 17:26:40,548 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:26:41,610 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:26:51,625 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:27:01,643 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:27:11,661 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:27:21,677 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:27:26,311 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:27:31,693 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:27:34,475 - root - INFO - Network watchdog stopped
2025-06-26 17:27:34,475 - root - INFO - Network watchdog stopped
2025-06-26 17:27:34,475 - root - INFO - Background service stopped
2025-06-26 17:27:34,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
