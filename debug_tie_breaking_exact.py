#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.scoring import find_best_asset_for_day

def debug_tie_breaking_exact():
    """Debug tie-breaking with the exact order we see in the logs."""
    
    print("=" * 80)
    print("DEBUGGING TIE-BREAKING WITH EXACT LOG ORDER")
    print("=" * 80)
    
    # Use the exact order from the conversion debug logs
    print("Using the exact EUR scores order from the conversion logs:")
    
    # This is the exact order from the logs
    eur_scores_order = [
        'ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 
        'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 
        'BNB/EUR', 'DOT/EUR'
    ]
    
    print("EUR scores dictionary order:")
    for i, asset in enumerate(eur_scores_order, 1):
        marker = ""
        if asset == 'BTC/EUR':
            marker = " ← BTC"
        elif asset == 'TRX/EUR':
            marker = " ← TRX"
        print(f"  {i:2d}. {asset}{marker}")
    
    # Find positions
    btc_pos = eur_scores_order.index('BTC/EUR') + 1
    trx_pos = eur_scores_order.index('TRX/EUR') + 1
    
    print(f"\nBTC/EUR position: {btc_pos}")
    print(f"TRX/EUR position: {trx_pos}")
    print(f"BTC comes before TRX: {btc_pos < trx_pos}")
    
    # Test 1: Create scores dictionary with BTC and TRX tied at max score
    print(f"\n" + "=" * 50)
    print("TEST 1: BTC and TRX tied at score 12")
    print("=" * 50)
    
    test_scores_1 = {}
    for asset in eur_scores_order:
        if asset == 'BTC/EUR':
            test_scores_1[asset] = 12.0
        elif asset == 'TRX/EUR':
            test_scores_1[asset] = 12.0
        else:
            test_scores_1[asset] = 8.0  # Lower score
    
    print("Test scores:")
    for asset, score in test_scores_1.items():
        if asset in ['BTC/EUR', 'TRX/EUR']:
            print(f"  {asset}: {score} ← TIED")
        else:
            print(f"  {asset}: {score}")
    
    print(f"\nDictionary keys order: {list(test_scores_1.keys())}")
    
    selected_1 = find_best_asset_for_day(test_scores_1)
    print(f"\nSelected asset: {selected_1}")
    
    if selected_1 == 'BTC/EUR':
        print("✅ BTC was selected (correct)")
    elif selected_1 == 'TRX/EUR':
        print("❌ TRX was selected (BUG!)")
    else:
        print(f"? {selected_1} was selected")
    
    # Test 2: Create scores dictionary with the exact scores from the logs
    print(f"\n" + "=" * 50)
    print("TEST 2: Exact scores from logs (BTC=13, TRX=12)")
    print("=" * 50)
    
    # From the logs: BTC/USDT: 13.0, TRX/USDT: 12.0
    test_scores_2 = {
        'ETH/EUR': 8.0,
        'BTC/EUR': 13.0,
        'SOL/EUR': 6.0,
        'SUI/EUR': 2.0,
        'XRP/EUR': 9.0,
        'AAVE/EUR': 10.0,
        'AVAX/EUR': 4.0,
        'ADA/EUR': 3.0,
        'LINK/EUR': 7.0,
        'TRX/EUR': 12.0,
        'PEPE/EUR': 0.0,
        'DOGE/EUR': 5.0,
        'BNB/EUR': 11.0,
        'DOT/EUR': 1.0
    }
    
    print("Test scores (from logs):")
    max_score = max(test_scores_2.values())
    for asset, score in test_scores_2.items():
        marker = ""
        if score == max_score:
            marker = " ← MAX"
        elif asset in ['BTC/EUR', 'TRX/EUR']:
            marker = " ← FOCUS"
        print(f"  {asset}: {score}{marker}")
    
    selected_2 = find_best_asset_for_day(test_scores_2)
    print(f"\nSelected asset: {selected_2}")
    print(f"Expected: BTC/EUR (highest score)")
    
    if selected_2 == 'BTC/EUR':
        print("✅ BTC was selected (correct - highest score)")
    else:
        print(f"❌ {selected_2} was selected instead of BTC")
    
    # Test 3: Force a tie between BTC and TRX at the maximum score
    print(f"\n" + "=" * 50)
    print("TEST 3: Force BTC and TRX tie at max score")
    print("=" * 50)
    
    test_scores_3 = test_scores_2.copy()
    test_scores_3['BTC/EUR'] = 13.0
    test_scores_3['TRX/EUR'] = 13.0  # Force tie
    
    print("Test scores (forced tie):")
    max_score = max(test_scores_3.values())
    for asset, score in test_scores_3.items():
        marker = ""
        if score == max_score:
            marker = " ← TIED MAX"
        print(f"  {asset}: {score}{marker}")
    
    selected_3 = find_best_asset_for_day(test_scores_3)
    print(f"\nSelected asset: {selected_3}")
    
    if selected_3 == 'BTC/EUR':
        print("✅ BTC was selected (correct - comes first in dictionary)")
    elif selected_3 == 'TRX/EUR':
        print("❌ TRX was selected (BUG! - should be BTC)")
    else:
        print(f"? {selected_3} was selected")
    
    print(f"\n" + "=" * 80)

if __name__ == "__main__":
    debug_tie_breaking_exact()
