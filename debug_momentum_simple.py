#!/usr/bin/env python3
"""
Simple debugging script to analyze the momentum issue using the actual CSV data.
"""

import pandas as pd
import ast
from datetime import datetime, <PERSON><PERSON><PERSON>

def debug_momentum_issue():
    """Debug the momentum issue using actual allocation history data."""
    
    # Load the CSV file
    csv_file = 'allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv'
    try:
        df = pd.read_csv(csv_file)
        print(f"Loaded {len(df)} rows from {csv_file}")
    except FileNotFoundError:
        print(f"ERROR: {csv_file} not found.")
        return
    
    # Focus on the critical dates around the violation
    print("\n=== ANALYZING MOMENTUM VIOLATION ===")
    print("Date: 2024-01-06 (violation date)")
    print("Expected: SUI/USDT should be selected (better momentum)")
    print("Actual: SOL/USDT was selected")
    
    # Extract the relevant rows
    target_dates = ['2024-01-05', '2024-01-06', '2024-01-07']
    
    for date_str in target_dates:
        # Find the row for this date
        date_rows = df[df['date'].str.contains(date_str)]
        if date_rows.empty:
            print(f"No data found for {date_str}")
            continue
            
        row = date_rows.iloc[0]
        
        # Parse the scores dictionary
        scores_str = row['scores']
        try:
            scores_dict = ast.literal_eval(scores_str)
        except:
            print(f"Error parsing scores for {date_str}: {scores_str}")
            continue
        
        print(f"\n{date_str}:")
        print(f"  Selected: {row['current_holdings']}")
        print(f"  Scores: SOL/USDT={scores_dict.get('SOL/USDT', 'N/A')}, SUI/USDT={scores_dict.get('SUI/USDT', 'N/A')}")
    
    # Now let's simulate what the momentum calculation should be
    print(f"\n=== MOMENTUM CALCULATION ANALYSIS ===")
    
    # Get scores for 2024-01-05 (previous day)
    prev_date_rows = df[df['date'].str.contains('2024-01-05')]
    if prev_date_rows.empty:
        print("ERROR: No data for 2024-01-05")
        return
    
    prev_scores_str = prev_date_rows.iloc[0]['scores']
    prev_scores = ast.literal_eval(prev_scores_str)

    # Get scores for 2024-01-06 (violation day)
    curr_date_rows = df[df['date'].str.contains('2024-01-06')]
    if curr_date_rows.empty:
        print("ERROR: No data for 2024-01-06")
        return

    curr_scores_str = curr_date_rows.iloc[0]['scores']
    curr_scores = ast.literal_eval(curr_scores_str)
    
    print(f"Previous scores (2024-01-05): {prev_scores}")
    print(f"Current scores (2024-01-06): {curr_scores}")
    
    # Calculate momentum for tied assets
    sol_prev = prev_scores.get('SOL/USDT', 0)
    sol_curr = curr_scores.get('SOL/USDT', 0)
    sui_prev = prev_scores.get('SUI/USDT', 0)
    sui_curr = curr_scores.get('SUI/USDT', 0)
    
    sol_momentum = sol_curr - sol_prev
    sui_momentum = sui_curr - sui_prev
    
    print(f"\nMomentum Analysis:")
    print(f"SOL/USDT: {sol_prev} → {sol_curr} (Δ={sol_momentum:+.1f})")
    print(f"SUI/USDT: {sui_prev} → {sui_curr} (Δ={sui_momentum:+.1f})")
    
    # Determine what should happen with momentum approach
    if sol_curr == sui_curr:  # Tied scores
        print(f"\n✅ TIED SCORES DETECTED: Both have score {sol_curr}")
        if sui_momentum > sol_momentum:
            print(f"✅ SUI/USDT has better momentum ({sui_momentum:+.1f} > {sol_momentum:+.1f})")
            print(f"✅ MOMENTUM APPROACH: SUI/USDT should be selected")
        elif sol_momentum > sui_momentum:
            print(f"❌ SOL/USDT has better momentum ({sol_momentum:+.1f} > {sui_momentum:+.1f})")
            print(f"❌ MOMENTUM APPROACH: SOL/USDT should be selected")
        else:
            print(f"⚖️ Equal momentum ({sol_momentum:+.1f}), fallback to asset order")
    else:
        print(f"No tie - highest score wins")
    
    # Check what was actually selected
    selected_asset = curr_date_rows.iloc[0]['current_holdings']
    print(f"\nActual selection: {selected_asset}")
    
    # Create a simple DataFrame to simulate daily_scores_df
    print(f"\n=== SIMULATING DAILY_SCORES_DF INDEXING ===")
    
    # Extract all dates and scores
    dates = []
    all_scores = []
    
    for _, row in df.iterrows():
        date_str = row['date']
        # Convert to datetime
        date_obj = pd.to_datetime(date_str)
        dates.append(date_obj)
        
        scores_str = row['scores']
        scores_dict = ast.literal_eval(scores_str)
        all_scores.append(scores_dict)
    
    # Create DataFrame similar to daily_scores_df
    assets = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']
    scores_data = {asset: [] for asset in assets}
    
    for scores_dict in all_scores:
        for asset in assets:
            scores_data[asset].append(scores_dict.get(asset, 0))
    
    daily_scores_df = pd.DataFrame(scores_data, index=dates)
    
    # Find the violation date
    violation_date = pd.to_datetime('2024-01-06')
    try:
        violation_idx = daily_scores_df.index.get_loc(violation_date)
        print(f"Violation date found at index: {violation_idx}")
    except KeyError:
        print("ERROR: Violation date not found in DataFrame")
        return
    
    # Simulate the problematic logic from main_program.py
    i = violation_idx
    start_idx = 0
    
    if i > start_idx:
        # This is the current (potentially wrong) logic
        prev_score_date = daily_scores_df.index[i-1]
        print(f"Index-based previous date: {prev_score_date}")
        
        # What should be the previous date
        expected_prev_date = violation_date - timedelta(days=1)
        print(f"Expected previous date: {expected_prev_date}")
        
        # Check if they match
        if prev_score_date.date() != expected_prev_date.date():
            print(f"🚨 DATE MISMATCH!")
            print(f"   Index gives: {prev_score_date.date()}")
            print(f"   Expected: {expected_prev_date.date()}")
        else:
            print(f"✅ Dates match correctly")
        
        # Get scores using current method
        prev_scores_current_method = daily_scores_df.loc[prev_score_date]
        print(f"\nScores from index-based method:")
        print(f"SOL/USDT: {prev_scores_current_method['SOL/USDT']}")
        print(f"SUI/USDT: {prev_scores_current_method['SUI/USDT']}")
        
        # Compare with expected scores
        print(f"\nExpected scores (from CSV analysis):")
        print(f"SOL/USDT: {sol_prev}")
        print(f"SUI/USDT: {sui_prev}")
        
        if (prev_scores_current_method['SOL/USDT'] != sol_prev or 
            prev_scores_current_method['SUI/USDT'] != sui_prev):
            print(f"🚨 SCORE MISMATCH DETECTED!")
            print(f"This explains why momentum calculation is wrong!")
        else:
            print(f"✅ Scores match - issue must be elsewhere")

if __name__ == "__main__":
    debug_momentum_issue()
