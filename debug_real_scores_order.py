#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config_manager import load_config
from main_program import run_strategy_for_web
from datetime import datetime, timedelta

def debug_real_scores_order():
    """Debug the actual scores dictionary order in a real strategy execution."""
    
    print("=" * 80)
    print("DEBUGGING REAL SCORES ORDER IN STRATEGY EXECUTION")
    print("=" * 80)
    
    # Load the configuration
    config_path = "config/settings_bitvavo_eur.yaml"
    print(f"Loading config from: {config_path}")
    
    try:
        config = load_config(config_path)
        settings = config.get('settings', {})
        
        # Get the trend assets and trading assets
        trend_assets = settings.get('trend_assets', settings.get('assets', ['BTC/USDT', 'ETH/USDT', 'SOL/USDT']))
        trading_assets = settings.get('assets', ['BTC/EUR', 'ETH/EUR', 'SOL/EUR'])
        
        print(f"\nTrend assets (USDT): {len(trend_assets)} assets")
        print(f"Trading assets (EUR): {len(trading_assets)} assets")
        
        # Run a quick strategy to get the actual scores
        print("\nRunning strategy to get real scores...")
        
        # Use a recent date for analysis
        analysis_start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d')
        
        results = run_strategy_for_web(
            use_mtpi_signal=False,  # Disable MTPI to focus on scoring
            timeframe='1d',
            analysis_start_date=analysis_start_date,
            selected_assets=trend_assets,  # Use USDT pairs for trend detection
            trading_assets=trading_assets,  # Pass EUR pairs for trading execution
            n_assets=1,
            use_weighted_allocation=False,
            use_cache=True,
            force_refresh_cache=False
        )
        
        if results and 'latest_scores' in results:
            latest_scores = results['latest_scores']
            
            print(f"\n✅ Got real scores dictionary with {len(latest_scores)} assets")
            print("\nREAL SCORES DICTIONARY ORDER:")
            print("-" * 50)
            
            for i, (asset, score) in enumerate(latest_scores.items(), 1):
                marker = ""
                if asset == 'BTC/EUR':
                    marker = " ← BTC"
                elif asset == 'TRX/EUR':
                    marker = " ← TRX"
                print(f"  {i:2d}. {asset}: {score}{marker}")
            
            # Find BTC and TRX positions
            assets_list = list(latest_scores.keys())
            btc_pos = assets_list.index('BTC/EUR') + 1 if 'BTC/EUR' in assets_list else None
            trx_pos = assets_list.index('TRX/EUR') + 1 if 'TRX/EUR' in assets_list else None
            
            print(f"\nPOSITION ANALYSIS:")
            print("-" * 30)
            if btc_pos:
                print(f"BTC/EUR position: {btc_pos}")
            if trx_pos:
                print(f"TRX/EUR position: {trx_pos}")
            
            if btc_pos and trx_pos:
                print(f"BTC comes before TRX: {btc_pos < trx_pos}")
                
                # Check if they have the same score
                btc_score = latest_scores.get('BTC/EUR', 0)
                trx_score = latest_scores.get('TRX/EUR', 0)
                print(f"BTC score: {btc_score}")
                print(f"TRX score: {trx_score}")
                print(f"Scores are equal: {btc_score == trx_score}")
                
                if btc_score == trx_score:
                    print("\n🔍 TIE DETECTED!")
                    if btc_pos < trx_pos:
                        print("✅ BTC should be selected (comes first in dictionary)")
                    else:
                        print("❌ TRX would be selected (comes first in dictionary)")
                        print("🚨 THIS EXPLAINS THE ISSUE!")
            
            # Test the actual tie-breaking function
            print(f"\nTESTING ACTUAL TIE-BREAKING FUNCTION:")
            print("-" * 50)
            
            from src.scoring import find_best_asset_for_day
            
            # Create a test scenario with tied BTC and TRX scores
            test_scores = latest_scores.copy()
            max_score = max(test_scores.values())
            test_scores['BTC/EUR'] = max_score
            test_scores['TRX/EUR'] = max_score
            
            print("Test scores (BTC and TRX both set to max score):")
            for asset, score in test_scores.items():
                if asset in ['BTC/EUR', 'TRX/EUR']:
                    print(f"  {asset}: {score} ← TIED")
            
            selected_asset = find_best_asset_for_day(test_scores)
            print(f"\nSelected asset: {selected_asset}")
            
            if selected_asset == 'BTC/EUR':
                print("✅ BTC was selected")
            elif selected_asset == 'TRX/EUR':
                print("❌ TRX was selected - THIS IS THE BUG!")
            else:
                print(f"? {selected_asset} was selected")
        
        else:
            print("❌ Failed to get scores from strategy execution")
            if results:
                print(f"Results keys: {list(results.keys())}")
            else:
                print("No results returned")
        
        print("\n" + "=" * 80)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_real_scores_order()
