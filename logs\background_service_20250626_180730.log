2025-06-26 18:07:30,615 - root - INFO - Loaded environment variables from .env file
2025-06-26 18:07:31,291 - root - INFO - Loaded 53 trade records from logs/trades\trade_log_********.json
2025-06-26 18:07:31,292 - root - INFO - Loaded 31 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 18:07:31,292 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 18:07:32,161 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:32,169 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:32,175 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:32,181 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:32,181 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:32,189 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:32,190 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 18:07:32,191 - root - INFO - Notification configuration loaded successfully.
2025-06-26 18:07:33,023 - root - INFO - Telegram command handlers registered
2025-06-26 18:07:33,024 - root - INFO - Telegram bot polling started
2025-06-26 18:07:33,024 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 18:07:33,025 - root - INFO - Telegram notification channel initialized
2025-06-26 18:07:33,026 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 18:07:33,026 - root - INFO - Loaded 24 templates from file
2025-06-26 18:07:33,026 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 18:07:33,027 - root - INFO - Notification manager initialized
2025-06-26 18:07:33,027 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 18:07:33,027 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 18:07:33,028 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 18:07:33,028 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 18:07:33,032 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 18:07:33,033 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 18:07:33,034 - root - INFO - Recovery manager initialized
2025-06-26 18:07:33,034 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 18:07:33,034 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 18:07:33,034 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:33,041 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:33,042 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:07:33,042 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:07:33,042 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:07:33,042 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:07:33,042 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:07:33,042 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:07:33,042 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:07:33,042 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:07:33,043 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:33,052 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:33,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 18:07:33,094 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 18:07:33,096 - telegram.ext.Application - INFO - Application started
2025-06-26 18:07:33,297 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 18:07:33,297 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:07:33,297 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:07:33,298 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:07:33,298 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:07:33,298 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:33,304 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:33,310 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:33,317 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:33,318 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:07:33,325 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:33,326 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 18:07:33,326 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 18:07:33,327 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 18:07:33,327 - root - INFO - Trading mode: paper
2025-06-26 18:07:33,327 - root - INFO - Trading enabled: True
2025-06-26 18:07:33,327 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 18:07:33,327 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 18:07:33,327 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 18:07:33,327 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 18:07:33,328 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:33,335 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:33,526 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 18:07:33,526 - root - INFO - Trading enabled in paper mode
2025-06-26 18:07:33,528 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:07:33,528 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 18:07:33,528 - root - INFO - Reset paper trading account to initial balance
2025-06-26 18:07:33,528 - root - INFO - Generated run ID: ********_180733
2025-06-26 18:07:33,528 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 18:07:33,529 - root - INFO - Background service initialized
2025-06-26 18:07:33,530 - root - INFO - Network watchdog started
2025-06-26 18:07:33,530 - root - INFO - Network watchdog started
2025-06-26 18:07:33,532 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 18:07:33,533 - root - INFO - Background service started
2025-06-26 18:07:33,533 - root - INFO - Executing strategy (run #1)...
2025-06-26 18:07:33,534 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 18:07:33,534 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 18:07:33,535 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 18:07:33,535 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 18:07:33,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:07:39,551 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 18:07:39,619 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:07:39,620 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 18:07:39,621 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 18:07:39,621 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 18:07:39,622 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 18:07:39,646 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,647 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,648 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,648 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 18:07:39,649 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,662 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,662 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,663 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,663 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 18:07:39,664 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,676 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,676 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,676 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,677 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 18:07:39,677 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,684 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,685 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,685 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,686 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 18:07:39,686 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,698 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,699 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,699 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,699 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 18:07:39,700 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,712 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,712 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,712 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,713 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 18:07:39,713 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,724 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,725 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,725 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,725 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 18:07:39,725 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,741 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,742 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,742 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,742 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 18:07:39,743 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,760 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,760 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,761 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,761 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 18:07:39,761 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,774 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,774 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,775 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,775 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 18:07:39,776 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,782 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,783 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,783 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,784 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 18:07:39,784 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,797 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,797 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,798 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,798 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 18:07:39,798 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,812 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,812 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,813 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,813 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 18:07:39,813 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,827 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:39,827 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,828 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:07:39,828 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 18:07:39,828 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:39,830 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 18:07:39,830 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 18:07:39,830 - root - INFO -   - Number of indicators: 8
2025-06-26 18:07:39,831 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:07:39,831 - root - INFO -   - Combination method: consensus
2025-06-26 18:07:39,831 - root - INFO -   - Long threshold: 0.1
2025-06-26 18:07:39,831 - root - INFO -   - Short threshold: -0.1
2025-06-26 18:07:39,831 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:07:39,831 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:07:39,831 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 18:07:39,832 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-26 18:07:39,832 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 18:07:39,832 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 18:07:39,832 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 18:07:39,832 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 18:07:39,832 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 18:07:39,832 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 18:07:39,832 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 18:07:39,832 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:39,840 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:39,841 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:39,845 - root - INFO - Configuration saved successfully.
2025-06-26 18:07:39,845 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:07:39,846 - root - INFO - Number of trend detection assets: 14
2025-06-26 18:07:39,846 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 18:07:39,846 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:07:39,846 - root - INFO - Number of trading assets: 14
2025-06-26 18:07:39,846 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 18:07:40,052 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:07:40,058 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:40,064 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 18:07:40,070 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:40,070 - root - INFO - Execution context: backtesting
2025-06-26 18:07:40,070 - root - INFO - Execution timing: candle_close
2025-06-26 18:07:40,070 - root - INFO - Ratio calculation method: independent
2025-06-26 18:07:40,070 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 18:07:40,070 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:07:40,072 - root - INFO - MTPI combination method override: consensus
2025-06-26 18:07:40,072 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 18:07:40,072 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 18:07:40,072 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 18:07:40,072 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:07:40,074 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,074 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,075 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,076 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,077 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,077 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,077 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,078 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,078 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,078 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,079 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,079 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,079 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,079 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:07:40,079 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 18:07:40,095 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,097 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,097 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,097 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 18:07:40,110 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,112 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,112 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,112 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:07:40,126 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,128 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,128 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,128 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 18:07:40,136 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,138 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,138 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,139 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 18:07:40,153 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,154 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,155 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,155 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 18:07:40,166 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,168 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,168 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,168 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 18:07:40,185 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,187 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,187 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,187 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 18:07:40,206 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,208 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,211 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,211 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 18:07:40,261 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,266 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,267 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,267 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 18:07:40,287 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,288 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,288 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,288 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 18:07:40,295 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,296 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,297 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,297 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 18:07:40,313 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,314 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,315 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,315 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 18:07:40,331 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,332 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,332 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,332 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 18:07:40,346 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,347 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:07:40,347 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,347 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 18:07:40,347 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:07:40,348 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,348 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,348 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,348 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,348 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,348 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,349 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:07:40,367 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 18:07:40,368 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:07:40,369 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 18:07:40,369 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:07:40,369 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:07:40,377 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:40,377 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 18:07:40,377 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:07:40,377 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:07:40,377 - root - INFO - Override: combination_method = consensus
2025-06-26 18:07:40,378 - root - INFO - Override: long_threshold = 0.1
2025-06-26 18:07:40,378 - root - INFO - Override: short_threshold = -0.1
2025-06-26 18:07:40,378 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 18:07:40,378 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 18:07:40,378 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 18:07:40,379 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 18:07:40,394 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:07:40,394 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:07:40,394 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:07:40,395 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:07:40,395 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:40,396 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 18:07:40,428 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 18:07:40,428 - root - INFO - Generated pgo signals: 256 values
2025-06-26 18:07:40,429 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 18:07:40,429 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 18:07:40,438 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 18:07:40,439 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 18:07:40,439 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 18:07:40,779 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 18:07:40,780 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 18:07:40,819 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 18:07:40,819 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 18:07:40,819 - root - INFO - Generated DEMA Super Score signals
2025-06-26 18:07:40,819 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 18:07:40,891 - root - INFO - Generated DPSD signals
2025-06-26 18:07:40,891 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 18:07:40,891 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 18:07:40,892 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 18:07:40,899 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 18:07:40,899 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 18:07:40,899 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 18:07:40,944 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 18:07:40,945 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 18:07:40,945 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 18:07:41,022 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 18:07:41,027 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 18:07:41,028 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:07:41,028 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 18:07:41,029 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:07:41,029 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 18:07:41,030 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 18:07:41,035 - root - INFO - Configuration saved successfully.
2025-06-26 18:07:41,035 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 18:07:41,036 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:07:41,044 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:41,044 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 18:07:41,044 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 18:07:41,044 - root - INFO - Using ratio calculation method: independent
2025-06-26 18:07:41,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,078 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:41,093 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:41,094 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,106 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:41,111 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:41,126 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:07:41,126 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,139 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:07:41,144 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:41,159 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:41,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,173 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:41,178 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:41,193 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:41,194 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,206 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:41,210 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:41,225 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:07:41,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,241 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:07:41,248 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:41,265 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:41,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,281 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:41,289 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:41,305 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:07:41,305 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,319 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:07:41,325 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:41,338 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:07:41,339 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,352 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:07:41,358 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:41,374 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:41,374 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,388 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:41,395 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:41,410 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:41,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,425 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:41,430 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:41,448 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,465 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:41,485 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:07:41,485 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,501 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:07:41,507 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:41,524 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:41,524 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,538 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:41,543 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:41,558 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,603 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:41,639 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:41,640 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,659 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:41,665 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:41,684 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:41,684 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,701 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:41,708 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:41,727 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:41,727 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,745 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:41,751 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:41,769 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:07:41,769 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,785 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:07:41,792 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:41,809 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:41,809 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,825 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:41,829 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:41,846 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:41,847 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,860 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:41,866 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:41,886 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:41,886 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,902 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:41,908 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:41,927 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:41,927 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,942 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:41,946 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:41,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:41,980 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:41,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,014 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:42,029 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:07:42,029 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,043 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:07:42,048 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:42,064 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:07:42,064 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,079 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:07:42,083 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:42,100 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,118 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:42,135 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:42,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,151 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:42,158 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:42,173 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:42,175 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,188 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:42,194 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:42,211 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:07:42,211 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,226 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:07:42,231 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:42,247 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:07:42,247 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,260 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:07:42,265 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:42,281 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:42,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,296 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:42,301 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:42,317 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:42,317 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,332 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:42,337 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:42,356 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:07:42,356 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,371 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:07:42,376 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:42,393 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:07:42,393 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,408 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:07:42,413 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:42,431 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:42,431 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,445 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:42,449 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:42,466 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:42,466 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,481 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:42,486 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:42,503 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:42,503 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,517 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:42,522 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:42,539 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:42,540 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,554 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:42,560 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:42,576 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,595 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:42,613 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,633 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:42,650 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,667 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:42,683 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:07:42,684 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,698 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:07:42,703 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:42,720 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,742 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:42,758 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,777 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:42,793 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:42,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,808 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:42,812 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:42,830 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,851 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:42,869 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,896 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:42,914 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:42,914 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,931 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:42,936 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:42,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:42,982 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:42,997 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,017 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:43,033 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:07:43,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,047 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:07:43,051 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:43,069 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,089 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:43,106 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,132 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:43,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:07:43,158 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:43,159 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,178 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:43,184 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:43,210 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:43,211 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,229 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:43,237 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:43,263 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:07:43,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,277 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:07:43,283 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:43,299 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:07:43,299 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,315 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:07:43,319 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:43,341 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:43,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,358 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:43,364 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:43,381 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:43,381 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,396 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:43,401 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:43,416 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:43,416 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,431 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:07:43,435 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:43,453 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,472 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:43,490 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,509 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:43,526 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:07:43,526 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,538 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:07:43,544 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:43,560 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,579 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:43,595 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,613 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:43,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,648 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:43,662 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,680 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:43,695 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,713 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:43,729 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:43,730 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,744 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:43,748 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:43,763 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:43,764 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,778 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:43,782 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:43,796 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:43,797 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,811 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:43,815 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:43,832 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,851 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:43,867 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,887 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:43,904 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:07:43,904 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,921 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:07:43,929 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:43,947 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:43,969 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:43,988 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:43,988 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,007 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:44,014 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:44,032 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,053 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:44,076 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,099 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:44,123 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,148 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:44,166 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,185 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:44,209 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,230 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:44,248 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:07:44,249 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,264 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:07:44,269 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:44,288 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,309 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:44,326 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:07:44,327 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,342 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:07:44,346 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:44,362 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,379 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:44,397 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,418 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:44,435 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,455 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:44,473 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,497 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:44,515 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,533 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:44,549 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:44,550 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,566 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:44,571 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:44,589 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,610 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:44,627 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,645 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:44,664 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:07:44,664 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,679 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:07:44,683 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:44,700 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,718 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:44,736 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:07:44,737 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,752 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:07:44,758 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:44,775 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:44,775 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,788 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:44,794 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:44,809 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:07:44,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,825 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:07:44,829 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:44,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,863 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:44,879 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:44,879 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,894 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:44,898 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:44,912 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:44,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,927 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:44,931 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:44,952 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:44,976 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:44,992 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:44,993 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,005 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:45,010 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:45,026 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,044 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:45,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,078 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:45,093 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,113 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:45,127 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,145 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:45,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,179 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:45,198 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:07:45,198 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,214 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:07:45,219 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:45,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,267 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:45,285 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,308 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:45,328 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,348 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:45,366 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:45,367 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,383 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:45,389 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:45,408 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,427 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:45,444 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,464 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:45,479 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,498 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:45,515 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,536 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:45,554 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,574 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:45,589 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:45,591 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,605 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:45,609 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:45,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,644 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:45,660 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:45,660 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,676 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:45,680 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:45,697 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:45,697 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,713 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:45,717 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:45,735 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:45,735 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,749 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:45,753 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:45,771 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,791 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:45,805 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:45,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,820 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:45,827 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:45,843 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:45,844 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,859 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:45,863 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:45,880 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,899 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:45,915 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,934 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:45,951 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:45,969 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:45,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,005 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:46,022 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,042 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:46,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,078 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:46,092 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,112 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:46,126 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,144 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:46,159 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:07:46,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,175 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:07:46,179 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:46,194 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:46,194 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,209 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:07:46,213 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:46,229 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:46,229 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,242 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:07:46,247 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:46,262 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:07:46,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,278 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:07:46,282 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:46,298 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,317 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:46,332 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,351 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:46,368 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,392 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:46,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,433 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:46,449 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,471 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:46,497 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,533 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:46,553 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,583 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:46,603 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:46,603 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,623 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:46,628 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:46,650 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:46,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,674 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:07:46,682 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:46,701 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:46,702 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,726 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:46,734 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:46,753 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:07:46,753 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,770 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:07:46,775 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:46,791 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:07:46,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,804 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:07:46,810 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:46,825 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:46,826 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,837 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:46,843 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:46,859 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:07:46,859 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,875 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:07:46,879 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:46,895 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:07:46,896 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,910 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:07:46,915 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:46,930 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,949 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:46,968 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:46,969 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:46,983 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:46,988 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:47,003 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,024 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:47,040 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,059 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:47,078 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,078 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,093 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,097 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:47,113 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:47,113 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,127 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:47,131 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:47,147 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:47,148 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,163 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:07:47,168 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:47,185 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:07:47,185 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,198 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:07:47,203 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:47,218 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:07:47,218 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,233 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:07:47,239 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:47,254 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,255 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,271 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,277 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:47,292 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:07:47,292 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,305 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:07:47,311 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:47,326 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:47,327 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,340 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:07:47,345 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:47,361 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,362 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,377 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,381 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:47,399 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,423 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:47,440 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,458 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:07:47,474 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,493 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:07:47,510 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,528 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:07:47,545 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,566 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:07:47,585 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:47,586 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,602 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:07:47,609 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:07:47,629 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:47,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,644 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:07:47,651 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:07:47,670 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:47,670 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,687 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:07:47,694 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:07:47,714 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:07:47,714 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,732 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:07:47,738 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:07:47,754 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:07:47,754 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,768 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:07:47,774 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:07:47,788 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:07:47,788 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,802 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:07:47,807 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:07:47,821 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,841 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:07:47,857 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,858 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,870 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:07:47,876 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:07:47,895 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,913 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:07:47,929 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:07:47,948 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:07:49,588 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 18:07:49,588 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 18:07:49,588 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 18:07:49,591 - root - INFO - Date ranges for each asset:
2025-06-26 18:07:49,592 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,592 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,592 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,592 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,593 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,593 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,593 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,593 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,594 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:07:49,595 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 18:07:49,597 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 18:07:49,598 - root - INFO -    Execution Method: candle_close
2025-06-26 18:07:49,598 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 18:07:49,598 - root - INFO -    Signal generated and executed immediately
2025-06-26 18:07:49,602 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,602 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,602 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,602 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,602 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,603 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,604 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,604 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 18:07:49,604 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 18:07:49,604 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 18:07:49,604 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,604 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:07:49,604 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 18:07:49,605 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,605 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,605 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,605 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,605 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,606 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,607 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,607 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,608 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,608 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,608 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,608 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,609 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,610 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 18:07:49,610 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 18:07:49,610 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 18:07:49,610 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,611 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:07:49,611 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:07:49,611 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 18:07:49,612 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,612 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,613 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,613 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,613 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,613 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,613 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,613 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,614 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,615 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,615 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,615 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,615 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,615 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,615 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:07:49,617 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 18:07:49,617 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 18:07:49,617 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 18:07:49,617 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,617 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:07:49,617 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:07:49,617 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 18:07:49,619 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 18:07:49,619 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 18:07:49,619 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 18:07:49,619 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,619 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:07:49,619 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:07:49,619 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 18:07:49,620 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 18:07:49,620 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 18:07:49,620 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 18:07:49,620 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,620 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:07:49,620 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:07:49,620 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 18:07:49,624 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 18:07:49,625 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 18:07:49,625 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 18:07:49,625 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,625 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:07:49,625 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:07:49,626 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 18:07:49,629 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 18:07:49,629 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 18:07:49,629 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 18:07:49,629 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,629 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:07:49,629 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:07:49,629 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 18:07:49,631 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 18:07:49,631 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 18:07:49,631 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 18:07:49,631 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,631 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:07:49,631 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:07:49,631 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 18:07:49,632 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 18:07:49,632 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 18:07:49,632 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 18:07:49,632 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,632 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:07:49,632 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:07:49,632 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 18:07:49,633 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 18:07:49,634 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 18:07:49,634 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 18:07:49,634 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,634 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:07:49,634 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 18:07:49,634 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 18:07:49,635 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 18:07:49,635 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 18:07:49,635 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 18:07:49,635 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,635 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 18:07:49,635 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:07:49,635 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 18:07:49,636 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 18:07:49,637 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 18:07:49,637 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 18:07:49,637 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,637 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:07:49,637 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 18:07:49,637 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 18:07:49,638 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 18:07:49,638 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 18:07:49,638 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 18:07:49,638 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,638 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 18:07:49,638 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:07:49,638 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:07:49,639 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 18:07:49,640 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 18:07:49,640 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 18:07:49,640 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,640 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:07:49,640 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:07:49,640 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 18:07:49,642 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 18:07:49,642 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 18:07:49,642 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 18:07:49,643 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,643 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:07:49,643 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:07:49,643 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 18:07:49,649 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 18:07:49,649 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 18:07:49,649 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 18:07:49,650 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,650 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:07:49,650 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 18:07:49,650 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 18:07:49,652 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 18:07:49,653 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 18:07:49,653 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 18:07:49,653 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,653 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 18:07:49,653 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 18:07:49,653 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 18:07:49,659 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 18:07:49,659 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 18:07:49,659 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 18:07:49,659 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,659 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 18:07:49,660 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:07:49,660 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:07:49,662 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 18:07:49,662 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 18:07:49,663 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 18:07:49,663 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,663 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:07:49,663 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:07:49,663 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 18:07:49,664 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 18:07:49,664 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 18:07:49,665 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 18:07:49,665 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,665 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:07:49,665 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:07:49,665 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:07:49,666 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 18:07:49,666 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 18:07:49,666 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 18:07:49,666 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,666 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:07:49,666 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:07:49,666 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 18:07:49,676 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 18:07:49,676 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 18:07:49,676 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 18:07:49,676 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:07:49,676 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:07:49,676 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:07:49,677 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 18:07:49,705 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 18:07:49,705 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:07:49,705 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:07:49,706 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:07:49,706 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:07:49,706 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:07:49,707 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 18:07:49,707 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:07:49,707 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 18:07:49,707 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 18:07:49,707 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 18:07:49,708 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 18:07:49,708 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 18:07:49,708 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 18:07:49,708 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:07:49,708 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 18:07:49,709 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 18:07:49,709 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 18:07:49,709 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:07:49,709 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 18:07:49,709 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:07:49,709 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 18:07:49,709 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 18:07:49,711 - root - INFO - Strategy execution completed in 0s
2025-06-26 18:07:49,711 - root - INFO - DEBUG: self.elapsed_time = 0.12204766273498535 seconds
2025-06-26 18:07:49,714 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 18:07:49,714 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 18:07:49,714 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 18:07:49,714 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 18:07:49,715 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 18:07:49,717 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,719 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,720 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,721 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,722 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,723 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,724 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,726 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,727 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,728 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,729 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,730 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,730 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,731 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:07:49,734 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 18:07:49,734 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 18:07:49,736 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 18:07:49,736 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 18:07:49,738 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 18:07:49,738 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 18:07:49,740 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 18:07:49,740 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 18:07:49,743 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 18:07:49,743 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 18:07:49,745 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 18:07:49,745 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 18:07:49,746 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 18:07:49,747 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 18:07:49,748 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 18:07:49,748 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 18:07:49,749 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 18:07:49,749 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 18:07:49,752 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 18:07:49,752 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 18:07:49,754 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 18:07:49,754 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 18:07:49,755 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 18:07:49,756 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 18:07:49,757 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 18:07:49,758 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 18:07:49,759 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 18:07:49,760 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 18:07:49,761 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:07:49,768 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:49,777 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 18:07:49,858 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:07:49,863 - root - INFO - Configuration loaded successfully.
2025-06-26 18:07:51,139 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,139 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,139 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,139 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,140 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,142 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,142 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 18:07:51,142 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 18:07:51,142 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,143 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,143 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,143 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,143 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,143 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,144 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,144 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,144 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,144 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,144 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,145 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,145 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,145 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:07:51,161 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 18:07:51,162 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:07:51,165 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 18:07:51,165 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 18:07:51,168 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 18:07:51,168 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:07:51,168 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:07:51,169 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:07:51,169 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:07:51,169 - root - ERROR - [DEBUG] CONVERSION - TIED USDT ASSETS: ['BTC/USDT', 'TRX/USDT'] (score: 12.0)
2025-06-26 18:07:51,169 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 18:07:51,170 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 12.0)
2025-06-26 18:07:51,170 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 18:07:51,170 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 18:07:51,170 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 18:07:51,170 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 18:07:51,171 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 18:07:51,172 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 18:07:51,172 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 18:07:51,172 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-26 18:07:51,172 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 18:07:51,173 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 18:07:51,173 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 18:07:51,173 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 18:07:51,173 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:07:51,174 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:07:51,174 - root - ERROR - [DEBUG] CONVERSION - TIED EUR ASSETS: ['BTC/EUR', 'TRX/EUR'] (score: 12.0)
2025-06-26 18:07:51,175 - root - ERROR - [DEBUG] CONVERSION - First EUR asset (should be selected): BTC/EUR
2025-06-26 18:07:51,175 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:07:51,183 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_180733.csv
2025-06-26 18:07:51,184 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_180733.csv
2025-06-26 18:07:51,184 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 18:07:51,184 - root - INFO - Results type: <class 'dict'>
2025-06-26 18:07:51,185 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 18:07:51,185 - root - INFO - Success flag set to: True
2025-06-26 18:07:51,185 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 18:07:51,185 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:07:51,185 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 18:07:51,186 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:07:51,186 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 18:07:51,186 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 18:07:51,186 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 18:07:51,186 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 18:07:51,186 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 18:07:51,186 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 18:07:51,186 - root - INFO -   - success: <class 'bool'>
2025-06-26 18:07:51,187 - root - INFO -   - message: <class 'str'>
2025-06-26 18:07:51,187 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 18:07:51,187 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 18:07:51,187 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 18:07:51,188 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 18:07:51,188 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 18:07:51,210 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 18:07:51,211 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:07:51,211 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 18:07:51,211 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 18:07:51,211 - root - INFO - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 18:07:51,211 - root - INFO - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 18:07:51,213 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-26 18:07:51,213 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-26 18:07:51,213 - root - WARNING - [DEBUG] TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 18:07:51,213 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 18:07:51,213 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 18:07:51,213 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 18:07:51,213 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 18:07:51,213 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 18:07:51,213 - root - INFO - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 18:07:51,213 - root - INFO - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 18:07:51,214 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 18:07:51,214 - root - INFO - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 18:07:51,214 - root - INFO - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-26 18:07:51,214 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-26 18:07:51,219 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 18:07:51,219 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 18:07:51,549 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 18:07:51,550 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-26 18:07:51,608 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-26 18:07:51,609 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1750954067517, 'datetime': '2025-06-26T16:07:47.517Z', 'high': 0.23507, 'low': 0.23076, 'bid': 0.23161, 'bidVolume': 8921.197603, 'ask': 0.2317, 'askVolume': 4866.774698, 'vwap': 0.2327747830852619, 'open': 0.233, 'close': 0.23173, 'last': 0.23173, 'previousClose': None, 'change': -0.00127, 'percentage': -0.5450643776824033, 'average': 0.232365, 'baseVolume': 1388902.109408, 'quoteVolume': 323301.3872441099, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1750867667517, 'timestamp': 1750954067517, 'open': '0.233', 'openTimestamp': 1750867805109, 'high': '0.23507', 'low': '0.23076', 'last': '0.23173', 'closeTimestamp': 1750953972120, 'bid': '0.2316100', 'bidSize': '8921.197603', 'ask': '0.2317000', 'askSize': '4866.774698', 'volume': '1388902.109408', 'volumeQuote': '323301.38724410988'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 18:07:51,609 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23173
2025-06-26 18:07:51,609 - root - INFO - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23173
2025-06-26 18:07:51,609 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 18:07:51,609 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 18:07:51,609 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 18:07:51,610 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 18:07:51,610 - root - INFO - Available balance for EUR: 100.********
2025-06-26 18:07:51,612 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 18:07:51,612 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 18:07:51,612 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 18:07:51,612 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 18:07:51,613 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 18:07:51,613 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 18:07:51,614 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:07:51,614 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.**************, price: 0.23173
2025-06-26 18:07:51,614 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 18:07:51,615 - root - INFO - Order fee: 0.******** EUR
2025-06-26 18:07:51,615 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 18:07:51,629 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 18:07:51,629 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:07:51,629 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 18:07:51,642 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 18:07:51,642 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:07:51,642 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 18:07:51,642 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.93790186855392, 'price': 0.23173, 'order': {'id': 'paper-1750954071-TRX/EUR-buy-42.**************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.**************, 'price': 0.23173, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.**************, 'remaining': 0, 'timestamp': 1750954071613, 'datetime': '2025-06-26T18:07:51.613861', 'trades': [], 'average': 0.23173, 'average_price': 0.23173}, 'filled_amount': 42.**************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T18:07:51.615861'}
2025-06-26 18:07:51,708 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:07:51,729 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 18:07:51,729 - root - INFO - Asset scores (sorted by score):
2025-06-26 18:07:51,729 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,729 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 18:07:51,730 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,730 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,730 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,730 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,730 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,730 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,730 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,731 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,731 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,731 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,731 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,731 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:07:51,731 - root - INFO - Rejected assets:
2025-06-26 18:07:51,731 - root - INFO -   BTC/EUR: reason=Failed to trade, score=12.0, rank=1
2025-06-26 18:07:51,731 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 12.0) - Failed to trade
2025-06-26 18:07:51,732 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 18:07:51,732 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 18:07:51,732 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:07:51,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:07:51,787 - root - INFO - Strategy execution completed successfully in 18.25 seconds
2025-06-26 18:07:51,791 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 18:07:53,170 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:08:03,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:08:13,205 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:08:23,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:08:33,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:08:43,240 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:08:53,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:09:03,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:09:13,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:09:23,304 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:09:33,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:09:43,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:09:53,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:03,355 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:13,371 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:23,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:33,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:43,424 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:47,956 - root - INFO - Received signal 2, shutting down...
2025-06-26 18:10:53,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:10:56,801 - root - INFO - Network watchdog stopped
2025-06-26 18:10:56,802 - root - INFO - Network watchdog stopped
2025-06-26 18:10:56,802 - root - INFO - Background service stopped
2025-06-26 18:10:56,919 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
