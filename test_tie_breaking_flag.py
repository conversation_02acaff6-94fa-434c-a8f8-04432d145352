#!/usr/bin/env python3
"""
Test script to verify that the tie-breaking strategy flag works in main_program.py
"""

import subprocess
import sys
import os

def test_tie_breaking_flag():
    """Test that the tie-breaking strategy flag is properly recognized."""
    
    print("Testing tie-breaking strategy flag implementation...")
    print("=" * 60)
    
    # Test 1: Check that --help shows the new flag
    print("\n1. Testing --help output...")
    try:
        result = subprocess.run([
            sys.executable, "main_program.py", "--help"
        ], capture_output=True, text=True, timeout=30)
        
        if "--tie-breaking-strategy" in result.stdout:
            print("✅ --tie-breaking-strategy flag found in help")
        else:
            print("❌ --tie-breaking-strategy flag NOT found in help")
            print("Help output snippet:")
            print(result.stdout[-500:])  # Show last 500 chars
            
    except Exception as e:
        print(f"❌ Error testing help: {e}")
    
    # Test 2: Test momentum strategy (default)
    print("\n2. Testing momentum strategy (default)...")
    test_command = [
        sys.executable, "main_program.py",
        "--assets", "BTC/USDT", "ETH/USDT", "SOL/USDT",
        "--timeframe", "1d",
        "--analysis-start-date", "2024-01-01",
        "--analysis-end-date", "2024-01-05",  # Very short period for quick test
        "--n-assets", "1",
        "--transaction-fee", "0.001",
        "--no-cache",  # Don't use cache for clean test
        "--tie-breaking-strategy", "momentum"
    ]
    
    try:
        result = subprocess.run(test_command, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Momentum strategy test completed successfully")
            if "Tie-breaking strategy: momentum" in result.stderr:
                print("✅ Momentum strategy properly logged")
            else:
                print("⚠️  Momentum strategy not found in logs")
        else:
            print(f"❌ Momentum strategy test failed with return code {result.returncode}")
            print("Error output:")
            print(result.stderr[-500:])  # Show last 500 chars
            
    except subprocess.TimeoutExpired:
        print("❌ Momentum strategy test timed out")
    except Exception as e:
        print(f"❌ Error testing momentum strategy: {e}")
    
    # Test 3: Test incumbent strategy
    print("\n3. Testing incumbent strategy...")
    test_command[test_command.index("momentum")] = "incumbent"  # Change to incumbent
    
    try:
        result = subprocess.run(test_command, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Incumbent strategy test completed successfully")
            if "Tie-breaking strategy: incumbent" in result.stderr:
                print("✅ Incumbent strategy properly logged")
            else:
                print("⚠️  Incumbent strategy not found in logs")
        else:
            print(f"❌ Incumbent strategy test failed with return code {result.returncode}")
            print("Error output:")
            print(result.stderr[-500:])  # Show last 500 chars
            
    except subprocess.TimeoutExpired:
        print("❌ Incumbent strategy test timed out")
    except Exception as e:
        print(f"❌ Error testing incumbent strategy: {e}")
    
    # Test 4: Test invalid strategy
    print("\n4. Testing invalid strategy...")
    test_command[test_command.index("incumbent")] = "invalid"  # Change to invalid
    
    try:
        result = subprocess.run(test_command, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            print("✅ Invalid strategy properly rejected")
            if "invalid choice" in result.stderr.lower():
                print("✅ Proper error message shown")
            else:
                print("⚠️  Expected error message not found")
        else:
            print("❌ Invalid strategy was accepted (should have been rejected)")
            
    except Exception as e:
        print(f"❌ Error testing invalid strategy: {e}")
    
    print("\n" + "=" * 60)
    print("Tie-breaking strategy flag test completed!")
    print("\nYou can now use the flag in your backtesting:")
    print("  --tie-breaking-strategy momentum   (default, dictionary order for ties)")
    print("  --tie-breaking-strategy incumbent  (keep current leader when tied)")

if __name__ == "__main__":
    test_tie_breaking_flag()
