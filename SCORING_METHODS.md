# Asset Scoring Methods: Manual Inversion vs Independent Calculation

## Overview

This document explains the two different methods for calculating asset scores in the pairwise comparison system, their mathematical properties, bias characteristics, and practical implications for asset selection.

## Scoring System Fundamentals

### Basic Concept
- Assets are compared pairwise using technical indicators
- Each comparison yields a binary result: Asset A beats Asset B (score +1) or loses (score 0)
- Final asset score = sum of wins across all pairwise comparisons
- Asset with highest score is selected for allocation

### Mathematical Properties
- **N assets** → **N×(N-1)/2 total pairwise comparisons**
- **Total points distributed** = N×(N-1)/2
- **Maximum possible score per asset** = N-1 (beats all other assets)
- **Minimum possible score per asset** = 0 (loses to all other assets)

**Example with 14 assets:**
- Total comparisons: 14×13/2 = 91
- Total points distributed: 91
- Max score per asset: 13
- Min score per asset: 0

## Method 1: Manual Inversion

### How It Works
```
For each unique pair (A, B):
1. Calculate technical indicator comparison: A vs B
2. If A beats B: A gets +1 point, B gets +0 points
3. Automatically invert for the reverse pair: B vs A gets opposite result
4. No separate calculation needed for (B, A)
```

### Mathematical Characteristics
- **Forced complementarity**: If A beats B, then B automatically loses to A
- **Guaranteed point conservation**: Total points always equals N×(N-1)/2
- **Bias towards asset list order**: Earlier assets in the list get evaluated first

### Example Calculation
```
Assets: [BTC, ETH, SOL]
Pairs calculated: BTC/ETH, BTC/SOL, ETH/SOL

If BTC beats ETH: BTC+1, ETH+0
If BTC beats SOL: BTC+1, SOL+0  
If ETH beats SOL: ETH+1, SOL+0

Final scores: BTC=2, ETH=1, SOL=0
Reverse pairs automatically determined:
- ETH/BTC = 0 (since BTC/ETH = 1)
- SOL/BTC = 0 (since BTC/SOL = 1)
- SOL/ETH = 0 (since ETH/SOL = 1)
```

### Advantages
- **Computational efficiency**: Only N×(N-1)/2 calculations needed
- **Perfect point conservation**: No mathematical inconsistencies
- **Deterministic**: Same input always produces same output

### Disadvantages
- **List order bias**: Assets appearing earlier in the list have systematic advantage
- **Forced relationships**: May not reflect true market dynamics
- **Ties still possible**: Despite common misconception, ties can occur mathematically

## Method 2: Independent Calculation

### How It Works
```
For each pair (A, B) AND (B, A):
1. Calculate technical indicator comparison: A vs B
2. Calculate technical indicator comparison: B vs A (separately)
3. Each direction gets independent result
4. No forced inverse relationship
```

### Mathematical Characteristics
- **True independence**: Each comparison calculated separately
- **No forced complementarity**: A beating B doesn't guarantee B loses to A
- **Removes list order bias**: All comparisons treated equally
- **Higher tie probability**: Independent calculations more likely to produce equal scores

### Example Calculation
```
Assets: [BTC, ETH, SOL]
All pairs calculated independently:
BTC/ETH, ETH/BTC, BTC/SOL, SOL/BTC, ETH/SOL, SOL/ETH

Possible results:
- BTC/ETH = 1, ETH/BTC = 0 (consistent)
- BTC/ETH = 1, ETH/BTC = 1 (both win - market conditions dependent)
- BTC/ETH = 0, ETH/BTC = 0 (both lose - possible in sideways markets)

Final scores: Sum of all wins for each asset
```

### Advantages
- **Eliminates list order bias**: Fair treatment of all assets
- **Market reality**: Reflects actual market conditions where A>B doesn't always mean B<A
- **Flexibility**: Can capture complex market relationships

### Disadvantages
- **Higher computational cost**: N×(N-1) calculations needed
- **Point conservation not guaranteed**: Total points may vary
- **Higher tie probability**: More likely to produce equal scores

## Tie Scenarios Analysis

### Manual Inversion Ties
**Common Misconception**: "Manual inversion guarantees unique scores"
**Reality**: Ties are mathematically possible

**Example with 14 assets (from real data):**
```
Total points: 91
Tie scenario: 3 assets with 12 points each = 36 points
Remaining: 11 assets sharing 55 points
Result: 91 total points ✓ (mathematically valid)
```

### Independent Calculation Ties
- **More frequent**: Independent calculations increase tie probability
- **Market-driven**: Ties reflect actual market conditions
- **Natural occurrence**: Multiple assets can have similar performance

## Practical Implications

### When to Use Manual Inversion
- **Computational efficiency is priority**
- **Consistent, deterministic results needed**
- **List order bias is acceptable or managed**
- **Legacy system compatibility**

### When to Use Independent Calculation
- **Maximum fairness is priority**
- **Removing systematic biases is critical**
- **Computational resources are available**
- **Market reality reflection is important**

## Tie-Breaking Strategy Integration

Both scoring methods require robust tie-breaking strategies:

### Incumbent Strategy
- **Philosophy**: "Keep current leader until definitively beaten"
- **Best with**: Independent calculation (removes bias, conservative selection)
- **Use case**: Stable, low-turnover portfolios

### Momentum Strategy  
- **Philosophy**: "Favor assets with recent momentum"
- **Best with**: Manual inversion (leverages list order for momentum bias)
- **Use case**: Trend-following, higher-turnover strategies

## Configuration Recommendations

### Conservative Approach
```yaml
ratio_calculation: independent
tie_breaking_strategy: incumbent
```
- Maximum fairness + stability
- Lowest bias + conservative switching
- Best for long-term strategies

### Aggressive Approach
```yaml
ratio_calculation: manual_inversion  
tie_breaking_strategy: momentum
```
- Efficient calculation + trend following
- Acceptable bias + responsive switching
- Best for short-term strategies

### Balanced Approach
```yaml
ratio_calculation: independent
tie_breaking_strategy: momentum
```
- Fair calculation + responsive selection
- Removes bias + captures trends
- Best for medium-term strategies

## Mathematical Validation

### Point Conservation Check
```python
# For manual inversion
total_points = sum(all_asset_scores)
expected_points = n_assets * (n_assets - 1) / 2
assert total_points == expected_points

# For independent calculation  
# No guaranteed relationship - depends on market conditions
```

### Tie Frequency Analysis
- **Manual inversion**: Lower tie frequency, but ties possible
- **Independent calculation**: Higher tie frequency, more market-realistic

## Conclusion

The choice between manual inversion and independent calculation represents a fundamental trade-off:

- **Manual Inversion**: Efficiency and determinism vs. bias and forced relationships
- **Independent Calculation**: Fairness and market reality vs. computational cost and tie frequency

The optimal choice depends on your specific requirements for bias tolerance, computational resources, and portfolio management philosophy. For maximum fairness and minimal bias, independent calculation with incumbent tie-breaking is recommended.
