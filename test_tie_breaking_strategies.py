#!/usr/bin/env python3
"""
Test script to verify both incumbent and momentum tie-breaking strategies work correctly.
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tie_breaking_strategies():
    """Test both incumbent and momentum tie-breaking strategies."""
    
    print("=" * 80)
    print("TESTING TIE-BREAKING STRATEGIES")
    print("=" * 80)
    
    # Test data setup
    tied_scores = {
        'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 
        'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 
        'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 
        'BNB/EUR': 11.0, 'DOT/EUR': 1.0
    }
    
    # Historical series where TR<PERSON> was the incumbent
    dates = pd.date_range(start='2025-06-21', end='2025-06-25', freq='D')
    best_asset_series = pd.Series(['TRX/EUR'] * len(dates), index=dates)
    incumbent_from_series = best_asset_series.iloc[-1]
    
    print("SETUP:")
    print("-" * 40)
    print(f"Incumbent from series: {incumbent_from_series}")
    
    max_score = max(tied_scores.values())
    tied_assets = [asset for asset, score in tied_scores.items() if score == max_score]
    print(f"Current tied assets: {tied_assets} (score: {max_score})")
    print(f"Dictionary order: {list(tied_scores.keys())}")
    
    # Test 1: Incumbent Approach
    print(f"\n1. INCUMBENT APPROACH:")
    print("-" * 40)
    
    if incumbent_from_series in tied_assets:
        incumbent_result = incumbent_from_series
        print(f"✅ Incumbent {incumbent_from_series} is among tied assets")
        print(f"✅ Keep incumbent: {incumbent_result}")
    else:
        from src.scoring import find_best_asset_for_day
        incumbent_result = find_best_asset_for_day(tied_scores)
        print(f"❌ Incumbent {incumbent_from_series} not among tied assets")
        print(f"❌ Use tie-breaking: {incumbent_result}")
    
    # Test 2: Momentum Approach
    print(f"\n2. MOMENTUM APPROACH:")
    print("-" * 40)
    
    from src.scoring import find_best_asset_for_day
    momentum_result = find_best_asset_for_day(tied_scores)
    print(f"Always use current day tie-breaking: {momentum_result}")
    print(f"Favors dictionary order (first asset in tied list)")
    
    # Compare results
    print(f"\n3. COMPARISON:")
    print("-" * 40)
    print(f"Incumbent approach selects: {incumbent_result}")
    print(f"Momentum approach selects:  {momentum_result}")
    
    if incumbent_result == momentum_result:
        print("✅ Both approaches agree")
    else:
        print("❌ Approaches disagree - this is the key difference!")
        print(f"   Incumbent keeps the proven leader: {incumbent_result}")
        print(f"   Momentum favors dictionary order: {momentum_result}")
    
    # Test edge cases
    print(f"\n4. EDGE CASE TESTING:")
    print("-" * 40)
    
    # Case 1: Clear winner (no tie)
    clear_winner_scores = tied_scores.copy()
    clear_winner_scores['BTC/EUR'] = 15.0  # BTC becomes clear winner
    
    print("Case 1: BTC becomes clear winner (score 15)")
    from src.scoring import find_best_asset_for_day
    clear_winner = find_best_asset_for_day(clear_winner_scores)
    print(f"Both approaches would select: {clear_winner}")
    
    # Case 2: Incumbent drops out of tie
    incumbent_drops_scores = tied_scores.copy()
    incumbent_drops_scores['TRX/EUR'] = 10.0  # TRX drops below tie
    incumbent_drops_scores['BNB/EUR'] = 12.0  # BNB joins the tie
    
    print(f"\nCase 2: Incumbent (TRX) drops to 10, BNB joins tie at 12")
    max_score_2 = max(incumbent_drops_scores.values())
    tied_assets_2 = [asset for asset, score in incumbent_drops_scores.items() if score == max_score_2]
    print(f"New tied assets: {tied_assets_2}")
    
    # Incumbent approach
    if incumbent_from_series in tied_assets_2:
        incumbent_result_2 = incumbent_from_series
    else:
        incumbent_result_2 = find_best_asset_for_day(incumbent_drops_scores)
    
    # Momentum approach
    momentum_result_2 = find_best_asset_for_day(incumbent_drops_scores)
    
    print(f"Incumbent approach: {incumbent_result_2} (incumbent dropped, use tie-breaking)")
    print(f"Momentum approach:  {momentum_result_2} (always use tie-breaking)")
    
    # Summary
    print(f"\n5. STRATEGY SUMMARY:")
    print("-" * 40)
    print("INCUMBENT APPROACH (Conservative):")
    print("  ✅ Reduces unnecessary switching")
    print("  ✅ Stable in choppy markets")
    print("  ✅ 'Don't change horses mid-race'")
    print("  ❌ May miss early momentum")
    
    print("\nMOMENTUM APPROACH (Aggressive):")
    print("  ✅ Catches momentum early")
    print("  ✅ Responsive to changes")
    print("  ✅ 'Catch the rising star'")
    print("  ❌ May cause more switching")
    
    print(f"\n" + "=" * 80)

if __name__ == "__main__":
    test_tie_breaking_strategies()
