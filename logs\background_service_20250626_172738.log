2025-06-26 17:27:38,738 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:27:39,634 - root - INFO - Loaded 43 trade records from logs/trades\trade_log_********.json
2025-06-26 17:27:39,635 - root - INFO - Loaded 26 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:27:39,635 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:27:40,705 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:40,715 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:40,720 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:40,728 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:40,728 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:40,733 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:40,734 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:27:40,734 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:27:41,726 - root - INFO - Telegram command handlers registered
2025-06-26 17:27:41,727 - root - INFO - Telegram bot polling started
2025-06-26 17:27:41,727 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:27:41,727 - root - INFO - Telegram notification channel initialized
2025-06-26 17:27:41,728 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:27:41,729 - root - INFO - Loaded 24 templates from file
2025-06-26 17:27:41,729 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:27:41,729 - root - INFO - Notification manager initialized
2025-06-26 17:27:41,730 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:27:41,730 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:27:41,731 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:27:41,731 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:27:41,733 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:27:41,737 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:27:41,737 - root - INFO - Recovery manager initialized
2025-06-26 17:27:41,738 - root - ERROR - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:27:41,738 - root - ERROR - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:27:41,738 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:41,749 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:41,749 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:27:41,751 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:27:41,751 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:27:41,751 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:27:41,751 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:27:41,752 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:27:41,752 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:27:41,752 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:27:41,752 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:41,764 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:41,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:27:41,812 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:27:41,814 - telegram.ext.Application - INFO - Application started
2025-06-26 17:27:42,044 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:27:42,044 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:27:42,044 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:27:42,045 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:27:42,045 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:27:42,045 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:42,052 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:42,054 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:42,062 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:42,063 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:27:42,069 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:42,069 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:27:42,069 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:27:42,069 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:27:42,069 - root - INFO - Trading mode: paper
2025-06-26 17:27:42,070 - root - INFO - Trading enabled: True
2025-06-26 17:27:42,070 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:27:42,071 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:27:42,071 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:27:42,071 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:27:42,071 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:42,079 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:42,262 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:27:42,263 - root - INFO - Trading enabled in paper mode
2025-06-26 17:27:42,263 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:27:42,264 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:27:42,264 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:27:42,264 - root - INFO - Generated run ID: ********_172742
2025-06-26 17:27:42,264 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:27:42,265 - root - INFO - Background service initialized
2025-06-26 17:27:42,265 - root - INFO - Network watchdog started
2025-06-26 17:27:42,265 - root - INFO - Network watchdog started
2025-06-26 17:27:42,266 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:27:42,267 - root - INFO - Background service started
2025-06-26 17:27:42,267 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:27:42,267 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:27:42,268 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:27:42,269 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:27:42,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:27:48,293 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:27:48,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:27:48,362 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:27:48,362 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:27:48,362 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:27:48,364 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:27:48,393 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,394 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,395 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,395 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:27:48,396 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,409 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,410 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,411 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,411 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:27:48,412 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,419 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,419 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,419 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,419 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:27:48,421 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,437 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,437 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,438 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,439 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:27:48,439 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,457 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,459 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,459 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,459 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:27:48,461 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,477 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,477 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,478 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,479 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:27:48,479 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,496 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,496 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,497 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,497 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:27:48,498 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,517 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,518 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,518 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,518 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:27:48,519 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,536 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,536 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,536 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,537 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:27:48,537 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,549 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,549 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,550 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,550 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:27:48,551 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,565 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,566 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,566 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,566 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:27:48,567 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,583 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,583 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,583 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,583 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:27:48,584 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,598 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,599 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,599 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:27:48,599 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:27:48,600 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,601 - root - INFO - Using 13 trend assets (USDT) for analysis and 13 trading assets (EUR) for execution
2025-06-26 17:27:48,602 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:27:48,602 - root - INFO -   - Number of indicators: 8
2025-06-26 17:27:48,602 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:27:48,602 - root - INFO -   - Combination method: consensus
2025-06-26 17:27:48,602 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:27:48,602 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:27:48,603 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:27:48,603 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:27:48,603 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: NOT FOUND
2025-06-26 17:27:48,603 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 9
2025-06-26 17:27:48,603 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:27:48,603 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:27:48,603 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:27:48,603 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:27:48,603 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:27:48,604 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:27:48,604 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:27:48,604 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:48,611 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:48,611 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:48,617 - root - INFO - Configuration saved successfully.
2025-06-26 17:27:48,617 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:27:48,617 - root - INFO - Number of trend detection assets: 13
2025-06-26 17:27:48,617 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:27:48,617 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:27:48,618 - root - INFO - Number of trading assets: 13
2025-06-26 17:27:48,618 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:27:48,826 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:27:48,834 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:48,841 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:27:48,848 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:48,848 - root - INFO - Execution context: backtesting
2025-06-26 17:27:48,849 - root - INFO - Execution timing: candle_close
2025-06-26 17:27:48,849 - root - INFO - Ratio calculation method: independent
2025-06-26 17:27:48,849 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:27:48,849 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:27:48,850 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:27:48,850 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:27:48,850 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:27:48,850 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:27:48,851 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:27:48,852 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,852 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,852 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,853 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,854 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,854 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,854 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,855 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,855 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,855 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,856 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,856 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,856 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:27:48,857 - root - INFO - Checking cache for 13 symbols (1d)...
2025-06-26 17:27:48,877 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,879 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,879 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,881 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:27:48,898 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,900 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,900 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,900 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:27:48,909 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,913 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,914 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,914 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:27:48,932 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,934 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,934 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,934 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:27:48,951 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,952 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,953 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,953 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:27:48,969 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,971 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,971 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,972 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:27:48,990 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:48,993 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:48,993 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:48,993 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:27:49,014 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,016 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:49,016 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,016 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:27:49,032 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,033 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:49,034 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,034 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:27:49,044 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,045 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:49,046 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,046 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:27:49,062 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,064 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:49,064 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,064 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:27:49,078 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,079 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:49,081 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,081 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:27:49,093 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,094 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:27:49,095 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,095 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:27:49,095 - root - INFO - All 13 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:27:49,095 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,096 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,096 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,096 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,097 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,097 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,097 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,097 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,097 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,097 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,098 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,098 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,098 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:27:49,113 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:27:49,113 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:27:49,113 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:27:49,114 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:27:49,114 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:27:49,121 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:49,121 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:27:49,121 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:27:49,121 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:27:49,121 - root - INFO - Override: combination_method = consensus
2025-06-26 17:27:49,122 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:27:49,122 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:27:49,122 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:27:49,122 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:27:49,123 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:27:49,123 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:27:49,141 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:27:49,142 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:27:49,142 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:27:49,142 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:27:49,142 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:27:49,142 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:27:49,167 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:27:49,167 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:27:49,167 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:27:49,167 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:27:49,177 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:27:49,177 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:27:49,177 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:27:49,525 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:27:49,526 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:27:49,565 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:27:49,566 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:27:49,566 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:27:49,566 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:27:49,647 - root - INFO - Generated DPSD signals
2025-06-26 17:27:49,648 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:27:49,648 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:27:49,648 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:27:49,654 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:27:49,654 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:27:49,655 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:27:49,701 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:27:49,702 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:27:49,702 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:27:49,783 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:27:49,788 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:27:49,789 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:27:49,789 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:27:49,791 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:27:49,792 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:27:49,794 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:27:49,798 - root - INFO - Configuration saved successfully.
2025-06-26 17:27:49,799 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:27:49,799 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:27:49,808 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:49,808 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:27:49,809 - root - INFO - Calculating ratio PGO signals for 13 assets with PGO(35) using full OHLCV data
2025-06-26 17:27:49,809 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:27:49,827 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:49,827 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:49,843 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:49,848 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:49,865 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:27:49,865 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:49,879 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:27:49,886 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:49,904 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:49,904 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:49,921 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:49,929 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:49,946 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:27:49,946 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:49,962 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:27:49,966 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:49,983 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:27:49,983 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:49,996 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:27:50,006 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:50,026 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:27:50,026 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,041 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:27:50,046 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:50,061 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:27:50,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,086 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:27:50,093 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:50,119 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:27:50,119 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,144 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:27:50,152 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:50,184 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:27:50,184 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,216 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:27:50,228 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:50,255 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:50,255 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,281 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:50,290 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:50,336 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,389 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:50,409 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:27:50,409 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,427 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:27:50,431 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:50,451 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:27:50,451 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,462 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:27:50,473 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:50,495 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:50,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,510 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:50,512 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:50,531 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:50,536 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,552 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:50,559 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:50,578 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:27:50,578 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,593 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:27:50,595 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:50,613 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:27:50,613 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,644 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:27:50,646 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:50,662 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:50,662 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,686 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:50,695 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:50,713 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:50,713 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,731 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:50,733 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:50,758 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:27:50,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,776 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:27:50,781 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:50,796 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:27:50,796 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,812 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:27:50,812 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:50,831 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:50,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,854 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:50,862 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:50,881 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:50,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,895 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:50,905 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:50,926 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:50,926 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,941 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:50,947 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:50,963 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:50,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:50,977 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:50,981 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:50,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,012 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:51,038 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,061 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:51,081 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:27:51,081 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,097 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:27:51,102 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:51,137 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,173 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:51,201 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,234 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:51,266 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:51,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,298 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:51,371 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:51,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,454 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:51,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,513 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:51,544 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:51,544 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,565 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:51,574 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:51,597 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,629 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:51,657 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,681 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:51,708 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:27:51,710 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,732 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:27:51,732 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:51,763 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,785 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:51,810 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:51,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,830 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:51,835 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:51,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:27:51,862 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:51,862 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,881 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:51,881 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:51,911 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:27:51,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,929 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:27:51,931 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:51,958 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:27:51,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:51,981 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:27:51,981 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:52,010 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:52,010 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,027 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:52,031 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:52,047 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:52,047 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,065 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:52,076 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:52,096 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:52,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,115 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:27:52,120 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:52,147 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,177 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:52,201 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,227 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:52,247 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:27:52,247 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,264 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:27:52,280 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:52,309 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,344 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:52,370 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,401 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:52,422 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,451 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:52,480 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,512 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:52,533 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:52,533 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,558 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:52,567 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:52,596 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:52,596 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,614 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:52,621 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:52,646 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:27:52,649 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,697 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:27:52,713 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:52,731 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,759 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:52,778 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,801 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:52,819 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:27:52,819 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,838 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:27:52,847 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:52,862 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,892 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:52,912 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:52,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,931 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:52,937 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:52,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:52,981 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:53,009 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,031 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:53,044 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,063 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:53,089 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,113 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:53,131 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:27:53,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,146 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:27:53,151 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:53,163 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,183 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:53,200 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:27:53,201 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,216 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:27:53,223 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:53,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,261 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:53,281 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,301 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:53,316 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,340 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:53,357 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,380 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:53,397 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,418 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:53,437 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:53,437 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,453 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:53,461 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:53,479 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,499 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:53,516 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:27:53,516 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,531 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:27:53,536 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:53,559 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,582 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:53,600 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:27:53,600 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,618 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:27:53,628 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:53,651 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:27:53,652 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,671 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:27:53,681 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:53,702 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:27:53,702 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,719 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:27:53,730 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:53,749 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,776 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:53,797 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:53,797 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,814 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:53,819 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:53,839 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:53,839 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,861 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:53,866 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:53,883 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,905 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:53,927 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:53,928 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,948 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:53,953 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:53,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:53,998 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:54,017 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,038 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:54,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,085 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:54,101 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,121 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:54,145 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:27:54,145 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,161 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:27:54,166 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:54,183 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,203 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:54,223 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,246 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:54,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,282 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:54,300 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:54,300 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,317 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:54,323 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:54,344 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,365 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:54,382 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,412 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:54,431 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,457 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:54,482 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,504 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:54,527 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:54,528 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,545 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:54,549 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:54,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,594 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:54,613 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:54,614 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,629 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:54,635 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:54,653 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:54,654 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,672 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:54,680 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:54,698 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:27:54,698 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,714 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:27:54,719 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:54,737 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,761 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:54,779 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:54,780 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,797 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:54,801 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:54,820 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:54,821 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,843 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:54,848 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:54,867 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,895 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:54,914 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,937 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:54,962 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:54,984 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:55,004 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,032 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:55,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,071 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:55,093 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,114 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:55,133 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,154 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:55,175 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:27:55,176 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,192 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:27:55,197 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:55,214 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:27:55,215 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,229 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:27:55,235 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:55,251 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:55,252 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,279 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:27:55,285 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:55,309 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:27:55,309 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,327 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:27:55,331 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:55,348 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,367 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:55,385 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,404 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:55,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,449 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:55,468 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,487 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:55,515 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,536 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:55,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,581 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:55,601 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:55,601 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,619 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:55,628 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:55,649 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:55,650 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,666 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:27:55,671 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:55,695 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:55,696 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,715 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:55,721 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:55,746 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:27:55,747 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,765 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:27:55,769 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:55,794 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:27:55,795 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,814 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:27:55,819 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:55,838 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:55,839 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,859 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:55,865 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:55,884 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:27:55,884 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,903 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:27:55,911 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:55,931 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:27:55,932 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:55,949 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:27:55,954 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:55,980 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,003 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:56,031 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:56,032 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,051 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:56,063 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:56,087 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,119 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:56,144 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:56,144 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,163 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:56,168 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:56,195 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:56,195 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,216 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:56,223 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:56,245 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:56,245 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,261 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:27:56,265 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:56,283 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:27:56,284 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,310 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:27:56,316 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:56,347 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:27:56,348 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,365 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:27:56,371 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:56,399 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:56,400 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,427 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:56,433 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:56,454 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:27:56,454 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,471 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:27:56,480 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:56,498 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:56,499 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,515 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:27:56,519 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:56,545 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:56,545 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,562 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:56,567 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:56,585 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,611 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:56,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,649 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:27:56,667 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,688 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:27:56,709 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,732 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:27:56,750 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:56,751 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,768 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:27:56,777 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:27:56,796 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:27:56,796 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,813 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:27:56,817 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:27:56,835 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:27:56,836 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,852 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:27:56,859 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:27:56,876 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:27:56,877 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,896 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:27:56,901 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:27:56,919 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:27:56,919 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,937 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:27:56,945 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:27:56,962 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:27:56,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:56,978 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:27:56,983 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:27:57,001 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:57,024 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:27:57,044 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:57,044 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:57,061 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:27:57,065 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:27:57,084 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:57,103 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:27:57,121 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:27:57,146 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:27:58,656 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:27:58,656 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 13)
2025-06-26 17:27:58,656 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:27:58,661 - root - INFO - Date ranges for each asset:
2025-06-26 17:27:58,661 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,661 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,665 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,665 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,666 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,666 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:27:58,666 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:27:58,666 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:27:58,666 - root - INFO -    Execution Method: candle_close
2025-06-26 17:27:58,666 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:27:58,666 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:27:58,674 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,674 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,674 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,674 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,674 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,674 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,675 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,675 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,675 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,675 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,675 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,676 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,676 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,676 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:27:58,676 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:27:58,676 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:27:58,677 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,677 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:27:58,677 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:27:58,678 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,678 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,678 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,678 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,678 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,678 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,678 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,679 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,681 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:27:58,681 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:27:58,681 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:27:58,682 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,682 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:27:58,682 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:27:58,682 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:27:58,683 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,683 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,684 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,684 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,684 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,685 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,686 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,686 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,686 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:27:58,687 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 17:27:58,687 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 17:27:58,687 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 17:27:58,688 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,688 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:27:58,688 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:27:58,688 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 17:27:58,691 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 17:27:58,691 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 17:27:58,692 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 17:27:58,692 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,692 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:27:58,692 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:27:58,692 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 17:27:58,693 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 17:27:58,693 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 17:27:58,693 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 17:27:58,693 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,693 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:27:58,693 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:27:58,694 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 17:27:58,697 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:27:58,697 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:27:58,697 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:27:58,697 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,697 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:27:58,697 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:27:58,697 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:27:58,701 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 17:27:58,701 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 17:27:58,701 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 17:27:58,701 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,701 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:27:58,701 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:27:58,701 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 17:27:58,701 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 17:27:58,701 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 17:27:58,701 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 17:27:58,701 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,701 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:27:58,701 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:27:58,701 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 17:27:58,704 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:27:58,704 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:27:58,704 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:27:58,704 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,704 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:27:58,705 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:27:58,705 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:27:58,706 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:27:58,706 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:27:58,707 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:27:58,707 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,707 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:27:58,707 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:27:58,707 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:27:58,708 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:27:58,709 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:27:58,709 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:27:58,709 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,709 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:27:58,709 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:27:58,709 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:27:58,710 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:27:58,710 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:27:58,711 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:27:58,711 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,711 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:27:58,711 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:27:58,711 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:27:58,711 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:27:58,711 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:27:58,711 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:27:58,711 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,711 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:27:58,711 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:27:58,711 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:27:58,711 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:27:58,711 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:27:58,711 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:27:58,711 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,711 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:27:58,711 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:27:58,711 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:27:58,711 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 17:27:58,711 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 17:27:58,711 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 17:27:58,711 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,711 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:27:58,711 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:27:58,711 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 17:27:58,722 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 17:27:58,723 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 17:27:58,723 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 17:27:58,723 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,723 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:27:58,723 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:27:58,724 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 17:27:58,726 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:27:58,726 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:27:58,726 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:27:58,726 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,726 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:27:58,726 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:27:58,726 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:27:58,731 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:27:58,731 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:27:58,731 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:27:58,731 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,731 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:27:58,731 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:27:58,731 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:27:58,731 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:27:58,731 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:27:58,731 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:27:58,731 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,731 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:27:58,731 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:27:58,731 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:27:58,731 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:27:58,731 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:27:58,731 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:27:58,731 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,731 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:27:58,731 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:27:58,731 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:27:58,739 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:27:58,739 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:27:58,739 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:27:58,739 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,739 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:27:58,739 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:27:58,739 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:27:58,745 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:27:58,745 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:27:58,745 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:27:58,745 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:27:58,745 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:27:58,745 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:27:58,745 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:27:58,778 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:27:58,778 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:27:58,778 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:27:58,778 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:27:58,778 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:27:58,778 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:27:58,778 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:27:58,781 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:27:58,781 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 17:27:58,781 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:27:58,781 - root - INFO - DEBUG: self.elapsed_time = 0.12494683265686035 seconds
2025-06-26 17:27:58,788 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:27:58,788 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:27:58,788 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:27:58,788 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:27:58,789 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:27:58,791 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:27:58,791 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:27:58,791 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:27:58,793 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,794 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,795 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,795 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,798 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,798 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,798 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,801 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,801 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,801 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,801 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,805 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,807 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:27:58,809 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:27:58,809 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:27:58,811 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:27:58,812 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:27:58,812 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:27:58,812 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:27:58,812 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:27:58,812 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:27:58,812 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:27:58,812 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:27:58,812 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:27:58,812 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:27:58,821 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:27:58,821 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:27:58,823 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:27:58,825 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:27:58,827 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:27:58,827 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:27:58,828 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:27:58,828 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:27:58,831 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:27:58,831 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:27:58,831 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:27:58,831 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:27:58,831 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:27:58,831 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:27:58,831 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:27:58,844 - root - INFO - Configuration loaded successfully.
2025-06-26 17:27:58,853 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:27:58,948 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:27:58,953 - root - INFO - Configuration loaded successfully.
2025-06-26 17:28:00,277 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,277 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,277 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,279 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,281 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,281 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:28:00,281 - root - INFO - Added 13 buy-and-hold curves to results
2025-06-26 17:28:00,281 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,281 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,281 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,282 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,282 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,282 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,282 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,282 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,283 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,283 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,283 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,283 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,283 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:28:00,296 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:28:00,296 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:28:00,296 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:28:00,296 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:28:00,301 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:28:00,301 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:28:00,331 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:28:00,337 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_172742.csv
2025-06-26 17:28:00,337 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_172742.csv
2025-06-26 17:28:00,337 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:28:00,337 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:28:00,337 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:28:00,337 - root - INFO - Success flag set to: True
2025-06-26 17:28:00,337 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:28:00,338 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:28:00,338 - root - INFO -   - buy_hold_curves: dict with 13 entries
2025-06-26 17:28:00,338 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:28:00,339 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:28:00,339 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:28:00,340 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:28:00,340 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:28:00,340 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:28:00,340 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:28:00,341 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:28:00,341 - root - INFO -   - message: <class 'str'>
2025-06-26 17:28:00,341 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:28:00,342 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 17:28:00,342 - root - ERROR - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 17:28:00,343 - root - ERROR - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 17:28:00,343 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 17:28:00,360 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR',
       'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR',
       'DOT/EUR'],
      dtype='object')
2025-06-26 17:28:00,361 - root - INFO - Last row columns: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:28:00,361 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG]   - MTPI signal: 1
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG]   - Use MTPI signal: False
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] ERROR - TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 17:28:00,361 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:28:00,361 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:28:00,361 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:28:00,361 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:28:00,361 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:28:00,361 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 17:28:00,361 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 17:28:00,361 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 17:28:00,721 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 17:28:00,721 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-26 17:28:00,758 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-26 17:28:00,758 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1750951677607, 'datetime': '2025-06-26T15:27:57.607Z', 'high': 0.23507, 'low': 0.23076, 'bid': 0.23211, 'bidVolume': 13290.0, 'ask': 0.23228, 'askVolume': 3536.511671, 'vwap': 0.2328232644837453, 'open': 0.23324, 'close': 0.23231, 'last': 0.23231, 'previousClose': None, 'change': -0.00093, 'percentage': -0.3987309209398044, 'average': 0.232775, 'baseVolume': 1386416.845911, 'quoteVolume': 322790.0960002567, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1750865277607, 'timestamp': 1750951677607, 'open': '0.23324', 'openTimestamp': 1750865363464, 'high': '0.23507', 'low': '0.23076', 'last': '0.23231', 'closeTimestamp': 1750951218751, 'bid': '0.2321100', 'bidSize': '13290', 'ask': '0.2322800', 'askSize': '3536.511671', 'volume': '1386416.845911', 'volumeQuote': '322790.0960002567'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 17:28:00,758 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23231
2025-06-26 17:28:00,758 - root - ERROR - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23231
2025-06-26 17:28:00,761 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 17:28:00,761 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 17:28:00,761 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 17:28:00,761 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:28:00,761 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:28:00,761 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:28:00,761 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 17:28:00,761 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 17:28:00,761 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:28:00,761 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:28:00,761 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:28:00,761 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:28:00,761 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.**************, price: 0.23231
2025-06-26 17:28:00,761 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:28:00,761 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:28:00,761 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:28:00,774 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:28:00,774 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:28:00,774 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:28:00,781 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:28:00,781 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:28:00,781 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:28:00,781 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.830700357281216, 'price': 0.23231, 'order': {'id': 'paper-1750951680-TRX/EUR-buy-42.**************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.**************, 'price': 0.23231, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.**************, 'remaining': 0, 'timestamp': 1750951680761, 'datetime': '2025-06-26T17:28:00.761352', 'trades': [], 'average': 0.23231, 'average_price': 0.23231}, 'filled_amount': 42.**************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:28:00.761352'}
2025-06-26 17:28:00,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:28:00,866 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:28:00,866 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:28:00,866 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:28:00,866 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,866 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,866 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,867 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,867 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,867 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,867 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,868 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,868 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,868 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,868 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,868 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:28:00,869 - root - INFO - Asset selection logged with 13 assets scored and 1 assets selected
2025-06-26 17:28:00,869 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:28:00,869 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:28:00,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:28:00,912 - root - INFO - Strategy execution completed successfully in 18.65 seconds
2025-06-26 17:28:00,916 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:28:01,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:28:11,905 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:28:21,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:28:31,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:28:41,957 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:28:50,016 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:28:51,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:29:00,024 - root - INFO - Network watchdog stopped
2025-06-26 17:29:00,025 - root - INFO - Network watchdog stopped
2025-06-26 17:29:00,026 - root - INFO - Background service stopped
2025-06-26 17:29:00,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
