# 📘 Momentum-Based Asset Selection Guide

This document outlines the logic and edge cases for selecting the best asset based on momentum-driven score changes, evaluated at the close of each daily candle.

---

## 🧠 Strategy Context

* Strategy runs once daily at candle close.
* Each asset is scored based on a dynamic indicator.
* Only **one asset is selected per day** ("best asset").
* **Momentum** is interpreted as the change in score from T₁ (previous day) to T₂ (current day).
* Score ties and directionality of score changes influence the selection.

---

## ✅ Core Selection Logic

1. If one asset has the **highest score**, it is selected.
2. If multiple assets are tied, compare their **momentum (score delta)**.
3. If still tied, apply a **tie-breaker** (e.g., retain incumbent, alphabetical order, etc.).

---

## 📊 Example 1 – Clear Momentum Winner

### T₁ Snapshot:

| Rank | Asset    | Score |
| ---- | -------- | ----- |
| ✅ 1  | TRX/USDT | 13    |
| 2    | BTC/USDT | 12    |
| 3    | BNB/USDT | 11    |

### T₂ Snapshot:

| Rank | Asset    | Score |
| ---- | -------- | ----- |
| ✅ 1  | BTC/USDC | 13    |
| 2    | TRX/USDC | 12    |
| 3    | BNB/USDC | 11    |

### Evaluation:

| Asset | T₁ Score | T₂ Score | Δ Score | Momentum        |
| ----- | -------- | -------- | ------- | --------------- |
| TRX   | 13       | 12       | –1      | 🔻 Weakening    |
| BTC   | 12       | 13       | +1      | ✅ Strengthening |

**➡️ Decision: BTC selected due to positive momentum**

---

## 📊 Example 2 – Tie Break Edge Case

### T₁ Snapshot:

| Rank | Asset    | Score |
| ---- | -------- | ----- |
| ✅ 1  | TRX/USDT | 13    |
| 2    | BTC/USDT | 12    |

### T₂ Snapshot:

| Rank | Asset    | Score |
| ---- | -------- | ----- |
| ✅1   | BTC/USDC | 12    |
| 2    | TRX/USDC | 12    |

### Evaluation:

| Asset | T₁ Score | T₂ Score | Δ Score | Momentum     |
| ----- | -------- | -------- | ------- | ------------ |
| TRX   | 13       | 12       | –1      | 🔻 Weakening |
| BTC   | 12       | 12       | 0       | ↔ Stable     |

**➡️ Decision: BTC selected due to stronger relative momentum**

---

## ⚠️ Edge Cases to Consider

### 1. Tie in Top Scores

> Multiple assets have equal highest scores at T₂.

* Resolve using momentum (Δscore).
* If still tied, apply deterministic tie-breaker.

### 2. New Asset Jumps to the Top

> Previously low asset suddenly becomes best.

* Flag as breakout. Select if score is highest and no better incumbent momentum exists.

### 3. Previous Winner Retains Score but Loses Rank

> Absolute score unchanged, but outperformed.

* Shows relative weakness.
* May switch to asset with stronger positive momentum.

---

## 📌 Summary

* Focus on **score changes** (momentum) over time, not just absolute values.
* Momentum logic helps avoid holding declining leaders.
* Decision rules should be **deterministic, explainable, and testable**.

---

Let us know if you'd like more detailed charts, visual decision trees, or additional daily snapshots.
