#!/usr/bin/env python3
"""
Test script to verify that the current system implements the conservative incumbent approach.
This means when assets have tied scores, the system should favor the previously selected asset.
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_incumbent_approach():
    """Test if the system preserves the incumbent when scores are tied."""
    
    print("=" * 80)
    print("TESTING INCUMBENT APPROACH VERIFICATION")
    print("=" * 80)
    
    # Simulate a best_asset_series where TRX was the incumbent
    dates = pd.date_range(start='2025-06-21', end='2025-06-25', freq='D')
    best_asset_series = pd.Series(['TRX/EUR'] * len(dates), index=dates)
    
    print("1. SIMULATED HISTORICAL SERIES (TRX as incumbent):")
    print("-" * 50)
    print(best_asset_series)
    
    # Current day scores with BTC and TRX tied
    current_scores = {
        'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 
        'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 
        'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 
        'BNB/EUR': 11.0, 'DOT/EUR': 1.0
    }
    
    print(f"\n2. CURRENT DAY SCORES (BTC and TRX tied at 12.0):")
    print("-" * 50)
    max_score = max(current_scores.values())
    tied_assets = [asset for asset, score in current_scores.items() if score == max_score]
    
    for asset, score in current_scores.items():
        marker = ""
        if score == max_score:
            marker = " ← TIED MAX"
        print(f"  {asset}: {score}{marker}")
    
    print(f"\nTied assets: {tied_assets}")
    print(f"Maximum score: {max_score}")
    
    # Test the current system's behavior
    print(f"\n3. CURRENT SYSTEM BEHAVIOR:")
    print("-" * 50)
    
    # This simulates what background_service.py does:
    # It extracts the last entry from best_asset_series
    incumbent_from_series = best_asset_series.iloc[-1]
    print(f"Incumbent from series: {incumbent_from_series}")
    
    # Check if incumbent is among the tied assets
    if incumbent_from_series in tied_assets:
        selected_asset = incumbent_from_series
        approach = "INCUMBENT APPROACH"
        print(f"✅ Incumbent {incumbent_from_series} is among tied assets")
        print(f"✅ System selects incumbent: {selected_asset}")
    else:
        # This would be the tie-breaking approach
        from src.scoring import find_best_asset_for_day
        selected_asset = find_best_asset_for_day(current_scores)
        approach = "TIE-BREAKING APPROACH"
        print(f"❌ Incumbent {incumbent_from_series} not among tied assets")
        print(f"❌ System uses tie-breaking: {selected_asset}")
    
    print(f"\n4. VERIFICATION RESULT:")
    print("-" * 50)
    print(f"Approach detected: {approach}")
    print(f"Selected asset: {selected_asset}")
    
    # Test what would happen with momentum approach
    print(f"\n5. COMPARISON WITH MOMENTUM APPROACH:")
    print("-" * 50)
    
    # In momentum approach, we'd favor the asset that just reached the top score
    # We'd need to know which asset "just caught up" - this would require historical score tracking
    # For now, let's assume BTC is the "follower" that just caught up
    
    print("Momentum approach would:")
    print("- Track which asset recently improved to reach the tied score")
    print("- Favor the 'follower' that just caught up to the leader")
    print("- In this case, might favor BTC if it just reached 12.0")
    
    # Test edge cases
    print(f"\n6. EDGE CASE TESTING:")
    print("-" * 50)
    
    # Case 1: Incumbent not in current tied assets
    edge_scores = current_scores.copy()
    edge_scores['TRX/EUR'] = 11.0  # TRX drops below tie
    edge_scores['BTC/EUR'] = 13.0  # BTC becomes clear winner
    
    print("Edge case: Incumbent (TRX) drops below tie, BTC becomes clear winner")
    max_edge_score = max(edge_scores.values())
    edge_tied = [asset for asset, score in edge_scores.items() if score == max_edge_score]
    print(f"New max score: {max_edge_score}")
    print(f"Assets at max: {edge_tied}")
    
    if incumbent_from_series not in edge_tied:
        print("✅ Incumbent approach would correctly switch to new leader")
        print(f"✅ Would select: {edge_tied[0]}")
    
    print(f"\n" + "=" * 80)

if __name__ == "__main__":
    test_incumbent_approach()
