2025-06-26 18:35:59,080 - root - INFO - Loaded environment variables from .env file
2025-06-26 18:35:59,845 - root - INFO - Loaded 57 trade records from logs/trades\trade_log_********.json
2025-06-26 18:35:59,846 - root - INFO - Loaded 33 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 18:35:59,846 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 18:36:00,998 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:01,007 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:01,007 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:01,016 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:01,016 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:01,025 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:01,026 - root - INFO - Loading notification configuration from config/notifications_kraken.json...
2025-06-26 18:36:01,026 - root - INFO - Notification configuration loaded successfully.
2025-06-26 18:36:02,163 - root - INFO - Telegram command handlers registered
2025-06-26 18:36:02,164 - root - INFO - Telegram bot polling started
2025-06-26 18:36:02,165 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 18:36:02,166 - root - INFO - Telegram notification channel initialized
2025-06-26 18:36:02,168 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 18:36:02,172 - root - INFO - Loaded 24 templates from file
2025-06-26 18:36:02,172 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 18:36:02,178 - root - INFO - Notification manager initialized
2025-06-26 18:36:02,179 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 18:36:02,179 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 18:36:02,179 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 18:36:02,180 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 18:36:02,182 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 18:36:02,183 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 18:36:02,184 - root - INFO - Recovery manager initialized
2025-06-26 18:36:02,184 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: kraken
2025-06-26 18:36:02,186 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'kraken', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'AAVE/EUR': 5.0, 'ADA/EUR': 2.0, 'AVAX/EUR': 4.0, 'BNB/EUR': 3.0, 'BTC/EUR': 3.5, 'DOGE/EUR': 5.0, 'DOT/EUR': 4.5, 'ETH/EUR': 7.5, 'LINK/EUR': 3.5, 'PEPE/EUR': 5.0, 'SOL/EUR': 4.5, 'SUI/EUR': 2.0, 'TRX/EUR': 3.0, 'XRP/EUR': 5.0, 'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.004}
2025-06-26 18:36:02,186 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:02,196 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:02,196 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:36:02,196 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:36:02,196 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:36:02,197 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:36:02,197 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:36:02,197 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:36:02,197 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:36:02,197 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:36:02,197 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:02,207 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:02,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getMe "HTTP/1.1 200 OK"
2025-06-26 18:36:02,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 18:36:02,261 - telegram.ext.Application - INFO - Application started
2025-06-26 18:36:03,437 - root - INFO - Successfully loaded markets for kraken.
2025-06-26 18:36:03,437 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:36:03,438 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:36:03,438 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:36:03,438 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:36:03,438 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:03,446 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:03,451 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:03,460 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:03,461 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:36:03,468 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:03,470 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 18:36:03,470 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 18:36:03,470 - root - INFO - Trading executor initialized for kraken
2025-06-26 18:36:03,470 - root - INFO - Trading mode: paper
2025-06-26 18:36:03,470 - root - INFO - Trading enabled: True
2025-06-26 18:36:03,470 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:36:03,470 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:36:03,471 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:36:03,471 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:36:03,471 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:03,480 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:04,704 - root - INFO - Successfully loaded markets for kraken.
2025-06-26 18:36:04,704 - root - INFO - Trading enabled in paper mode
2025-06-26 18:36:04,706 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:36:04,706 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 18:36:04,706 - root - INFO - Reset paper trading account to initial balance
2025-06-26 18:36:04,706 - root - INFO - Generated run ID: ********_183604
2025-06-26 18:36:04,706 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 18:36:04,707 - root - INFO - Background service initialized
2025-06-26 18:36:04,707 - root - INFO - Network watchdog started
2025-06-26 18:36:04,707 - root - INFO - Network watchdog started
2025-06-26 18:36:04,709 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 18:36:04,709 - root - INFO - Background service started
2025-06-26 18:36:04,709 - root - INFO - Executing strategy (run #1)...
2025-06-26 18:36:04,710 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 18:36:04,710 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 18:36:04,711 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 18:36:04,711 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 18:36:04,795 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:36:10,735 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 18:36:10,812 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:36:10,813 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 18:36:10,814 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 18:36:10,814 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 18:36:10,817 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 18:36:10,844 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,845 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,845 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,845 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 18:36:10,846 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,861 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,862 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,862 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,862 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 18:36:10,863 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,875 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,875 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,876 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,876 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 18:36:10,877 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,885 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,885 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,886 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,886 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 18:36:10,887 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,901 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,902 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,903 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,903 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 18:36:10,903 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,916 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,916 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,917 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,917 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 18:36:10,919 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,932 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,932 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,933 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,933 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 18:36:10,934 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,948 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,949 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,949 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,950 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 18:36:10,952 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,965 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,966 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,967 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,967 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 18:36:10,968 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,981 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,981 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,981 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,982 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 18:36:10,982 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:10,990 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:10,990 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,991 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:10,991 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 18:36:10,992 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,003 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,004 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,004 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,004 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 18:36:11,005 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,016 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,016 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,018 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,018 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 18:36:11,019 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,030 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,030 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,030 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,030 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 18:36:11,031 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,034 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 18:36:11,035 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 18:36:11,035 - root - INFO -   - Number of indicators: 8
2025-06-26 18:36:11,035 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:36:11,036 - root - INFO -   - Combination method: consensus
2025-06-26 18:36:11,036 - root - INFO -   - Long threshold: 0.1
2025-06-26 18:36:11,036 - root - INFO -   - Short threshold: -0.1
2025-06-26 18:36:11,036 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:36:11,036 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:36:11,036 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 18:36:11,036 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-26 18:36:11,036 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 18:36:11,036 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 18:36:11,037 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 18:36:11,037 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 18:36:11,037 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 18:36:11,037 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 18:36:11,037 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 18:36:11,037 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:11,044 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:11,045 - root - INFO - Saving configuration to config/settings_kraken_eur.yaml...
2025-06-26 18:36:11,050 - root - INFO - Configuration saved successfully.
2025-06-26 18:36:11,050 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:36:11,050 - root - INFO - Number of trend detection assets: 14
2025-06-26 18:36:11,050 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 18:36:11,050 - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:36:11,051 - root - INFO - Number of trading assets: 14
2025-06-26 18:36:11,051 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 18:36:11,252 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:36:11,260 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:11,267 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:36:11,276 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:11,276 - root - INFO - Execution context: backtesting
2025-06-26 18:36:11,276 - root - INFO - Execution timing: candle_close
2025-06-26 18:36:11,276 - root - INFO - Ratio calculation method: independent
2025-06-26 18:36:11,276 - root - INFO - Tie-breaking strategy: momentum
2025-06-26 18:36:11,277 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 18:36:11,277 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:36:11,277 - root - INFO - MTPI combination method override: consensus
2025-06-26 18:36:11,277 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 18:36:11,277 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 18:36:11,278 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 18:36:11,278 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:36:11,279 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,279 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,279 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,279 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,280 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,280 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,280 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,280 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,281 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,281 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,281 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,282 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,282 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,282 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:36:11,282 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 18:36:11,297 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,298 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,298 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,299 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 18:36:11,313 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,314 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,315 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,315 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:36:11,327 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,328 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,328 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,329 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 18:36:11,337 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,338 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,339 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,339 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 18:36:11,351 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,353 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,354 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,354 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 18:36:11,366 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,367 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,368 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,368 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 18:36:11,380 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,380 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,382 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,382 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 18:36:11,397 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,399 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,400 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,400 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 18:36:11,415 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,416 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,419 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,419 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 18:36:11,432 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,433 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,434 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,434 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 18:36:11,442 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,443 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,443 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,444 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 18:36:11,456 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,458 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,458 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,458 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 18:36:11,472 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,473 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,473 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,473 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 18:36:11,483 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,486 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:36:11,486 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,487 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 18:36:11,487 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:36:11,488 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,488 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,488 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,488 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,489 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,489 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,489 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,489 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,489 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,489 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,490 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,490 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,490 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,490 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:36:11,502 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 18:36:11,503 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:36:11,503 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 18:36:11,503 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:36:11,504 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:36:11,510 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:11,511 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 18:36:11,511 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:36:11,512 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:36:11,512 - root - INFO - Override: combination_method = consensus
2025-06-26 18:36:11,512 - root - INFO - Override: long_threshold = 0.1
2025-06-26 18:36:11,512 - root - INFO - Override: short_threshold = -0.1
2025-06-26 18:36:11,512 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 18:36:11,512 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 18:36:11,512 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 18:36:11,513 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 18:36:11,526 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:36:11,526 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:36:11,527 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:36:11,527 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:36:11,527 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:11,528 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 18:36:11,550 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 18:36:11,553 - root - INFO - Generated pgo signals: 256 values
2025-06-26 18:36:11,553 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 18:36:11,553 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 18:36:11,564 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 18:36:11,565 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 18:36:11,565 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 18:36:11,916 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 18:36:11,916 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 18:36:11,972 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 18:36:11,972 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 18:36:11,972 - root - INFO - Generated DEMA Super Score signals
2025-06-26 18:36:11,972 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 18:36:12,050 - root - INFO - Generated DPSD signals
2025-06-26 18:36:12,050 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 18:36:12,051 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 18:36:12,051 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 18:36:12,061 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 18:36:12,061 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 18:36:12,061 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 18:36:12,111 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 18:36:12,112 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 18:36:12,112 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 18:36:12,207 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 18:36:12,213 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 18:36:12,213 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:36:12,213 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 18:36:12,213 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:36:12,213 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 18:36:12,218 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 18:36:12,224 - root - INFO - Configuration saved successfully.
2025-06-26 18:36:12,224 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 18:36:12,224 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:36:12,234 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:12,234 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 18:36:12,234 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 18:36:12,234 - root - INFO - Using ratio calculation method: independent
2025-06-26 18:36:12,258 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,290 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:12,311 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:12,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:36:12,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,339 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:12,342 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:12,360 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:36:12,360 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,380 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:36:12,385 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:12,441 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:12,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,464 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:12,465 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:12,490 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:12,490 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,523 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:12,532 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:12,557 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:36:12,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,583 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:36:12,590 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:12,606 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:12,610 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,623 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:12,623 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:12,640 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:36:12,640 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,660 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:36:12,670 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:12,682 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:36:12,682 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,704 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:36:12,710 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:12,730 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:12,730 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,750 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:12,761 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:12,780 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:12,780 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,800 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:12,806 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:12,830 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,861 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:12,880 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:36:12,880 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:12,917 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:36:12,923 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:12,967 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:12,967 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,006 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:13,013 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:13,034 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,062 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:13,073 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:13,073 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,091 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:13,100 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:13,121 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:13,121 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,133 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:13,140 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:13,156 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:13,156 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,173 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:13,180 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:13,195 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:36:13,199 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,217 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:36:13,223 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:13,240 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:13,240 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,260 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:13,266 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:13,284 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:13,284 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,306 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:13,311 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:13,323 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:13,330 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,346 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:13,357 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:13,373 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:13,373 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,390 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:13,400 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:13,413 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,440 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:13,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,480 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:13,500 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:36:13,500 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,514 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:36:13,523 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:13,540 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:36:13,540 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,556 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:36:13,561 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:13,573 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,596 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:13,612 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:13,612 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,640 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:13,640 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:13,663 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:13,663 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,683 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:13,693 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:13,711 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:36:13,711 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,730 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:36:13,740 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:13,764 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:36:13,764 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,783 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:36:13,790 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:13,806 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:13,806 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,823 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:13,825 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:13,849 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:13,850 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,871 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:13,873 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:13,895 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:36:13,895 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,910 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:36:13,914 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:13,932 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:36:13,932 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,951 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:36:13,960 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:13,973 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:13,973 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:13,990 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:14,006 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:14,030 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:14,030 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,060 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:14,069 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:14,090 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:14,090 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,126 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:14,141 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:14,180 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:14,180 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,200 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:14,206 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:14,227 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,249 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:14,269 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,290 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:14,310 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,330 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:14,350 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:36:14,350 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,364 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:36:14,373 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:14,392 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,411 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:14,428 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,450 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:14,472 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:14,473 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,491 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:14,495 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:14,514 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,530 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:14,559 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,572 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:14,594 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:14,595 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,606 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:14,613 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:14,632 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,655 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:14,680 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,705 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:14,715 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:36:14,715 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,732 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:36:14,740 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:14,760 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,782 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:14,800 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,829 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:14,845 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:14,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,863 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:14,870 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:14,889 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:14,890 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,910 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:14,915 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:14,939 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:36:14,939 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,956 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:36:14,961 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:14,972 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:36:14,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:14,994 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:36:14,998 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:15,015 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:15,015 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,032 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:15,039 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:15,055 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:15,055 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,078 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:15,085 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:15,100 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:15,105 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,124 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:36:15,131 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:15,157 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,178 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:15,205 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,232 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:15,255 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:36:15,255 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,289 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:36:15,300 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:15,323 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,350 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:15,370 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,394 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:15,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,432 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:15,461 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,482 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:15,505 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,527 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:15,550 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:15,550 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,565 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:15,572 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:15,590 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:15,590 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,610 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:15,615 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:15,632 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:15,632 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,655 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:15,664 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:15,682 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,694 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:15,720 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,740 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:15,760 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:36:15,760 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,772 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:36:15,782 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:15,805 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,823 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:15,845 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:15,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,861 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:15,866 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:15,882 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,910 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:15,930 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,955 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:15,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:15,990 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:16,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,039 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:16,055 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,077 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:16,093 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:36:16,093 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,110 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:36:16,115 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:16,134 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,160 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:16,175 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:36:16,175 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,190 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:36:16,190 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:16,215 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,240 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:16,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,284 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:16,304 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,323 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:16,355 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,389 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:16,415 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,460 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:16,495 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:16,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,532 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:16,540 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:16,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,620 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:16,640 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,665 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:16,686 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:36:16,686 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,700 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:36:16,714 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:16,732 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,755 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:16,772 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:36:16,772 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,788 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:36:16,790 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:16,810 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:16,810 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,832 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:16,840 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:16,861 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:36:16,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,872 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:36:16,882 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:16,900 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,928 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:16,940 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:16,940 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:16,960 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:16,965 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:16,982 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:16,982 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,000 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:17,007 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:17,022 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,048 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:17,065 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:17,065 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,082 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:17,091 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:17,110 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,131 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:17,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,172 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:17,195 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,225 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:17,240 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,265 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:17,294 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,310 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:17,332 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:36:17,332 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,348 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:36:17,350 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:17,372 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,390 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:17,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,434 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:17,455 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,472 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:17,503 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:17,503 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,539 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:17,547 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:17,591 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,638 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:17,673 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,700 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:17,716 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,740 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:17,760 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,780 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:17,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,820 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:17,840 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:17,845 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,860 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:17,867 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:17,884 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,907 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:17,923 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:17,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,941 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:17,949 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:17,967 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:17,970 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:17,982 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:17,991 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:18,014 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:18,014 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,030 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:18,039 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:18,057 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,080 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:18,095 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:18,099 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,117 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:18,124 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:18,140 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:18,140 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,156 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:18,161 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:18,180 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,200 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:18,227 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,240 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:18,262 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,283 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:18,302 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,327 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:18,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,363 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:18,384 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,406 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:18,423 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,447 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:18,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,553 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:18,593 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:36:18,593 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,627 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:36:18,637 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:18,669 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:18,670 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,716 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:36:18,746 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:18,777 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:18,777 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,794 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:36:18,800 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:18,820 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:36:18,820 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,844 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:36:18,858 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:18,883 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:18,924 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:18,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,045 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:19,081 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,122 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:19,145 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,170 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:19,188 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,208 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:19,228 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,248 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:19,267 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,293 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:19,308 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:19,310 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,327 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:19,340 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:19,356 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:19,356 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,369 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:36:19,376 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:19,391 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:19,391 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,413 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:19,422 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:19,441 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:36:19,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,455 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:36:19,463 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:19,485 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:36:19,485 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,509 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:36:19,520 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:19,547 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:19,547 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,572 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:19,578 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:19,600 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:36:19,601 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,625 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:36:19,631 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:19,648 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:36:19,648 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,664 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:36:19,669 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:19,704 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,736 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:19,761 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:19,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,776 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:19,782 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:19,803 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,831 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:19,856 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,880 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:19,905 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:19,905 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,928 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:19,935 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:19,959 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:19,959 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:19,980 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:19,991 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:20,010 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:20,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,034 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:36:20,044 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:20,071 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:36:20,071 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,092 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:36:20,097 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:20,119 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:36:20,120 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,143 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:36:20,155 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:20,175 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:20,176 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,197 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:20,204 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:20,232 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:36:20,232 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,248 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:36:20,255 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:20,281 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:20,282 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,305 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:36:20,310 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:20,338 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:20,338 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,357 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:20,366 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:20,395 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,417 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:20,437 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,459 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:36:20,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,502 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:36:20,517 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,542 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:36:20,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,620 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:36:20,643 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:20,644 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,657 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:36:20,666 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:36:20,682 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:20,683 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,697 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:36:20,704 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:36:20,718 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:20,719 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,736 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:36:20,741 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:36:20,755 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:36:20,756 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,769 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:36:20,780 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:36:20,798 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:36:20,798 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,813 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:36:20,819 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:36:20,846 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:36:20,847 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,872 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:36:20,878 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:36:20,897 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,918 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:36:20,946 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:20,947 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:20,970 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:36:20,984 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:36:21,006 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:21,028 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:36:21,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:36:21,076 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:36:22,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:36:23,748 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 18:36:23,748 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 18:36:23,748 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 18:36:23,750 - root - INFO - Date ranges for each asset:
2025-06-26 18:36:23,750 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,750 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,755 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,755 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,755 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,755 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,755 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,756 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,756 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,756 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,756 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,757 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,757 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,757 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,757 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:36:23,758 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 18:36:23,762 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 18:36:23,763 - root - INFO -    Execution Method: candle_close
2025-06-26 18:36:23,763 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 18:36:23,763 - root - INFO -    Signal generated and executed immediately
2025-06-26 18:36:23,768 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,768 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,768 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,770 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,770 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,770 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,770 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,771 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,771 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,771 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,772 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,772 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,772 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,772 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,772 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 18:36:23,773 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 18:36:23,773 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 18:36:23,773 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,773 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:36:23,773 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 18:36:23,775 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,775 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,775 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,775 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,775 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,776 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,776 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,776 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,776 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,776 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,777 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,777 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,777 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,777 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,778 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,778 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,778 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,779 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,780 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,780 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,780 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 18:36:23,780 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 18:36:23,780 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 18:36:23,781 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,781 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:36:23,781 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:36:23,781 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 18:36:23,782 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,783 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,783 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,783 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,783 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,783 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,784 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,784 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,784 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,784 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,785 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,785 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,785 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,785 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,786 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,787 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,787 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,787 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,787 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,788 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:36:23,793 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 18:36:23,793 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 18:36:23,793 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 18:36:23,793 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,793 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:36:23,794 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:36:23,794 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 18:36:23,797 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 18:36:23,797 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 18:36:23,797 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 18:36:23,797 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,798 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:36:23,798 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:36:23,798 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 18:36:23,799 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 18:36:23,799 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 18:36:23,799 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 18:36:23,799 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,799 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:36:23,800 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:36:23,800 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 18:36:23,804 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 18:36:23,804 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 18:36:23,805 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 18:36:23,805 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,806 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:36:23,806 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:36:23,806 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 18:36:23,809 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 18:36:23,809 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 18:36:23,809 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 18:36:23,809 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,809 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:36:23,809 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:36:23,810 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 18:36:23,811 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 18:36:23,812 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 18:36:23,812 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 18:36:23,812 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,812 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:36:23,813 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:36:23,813 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 18:36:23,814 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 18:36:23,814 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 18:36:23,814 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 18:36:23,814 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,814 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:36:23,815 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:36:23,815 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 18:36:23,816 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 18:36:23,817 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 18:36:23,817 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 18:36:23,817 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,818 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:36:23,818 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 18:36:23,818 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 18:36:23,819 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 18:36:23,820 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 18:36:23,820 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 18:36:23,821 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,821 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 18:36:23,821 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:36:23,821 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 18:36:23,823 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 18:36:23,824 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 18:36:23,824 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 18:36:23,824 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,824 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:36:23,824 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 18:36:23,825 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 18:36:23,825 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 18:36:23,825 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 18:36:23,825 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 18:36:23,825 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,826 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 18:36:23,826 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:36:23,826 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:36:23,827 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 18:36:23,827 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 18:36:23,827 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 18:36:23,827 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,827 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:36:23,827 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:36:23,828 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 18:36:23,828 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 18:36:23,828 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 18:36:23,828 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 18:36:23,828 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,828 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:36:23,828 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:36:23,828 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 18:36:23,835 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 18:36:23,835 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 18:36:23,835 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 18:36:23,835 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,835 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:36:23,835 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 18:36:23,835 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 18:36:23,838 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 18:36:23,838 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 18:36:23,838 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 18:36:23,838 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,838 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 18:36:23,838 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 18:36:23,838 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 18:36:23,843 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 18:36:23,843 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 18:36:23,843 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 18:36:23,844 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,844 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 18:36:23,844 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:36:23,844 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:36:23,847 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 18:36:23,848 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 18:36:23,848 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 18:36:23,848 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,848 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:36:23,848 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:36:23,849 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 18:36:23,850 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 18:36:23,850 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 18:36:23,850 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 18:36:23,850 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,850 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:36:23,851 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:36:23,851 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:36:23,852 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 18:36:23,852 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 18:36:23,852 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 18:36:23,852 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,852 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:36:23,853 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:36:23,853 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 18:36:23,861 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 18:36:23,861 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 18:36:23,861 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 18:36:23,861 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:36:23,862 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:36:23,862 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:36:23,862 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 18:36:23,894 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 18:36:23,894 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:36:23,894 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:36:23,895 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:36:23,895 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:36:23,895 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:36:23,895 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 18:36:23,895 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:36:23,896 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 18:36:23,896 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 18:36:23,896 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 18:36:23,897 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:36:23,898 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 18:36:23,898 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 18:36:23,898 - root - INFO - Strategy execution completed in 0s
2025-06-26 18:36:23,899 - root - INFO - DEBUG: self.elapsed_time = 0.14969134330749512 seconds
2025-06-26 18:36:23,901 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 18:36:23,901 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 18:36:23,902 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 18:36:23,902 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 18:36:23,903 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 18:36:23,903 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 18:36:23,903 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 18:36:23,904 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,905 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,906 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,907 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,908 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,908 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,910 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,911 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,912 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,912 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,913 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,914 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,915 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,916 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:36:23,917 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 18:36:23,918 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 18:36:23,919 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 18:36:23,919 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 18:36:23,921 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 18:36:23,921 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 18:36:23,922 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 18:36:23,923 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 18:36:23,923 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 18:36:23,924 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 18:36:23,925 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 18:36:23,925 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 18:36:23,926 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 18:36:23,926 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 18:36:23,928 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 18:36:23,928 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 18:36:23,929 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 18:36:23,929 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 18:36:23,931 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 18:36:23,931 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 18:36:23,932 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 18:36:23,932 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 18:36:23,934 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 18:36:23,934 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 18:36:23,935 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 18:36:23,936 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 18:36:23,938 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 18:36:23,938 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 18:36:23,939 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:36:23,945 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:23,956 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 18:36:24,115 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:36:24,120 - root - INFO - Configuration loaded successfully.
2025-06-26 18:36:25,280 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 18:36:25,280 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 18:36:25,280 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,280 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,289 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,289 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,289 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,289 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:36:25,299 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 18:36:25,299 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:36:25,301 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 18:36:25,302 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 18:36:25,303 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 18:36:25,304 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:36:25,304 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:36:25,304 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:36:25,304 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:36:25,304 - root - ERROR - [DEBUG] CONVERSION - TIED USDT ASSETS: ['BTC/USDT', 'TRX/USDT'] (score: 12.0)
2025-06-26 18:36:25,305 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 18:36:25,305 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 12.0)
2025-06-26 18:36:25,305 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 18:36:25,305 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 18:36:25,305 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 18:36:25,306 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 18:36:25,306 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 18:36:25,306 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 18:36:25,306 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 18:36:25,306 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - TIED EUR ASSETS: ['BTC/EUR', 'TRX/EUR'] (score: 12.0)
2025-06-26 18:36:25,307 - root - ERROR - [DEBUG] CONVERSION - First EUR asset (should be selected): BTC/EUR
2025-06-26 18:36:25,308 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:36:25,313 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_183604.csv
2025-06-26 18:36:25,313 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_183604.csv
2025-06-26 18:36:25,313 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 18:36:25,313 - root - INFO - Results type: <class 'dict'>
2025-06-26 18:36:25,313 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 18:36:25,313 - root - INFO - Success flag set to: True
2025-06-26 18:36:25,313 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 18:36:25,314 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:36:25,314 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 18:36:25,314 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:36:25,314 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 18:36:25,314 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 18:36:25,314 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 18:36:25,314 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 18:36:25,314 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 18:36:25,314 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 18:36:25,314 - root - INFO -   - success: <class 'bool'>
2025-06-26 18:36:25,315 - root - INFO -   - message: <class 'str'>
2025-06-26 18:36:25,315 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 18:36:25,315 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 18:36:25,315 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 18:36:25,315 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:36:25,316 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: incumbent
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 12.0
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] TIE DETECTED - 2 assets tied at score 12.0
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] TIE DETECTED - Tied assets: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] TIE DETECTED - Current best_asset from series: TRX/EUR
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] INCUMBENT APPROACH - Keeping incumbent TRX/EUR
2025-06-26 18:36:25,316 - root - ERROR - [DEBUG] TIE-BREAKING CONFIRMED SELECTION: TRX/EUR
2025-06-26 18:36:25,363 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 18:36:25,363 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:36:25,364 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 18:36:25,364 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 18:36:25,364 - root - INFO - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 18:36:25,364 - root - INFO - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 18:36:25,365 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-26 18:36:25,365 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-26 18:36:25,365 - root - WARNING - [DEBUG] TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 18:36:25,365 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 18:36:25,365 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 18:36:25,366 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 18:36:25,366 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 18:36:25,366 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 18:36:25,366 - root - INFO - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 18:36:25,367 - root - INFO - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 18:36:25,367 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 18:36:25,367 - root - INFO - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 18:36:25,367 - root - INFO - [DEBUG] PRICE - TRX/EUR: Exchange ID: kraken
2025-06-26 18:36:25,368 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange kraken
2025-06-26 18:36:25,371 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 18:36:25,373 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 18:36:26,500 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 18:36:26,500 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-26 18:36:27,429 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-26 18:36:27,430 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': None, 'datetime': None, 'high': 0.234881, 'low': 0.230938, 'bid': 0.23156, 'bidVolume': 1892.0, 'ask': 0.23164, 'askVolume': 5680.0, 'vwap': 0.2326072, 'open': 0.233649, 'close': 0.231546, 'last': 0.231546, 'previousClose': None, 'change': -0.002103, 'percentage': -0.9000680507941399, 'average': 0.232597, 'baseVolume': 2922291.35647631, 'quoteVolume': 679746.0100141563, 'info': {'a': ['0.2316400', '5680', '5680.000'], 'b': ['0.2315600', '1892', '1892.000'], 'c': ['0.2315460', '91.17750685'], 'v': ['2123143.02142243', '2922291.35647631'], 'p': ['0.2320791', '0.2326072'], 't': [879, 1299], 'l': ['0.2309380', '0.2309380'], 'h': ['0.2348810', '0.2348810'], 'o': '0.2336490'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 18:36:27,430 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.231546
2025-06-26 18:36:27,430 - root - INFO - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.231546
2025-06-26 18:36:27,431 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 18:36:27,431 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 18:36:27,431 - root - INFO - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 18:36:27,431 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 18:36:27,431 - root - INFO - Available balance for EUR: 100.********
2025-06-26 18:36:27,431 - root - INFO - Reserved 0.599940 USDC for fees (rate: 0.4% with buffer)
2025-06-26 18:36:27,432 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 18:36:27,432 - root - INFO - Calculated position size for TRX/EUR: 427.******** (using 99.99% of 100, accounting for fees)
2025-06-26 18:36:27,432 - root - INFO - Calculated position size: 427.******** TRX
2025-06-26 18:36:27,432 - root - INFO - Entering position for TRX/EUR: 427.******** units at 0.******** (value: 98.******** EUR)
2025-06-26 18:36:27,432 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 18:36:27,432 - root - INFO - Paper trading buy for TRX/EUR: original=427.********, adjusted=424.********, after_fee=424.********
2025-06-26 18:36:27,438 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:36:27,438 - root - INFO - Created paper market buy order: TRX/EUR, amount: 424.*************, price: 0.231546
2025-06-26 18:36:27,438 - root - INFO - Filled amount: 424.******** TRX
2025-06-26 18:36:27,438 - root - INFO - Order fee: 0.******** EUR
2025-06-26 18:36:27,438 - root - INFO - Successfully entered position: TRX/EUR, amount: 424.********, price: 0.********
2025-06-26 18:36:27,443 - root - INFO - Trade executed: BUY TRX/EUR, amount=427.********, price=0.********, filled=424.********
2025-06-26 18:36:27,443 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:36:27,443 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 18:36:27,458 - root - INFO - Trade executed: BUY TRX/EUR, amount=427.********, price=0.********, filled=424.********
2025-06-26 18:36:27,458 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:36:27,459 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 18:36:27,459 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 427.099192816978, 'price': 0.231546, 'order': {'id': 'paper-1750955787-TRX/EUR-buy-424.*************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 424.*************, 'price': 0.231546, 'cost': 98.3986441515, 'fee': {'cost': 0.********41515, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 424.*************, 'remaining': 0, 'timestamp': 1750955787432, 'datetime': '2025-06-26T18:36:27.432603', 'trades': [], 'average': 0.231546, 'average_price': 0.231546}, 'filled_amount': 424.*************, 'fee': {'cost': 0.********41515, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T18:36:27.438666'}
2025-06-26 18:36:27,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:36:27,557 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 18:36:27,557 - root - INFO - Asset scores (sorted by score):
2025-06-26 18:36:27,557 - root - INFO -   BTC/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,557 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 18:36:27,557 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,558 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,560 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:36:27,560 - root - INFO - Rejected assets:
2025-06-26 18:36:27,560 - root - INFO -   BTC/EUR: reason=Failed to trade, score=12.0, rank=1
2025-06-26 18:36:27,560 - root - WARNING -   HIGH-SCORING ASSET REJECTED: BTC/EUR (rank 1, score 12.0) - Failed to trade
2025-06-26 18:36:27,561 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 18:36:27,561 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 18:36:27,561 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:36:27,674 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:36:27,675 - root - INFO - Strategy execution completed successfully in 22.97 seconds
2025-06-26 18:36:27,681 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 18:36:32,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:36:42,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:36:45,183 - root - INFO - Received signal 2, shutting down...
2025-06-26 18:36:52,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:36:55,208 - root - INFO - Network watchdog stopped
2025-06-26 18:36:55,209 - root - INFO - Network watchdog stopped
2025-06-26 18:36:55,209 - root - INFO - Background service stopped
2025-06-26 18:36:55,298 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
