2025-06-26 17:32:15,667 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:32:16,552 - root - INFO - Loaded 47 trade records from logs/trades\trade_log_********.json
2025-06-26 17:32:16,553 - root - INFO - Loaded 28 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:32:16,554 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:32:17,736 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:17,744 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:17,745 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:17,753 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:17,753 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:17,759 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:17,761 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:32:17,762 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:32:18,766 - root - INFO - Telegram command handlers registered
2025-06-26 17:32:18,766 - root - INFO - Telegram bot polling started
2025-06-26 17:32:18,766 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:32:18,766 - root - INFO - Telegram notification channel initialized
2025-06-26 17:32:18,768 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:32:18,768 - root - INFO - Loaded 24 templates from file
2025-06-26 17:32:18,769 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:32:18,769 - root - INFO - Notification manager initialized
2025-06-26 17:32:18,769 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:32:18,770 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:32:18,770 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:32:18,770 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:32:18,776 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:32:18,776 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:32:18,777 - root - INFO - Recovery manager initialized
2025-06-26 17:32:18,777 - root - ERROR - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:32:18,777 - root - ERROR - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:32:18,777 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:18,786 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:18,786 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:32:18,786 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:32:18,787 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:32:18,787 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:32:18,787 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:32:18,787 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:32:18,788 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:32:18,788 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:32:18,788 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:18,797 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:18,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:32:18,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:32:18,842 - telegram.ext.Application - INFO - Application started
2025-06-26 17:32:19,104 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:32:19,105 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:32:19,105 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:32:19,105 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:32:19,105 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:32:19,107 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:19,112 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:19,116 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:19,125 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:19,125 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:32:19,131 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:19,132 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:32:19,132 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:32:19,132 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:32:19,133 - root - INFO - Trading mode: paper
2025-06-26 17:32:19,133 - root - INFO - Trading enabled: True
2025-06-26 17:32:19,133 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:32:19,133 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:32:19,133 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:32:19,134 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:32:19,134 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:19,141 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:19,362 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:32:19,362 - root - INFO - Trading enabled in paper mode
2025-06-26 17:32:19,363 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:32:19,363 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:32:19,363 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:32:19,364 - root - INFO - Generated run ID: ********_173219
2025-06-26 17:32:19,364 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:32:19,364 - root - INFO - Background service initialized
2025-06-26 17:32:19,365 - root - INFO - Network watchdog started
2025-06-26 17:32:19,365 - root - INFO - Network watchdog started
2025-06-26 17:32:19,366 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:32:19,366 - root - INFO - Background service started
2025-06-26 17:32:19,367 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:32:19,367 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:32:19,367 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:32:19,368 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:32:19,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:32:25,385 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:32:25,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:32:25,487 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:32:25,488 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:32:25,488 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:32:25,491 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:32:25,515 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,515 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,516 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,516 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:32:25,517 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,529 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,529 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,529 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,530 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:32:25,530 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,535 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,535 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,535 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,535 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:32:25,537 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,548 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,549 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,549 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,549 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:32:25,549 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,565 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,565 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,566 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,566 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:32:25,566 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,581 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,581 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,581 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,582 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:32:25,582 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,597 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,598 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,598 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,598 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:32:25,599 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,612 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,612 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,612 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,613 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:32:25,613 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,627 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,627 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,627 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,627 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:32:25,628 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,635 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,635 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,635 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,636 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:32:25,636 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,651 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,651 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,652 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,652 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:32:25,654 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,670 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,670 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,671 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,671 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:32:25,673 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,684 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,685 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,685 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:32:25,685 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:32:25,686 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,689 - root - INFO - Using 13 trend assets (USDT) for analysis and 13 trading assets (EUR) for execution
2025-06-26 17:32:25,691 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:32:25,691 - root - INFO -   - Number of indicators: 8
2025-06-26 17:32:25,691 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:32:25,691 - root - INFO -   - Combination method: consensus
2025-06-26 17:32:25,692 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:32:25,692 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:32:25,692 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:32:25,693 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:32:25,693 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: NOT FOUND
2025-06-26 17:32:25,693 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 9
2025-06-26 17:32:25,693 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:32:25,694 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:32:25,694 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:32:25,694 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:32:25,694 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:32:25,694 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:32:25,695 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:32:25,695 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:25,701 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:25,701 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:25,707 - root - INFO - Configuration saved successfully.
2025-06-26 17:32:25,707 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:32:25,709 - root - INFO - Number of trend detection assets: 13
2025-06-26 17:32:25,709 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:32:25,709 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:32:25,709 - root - INFO - Number of trading assets: 13
2025-06-26 17:32:25,709 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:32:25,887 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:32:25,894 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:25,902 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:32:25,909 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:25,909 - root - INFO - Execution context: backtesting
2025-06-26 17:32:25,909 - root - INFO - Execution timing: candle_close
2025-06-26 17:32:25,909 - root - INFO - Ratio calculation method: independent
2025-06-26 17:32:25,909 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:32:25,911 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:32:25,911 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:32:25,911 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:32:25,911 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:32:25,911 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:32:25,912 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:32:25,913 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,914 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,914 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,915 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,915 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,915 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,916 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,916 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,916 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,916 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,917 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,917 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,917 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:32:25,917 - root - INFO - Checking cache for 13 symbols (1d)...
2025-06-26 17:32:25,931 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,932 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:25,933 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,933 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:32:25,946 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,946 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:25,947 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,947 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:32:25,953 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,954 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:25,954 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,956 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:32:25,968 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,969 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:25,969 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,971 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:32:25,981 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,983 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:25,983 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,983 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:32:25,996 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:25,996 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:25,998 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:25,998 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:32:26,013 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,013 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,014 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,014 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:32:26,028 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,029 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,030 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,031 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:32:26,044 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,045 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,046 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,047 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:32:26,053 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,053 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,054 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,054 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:32:26,068 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,069 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,071 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,071 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:32:26,084 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,085 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,085 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,085 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:32:26,097 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,098 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:32:26,099 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,099 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:32:26,099 - root - INFO - All 13 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:32:26,099 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,099 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,101 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,102 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,102 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,102 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,102 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:32:26,116 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:32:26,116 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:32:26,117 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:32:26,117 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:32:26,117 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:32:26,123 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:26,124 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:32:26,124 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:32:26,124 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:32:26,124 - root - INFO - Override: combination_method = consensus
2025-06-26 17:32:26,124 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:32:26,124 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:32:26,125 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:32:26,125 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:32:26,125 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:32:26,126 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:32:26,139 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:32:26,140 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:32:26,140 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:32:26,140 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:32:26,140 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:26,140 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:32:26,166 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:32:26,166 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:32:26,166 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:32:26,166 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:32:26,175 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:32:26,176 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:32:26,177 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:32:26,516 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:32:26,516 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:32:26,555 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:32:26,556 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:32:26,557 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:32:26,557 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:32:26,628 - root - INFO - Generated DPSD signals
2025-06-26 17:32:26,629 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:32:26,629 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:32:26,629 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:32:26,635 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:32:26,636 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:32:26,636 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:32:26,683 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:32:26,684 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:32:26,684 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:32:26,762 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:32:26,767 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:32:26,767 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:32:26,767 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:32:26,768 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:32:26,768 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:32:26,769 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:32:26,776 - root - INFO - Configuration saved successfully.
2025-06-26 17:32:26,777 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:32:26,777 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:32:26,783 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:26,784 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:32:26,784 - root - INFO - Calculating ratio PGO signals for 13 assets with PGO(35) using full OHLCV data
2025-06-26 17:32:26,784 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:32:26,799 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:26,800 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:26,814 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:26,819 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:26,834 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:32:26,834 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:26,849 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:32:26,854 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:26,869 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:26,871 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:26,884 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:26,889 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:26,906 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:32:26,906 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:26,919 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:32:26,926 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:26,941 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:32:26,942 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:26,954 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:32:26,960 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:26,976 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:32:26,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:26,991 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:32:26,995 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:27,011 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:32:27,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,025 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:32:27,030 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:27,044 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:32:27,044 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,059 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:32:27,065 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:27,074 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:32:27,074 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,091 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:32:27,091 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:27,111 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:27,111 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,124 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:27,131 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:27,146 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,161 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:27,181 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:32:27,181 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,191 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:32:27,201 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:27,218 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:32:27,218 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,231 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:32:27,239 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:27,255 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:27,255 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,268 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:27,274 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:27,291 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:27,291 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,307 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:27,311 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:27,324 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:32:27,324 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,341 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:32:27,348 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:27,361 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:32:27,361 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,374 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:32:27,383 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:27,400 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:27,401 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,411 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:27,422 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:27,439 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:27,439 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,451 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:27,457 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:27,475 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:32:27,475 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,491 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:32:27,492 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:27,511 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:32:27,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,531 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:32:27,534 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:27,557 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:27,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,574 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:27,581 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:27,601 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:27,601 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,617 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:27,623 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:27,642 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:27,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,661 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:27,667 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:27,689 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:27,689 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,707 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:27,711 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:27,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,746 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:27,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,784 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:27,791 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:32:27,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,811 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:32:27,816 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:27,831 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,851 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:27,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,884 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:27,901 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:27,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,916 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:27,922 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:27,938 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,956 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:27,972 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:27,991 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:28,007 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:28,007 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,024 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:28,024 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:28,041 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,061 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:28,074 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,096 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:28,111 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:32:28,113 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,124 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:32:28,131 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:28,141 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,161 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:28,174 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:28,174 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,194 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:28,199 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:28,211 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:28,211 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,224 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:28,231 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:28,246 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:32:28,246 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,261 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:32:28,263 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:28,281 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:32:28,281 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,291 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:32:28,301 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:28,311 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:28,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,323 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:28,331 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:28,341 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:28,341 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,361 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:28,361 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:28,374 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:28,381 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,391 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:32:28,391 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:28,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,431 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:28,441 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,461 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:28,481 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:32:28,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,491 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:32:28,501 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:28,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,531 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:28,541 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,561 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:28,581 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,601 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:28,611 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,633 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:28,641 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:28,641 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,661 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:28,668 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:28,682 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:28,682 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,698 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:28,704 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:28,722 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:32:28,722 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,735 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:32:28,744 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:28,773 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,807 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:28,834 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,888 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:28,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:32:28,923 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:32:28,928 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:28,981 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:32:28,985 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:29,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,032 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:29,052 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:29,052 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,069 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:29,076 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:29,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,117 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:29,133 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,152 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:29,168 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,189 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:29,207 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,228 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:29,245 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:32:29,246 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,262 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:32:29,266 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:29,283 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,303 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:29,322 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:32:29,322 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,337 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:32:29,342 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:29,359 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,378 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:29,394 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,415 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:29,431 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,451 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:29,466 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,492 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:29,510 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,531 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:29,547 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:29,549 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,564 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:29,568 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:29,585 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,605 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:29,624 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:32:29,625 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,641 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:32:29,647 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:29,662 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,681 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:29,699 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:32:29,699 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,716 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:32:29,721 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:29,743 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:32:29,743 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,759 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:32:29,763 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:29,779 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:32:29,780 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,795 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:32:29,800 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:29,819 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,837 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:29,855 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:29,856 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,869 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:29,877 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:29,893 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:29,894 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,909 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:29,915 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:29,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,959 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:29,977 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:29,977 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:29,994 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:29,999 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:30,019 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,046 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:30,066 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,086 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:30,107 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,126 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:30,143 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,161 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:30,183 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:32:30,183 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,200 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:32:30,203 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:30,227 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,243 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:30,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,281 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:30,298 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,316 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:30,331 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:30,331 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,341 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:30,351 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:30,375 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,395 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:30,411 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,431 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:30,452 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,476 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:30,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,518 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:30,539 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:30,540 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,557 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:30,564 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:30,583 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,608 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:30,630 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:30,630 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,653 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:30,659 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:30,677 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:30,677 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,693 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:30,700 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:30,717 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:32:30,717 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,734 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:32:30,743 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:30,760 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,781 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:30,799 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:30,799 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,816 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:30,821 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:30,843 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:30,843 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,859 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:30,865 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:30,884 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,908 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:30,928 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,949 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:30,968 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:30,991 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:31,011 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,033 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:31,052 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,077 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:31,102 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,131 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:31,152 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,179 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:31,203 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:32:31,203 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,227 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:32:31,232 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:31,250 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:32:31,250 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,266 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:32:31,273 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:31,295 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:31,295 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,318 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:32:31,327 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:31,346 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:32:31,347 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,364 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:32:31,369 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:31,392 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,413 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:31,432 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,453 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:31,476 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,499 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:31,518 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,541 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:31,563 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,585 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:31,609 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,642 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:31,663 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:31,663 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,684 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:31,693 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:31,715 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:31,715 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,737 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:32:31,747 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:31,767 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:31,767 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,793 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:31,799 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:31,819 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:32:31,819 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,843 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:32:31,848 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:31,869 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:32:31,869 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,893 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:32:31,898 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:31,916 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:31,916 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,933 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:31,939 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:31,963 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:32:31,963 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:31,981 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:32:31,985 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:32,005 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:32:32,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,026 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:32:32,031 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:32,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,076 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:32,096 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:32,096 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,113 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:32,119 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:32,136 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,162 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:32,181 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:32,181 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,198 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:32,203 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:32,228 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:32,228 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,246 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:32,251 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:32,277 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:32,277 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,295 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:32:32,299 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:32,321 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:32:32,323 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,344 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:32:32,350 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:32,371 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:32:32,371 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,397 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:32:32,403 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:32,426 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:32,426 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,443 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:32,449 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:32,464 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:32:32,464 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,483 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:32:32,490 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:32,511 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:32,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,528 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:32:32,534 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:32,550 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:32,551 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,565 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:32,569 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:32,592 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,611 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:32,627 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,648 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:32:32,664 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,684 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:32:32,707 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,736 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:32:32,761 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:32,761 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,781 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:32:32,789 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:32:32,814 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:32:32,814 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,828 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:32:32,834 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:32:32,850 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:32:32,851 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,909 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:32:32,916 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:32:32,937 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:32:32,938 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,954 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:32:32,960 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:32:32,977 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:32:32,978 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:32,993 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:32:32,997 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:32:33,013 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:32:33,014 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:33,028 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:32:33,034 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:32:33,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:33,067 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:32:33,082 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:33,083 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:33,098 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:32:33,103 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:32:33,124 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:33,145 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:32:33,159 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:32:33,178 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:32:34,586 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:32:34,588 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 13)
2025-06-26 17:32:34,588 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:32:34,588 - root - INFO - Date ranges for each asset:
2025-06-26 17:32:34,591 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,591 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,591 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,591 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,591 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,591 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,593 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,593 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,593 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,593 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,594 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,594 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,594 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,594 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:32:34,594 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:32:34,594 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:32:34,594 - root - INFO -    Execution Method: candle_close
2025-06-26 17:32:34,594 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:32:34,594 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:32:34,601 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,601 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,601 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,601 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,604 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,604 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,604 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,605 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,605 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,605 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,605 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,605 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,606 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,606 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:32:34,606 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:32:34,606 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:32:34,606 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,606 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:32:34,607 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:32:34,607 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,607 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:32:34,611 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:32:34,611 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:32:34,611 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,611 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:32:34,611 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:32:34,611 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:32:34,611 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,611 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:32:34,616 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 17:32:34,616 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 17:32:34,616 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 17:32:34,616 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,616 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:32:34,616 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:32:34,616 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 17:32:34,621 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 17:32:34,621 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 17:32:34,622 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 17:32:34,622 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,622 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:32:34,622 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:32:34,622 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 17:32:34,625 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 17:32:34,625 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 17:32:34,625 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 17:32:34,625 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,626 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:32:34,626 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:32:34,626 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 17:32:34,628 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:32:34,628 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:32:34,628 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:32:34,628 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,631 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:32:34,631 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:32:34,631 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:32:34,633 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 17:32:34,633 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 17:32:34,635 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 17:32:34,635 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,635 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:32:34,635 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:32:34,635 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 17:32:34,635 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 17:32:34,635 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 17:32:34,635 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 17:32:34,638 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,638 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:32:34,638 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:32:34,638 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 17:32:34,639 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:32:34,641 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:32:34,641 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:32:34,641 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,641 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:32:34,641 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:32:34,641 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:32:34,641 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:32:34,641 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:32:34,641 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:32:34,641 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,641 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:32:34,641 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:32:34,641 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:32:34,641 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:32:34,641 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:32:34,641 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:32:34,641 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,648 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:32:34,648 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:32:34,648 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:32:34,648 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:32:34,648 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:32:34,648 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:32:34,651 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,651 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:32:34,651 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:32:34,651 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:32:34,651 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:32:34,651 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:32:34,651 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:32:34,651 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,651 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:32:34,651 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:32:34,651 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:32:34,651 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:32:34,651 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:32:34,655 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:32:34,655 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,655 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:32:34,655 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:32:34,655 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:32:34,656 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 17:32:34,657 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 17:32:34,657 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 17:32:34,657 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,657 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:32:34,657 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:32:34,657 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 17:32:34,663 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 17:32:34,663 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 17:32:34,663 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 17:32:34,663 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,663 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:32:34,663 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:32:34,663 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 17:32:34,667 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:32:34,667 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:32:34,667 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:32:34,667 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,667 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:32:34,667 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:32:34,667 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:32:34,676 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:32:34,677 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:32:34,677 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:32:34,677 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,677 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:32:34,679 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:32:34,679 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:32:34,682 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:32:34,682 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:32:34,683 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:32:34,683 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,684 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:32:34,684 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:32:34,684 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:32:34,684 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:32:34,684 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:32:34,684 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:32:34,684 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,684 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:32:34,684 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:32:34,684 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:32:34,688 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:32:34,688 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:32:34,688 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:32:34,688 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,688 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:32:34,689 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:32:34,689 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:32:34,699 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:32:34,699 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:32:34,699 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:32:34,701 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:32:34,701 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:32:34,701 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:32:34,701 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:32:34,731 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:32:34,731 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:32:34,731 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:32:34,731 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:32:34,733 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:32:34,733 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 17:32:34,738 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:32:34,738 - root - INFO - DEBUG: self.elapsed_time = 0.15060782432556152 seconds
2025-06-26 17:32:34,741 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:32:34,741 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:32:34,741 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:32:34,741 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:32:34,741 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,741 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,749 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,749 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,751 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,753 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,753 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,755 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,757 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,757 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,761 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,763 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,763 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:32:34,765 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:32:34,765 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:32:34,766 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:32:34,766 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:32:34,766 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:32:34,766 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:32:34,771 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:32:34,772 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:32:34,774 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:32:34,774 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:32:34,776 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:32:34,776 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:32:34,779 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:32:34,779 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:32:34,781 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:32:34,781 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:32:34,781 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:32:34,781 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:32:34,784 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:32:34,784 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:32:34,784 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:32:34,784 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:32:34,788 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:32:34,788 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:32:34,792 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:32:34,792 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:32:34,794 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:32:34,801 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:34,811 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:32:34,895 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:32:34,900 - root - INFO - Configuration loaded successfully.
2025-06-26 17:32:36,061 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:32:36,061 - root - INFO - Added 13 buy-and-hold curves to results
2025-06-26 17:32:36,061 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,061 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,065 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,065 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,065 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,066 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,066 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:32:36,071 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:32:36,076 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:32:36,077 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:32:36,077 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:32:36,078 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:32:36,081 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:32:36,081 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:32:36,081 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:32:36,090 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_173219.csv
2025-06-26 17:32:36,090 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_173219.csv
2025-06-26 17:32:36,090 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:32:36,090 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:32:36,091 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:32:36,091 - root - INFO - Success flag set to: True
2025-06-26 17:32:36,091 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:32:36,091 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:32:36,091 - root - INFO -   - buy_hold_curves: dict with 13 entries
2025-06-26 17:32:36,091 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:32:36,091 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:32:36,091 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:32:36,091 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:32:36,093 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:32:36,093 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:32:36,093 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:32:36,093 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:32:36,093 - root - INFO -   - message: <class 'str'>
2025-06-26 17:32:36,093 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:32:36,093 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 17:32:36,093 - root - ERROR - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 17:32:36,095 - root - ERROR - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 17:32:36,095 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 17:32:36,107 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR',
       'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR',
       'DOT/EUR'],
      dtype='object')
2025-06-26 17:32:36,111 - root - INFO - Last row columns: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:32:36,111 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG]   - MTPI signal: 1
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG]   - Use MTPI signal: False
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] ERROR - TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 17:32:36,111 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:32:36,111 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:32:36,111 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:32:36,111 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:32:36,111 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:32:36,111 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 17:32:36,111 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-26 17:32:36,111 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 17:32:36,116 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 17:32:36,372 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 17:32:36,372 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1750951952585, 'datetime': '2025-06-26T15:32:32.585Z', 'high': 0.23507, 'low': 0.23076, 'bid': 0.23122, 'bidVolume': 2809.789759, 'ask': 0.23135, 'askVolume': 276.389487, 'vwap': 0.2328126025440634, 'open': 0.23311, 'close': 0.23128, 'last': 0.23128, 'previousClose': None, 'change': -0.00183, 'percentage': -0.7850371069452189, 'average': 0.232195, 'baseVolume': 1393905.82298, 'quoteVolume': 324518.84234929836, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1750865552585, 'timestamp': 1750951952585, 'open': '0.23311', 'openTimestamp': 1750865649161, 'high': '0.23507', 'low': '0.23076', 'last': '0.23128', 'closeTimestamp': 1750951935756, 'bid': '0.2312200', 'bidSize': '2809.789759', 'ask': '0.2313500', 'askSize': '276.389487', 'volume': '1393905.82298', 'volumeQuote': '324518.84234929836'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23128
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23128
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 17:32:36,416 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 17:32:36,416 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:32:36,416 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:32:36,422 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:32:36,423 - root - INFO - Calculated position size for TRX/EUR: 43.******** (using 10% of 100, accounting for fees)
2025-06-26 17:32:36,423 - root - INFO - Calculated position size: 43.******** TRX
2025-06-26 17:32:36,423 - root - INFO - Entering position for TRX/EUR: 43.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:32:36,423 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:32:36,423 - root - INFO - Paper trading buy for TRX/EUR: original=43.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:32:36,423 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:32:36,423 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.**************, price: 0.23128
2025-06-26 17:32:36,423 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:32:36,423 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:32:36,423 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:32:36,433 - root - INFO - Trade executed: BUY TRX/EUR, amount=43.********, price=0.********, filled=42.********
2025-06-26 17:32:36,433 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:32:36,433 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:32:36,443 - root - INFO - Trade executed: BUY TRX/EUR, amount=43.********, price=0.********, filled=42.********
2025-06-26 17:32:36,443 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:32:36,443 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:32:36,443 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 43.02144586648218, 'price': 0.23128, 'order': {'id': 'paper-1750951956-TRX/EUR-buy-42.**************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.**************, 'price': 0.23128, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.**************, 'remaining': 0, 'timestamp': 1750951956423, 'datetime': '2025-06-26T17:32:36.423605', 'trades': [], 'average': 0.23128, 'average_price': 0.23128}, 'filled_amount': 42.**************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:32:36.423605'}
2025-06-26 17:32:36,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:32:36,520 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:32:36,520 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:32:36,520 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:32:36,520 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,521 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,521 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,521 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,521 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,521 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,521 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,522 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,524 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,524 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,524 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,524 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:32:36,525 - root - INFO - Asset selection logged with 13 assets scored and 1 assets selected
2025-06-26 17:32:36,525 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:32:36,525 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:32:36,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:32:36,578 - root - INFO - Strategy execution completed successfully in 17.21 seconds
2025-06-26 17:32:36,582 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:32:38,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:32:48,941 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:32:58,961 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:33:08,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:33:18,991 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:33:29,002 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:33:39,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:33:49,033 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:33:59,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:34:09,061 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:34:19,079 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:34:29,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:34:39,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:34:49,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:34:59,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:35:09,171 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:35:19,201 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:35:29,222 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:35:39,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:35:49,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:35:59,273 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:36:09,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:36:19,301 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:36:29,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:36:39,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:36:49,337 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:36:59,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:37:09,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:37:19,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:37:29,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:37:34,143 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:37:38,988 - root - INFO - Network watchdog stopped
2025-06-26 17:37:38,989 - root - INFO - Network watchdog stopped
2025-06-26 17:37:38,990 - root - INFO - Background service stopped
2025-06-26 17:37:39,065 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
