"""
Beta Detection System - Table Display Module
Creates formatted tables similar to TradingView implementation.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import logging
from rich.console import Console
from rich.table import Table
from rich.text import Text
from rich import box
import json
import os
import threading
import time

try:
    from .beta_gui_display import show_beta_gui
    GUI_AVAILABLE = True
except ImportError:
    try:
        from beta_gui_display import show_beta_gui
        GUI_AVAILABLE = True
    except ImportError:
        GUI_AVAILABLE = False
        print("GUI display not available. Install tkinter for GUI support.")

logger = logging.getLogger(__name__)

class BetaTableDisplay:
    """
    Display beta values in formatted tables similar to TradingView.
    """
    
    def __init__(self):
        self.console = Console()
        
    def create_beta_table(self, beta_results: Dict[str, Dict],
                         previous_betas: Optional[Dict[str, float]] = None,
                         max_assets_per_column: int = 20,
                         show_gui: bool = False,
                         show_brackets: bool = False) -> None:
        """
        Create and display a beta table similar to TradingView layout.

        Args:
            beta_results: Current beta calculation results
            previous_betas: Previous beta values for ROC calculation
            max_assets_per_column: Maximum assets per column (default: 20)
            show_gui: Whether to show GUI window in addition to terminal display
            show_brackets: Whether to display beta brackets instead of unified table
        """
        if not beta_results:
            self.console.print("[red]No beta data available[/red]")
            return

        # Show beta brackets if requested
        if show_brackets:
            self._display_beta_brackets(beta_results, previous_betas)
            return

        # Determine display format based on number of assets
        assets = list(beta_results.keys())
        num_assets = len(assets)

        # Show terminal display
        if num_assets <= max_assets_per_column:
            self._display_single_column_table(beta_results, previous_betas)
        elif num_assets <= max_assets_per_column * 2:
            self._display_dual_column_table(beta_results, previous_betas, max_assets_per_column)
        else:
            # For very large asset lists, use multi-column display
            self._display_multi_column_table(beta_results, previous_betas, max_assets_per_column)

        # Show GUI if requested and available
        if show_gui and GUI_AVAILABLE:
            self.console.print("\n[cyan]🖥️  Opening GUI window for better display...[/cyan]")
            self.console.print("[yellow]💡 The GUI window will open after the analysis completes.[/yellow]")
            try:
                # Use blocking=True to keep GUI open after main program ends
                show_beta_gui(beta_results, previous_betas, blocking=True)
                self.console.print("[green]✅ GUI window closed by user.[/green]")
            except Exception as e:
                self.console.print(f"[red]❌ Failed to open GUI: {e}[/red]")
                import traceback
                traceback.print_exc()
        elif show_gui and not GUI_AVAILABLE:
            self.console.print("[yellow]⚠️  GUI not available. Install tkinter for GUI support.[/yellow]")
    
    def _display_single_column_table(self, beta_results: Dict[str, Dict], 
                                   previous_betas: Optional[Dict[str, float]] = None) -> None:
        """Display beta table in single column format."""
        
        table = Table(title="💎 Beta Table | BTC Benchmark | V1 💎", 
                     box=box.DOUBLE_EDGE,
                     title_style="bold cyan")
        
        # Add columns with proper widths
        table.add_column("TICKER", style="white", no_wrap=True, width=15)
        table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
        table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)
        
        # Sort assets by beta value (descending)
        sorted_assets = sorted(beta_results.items(), 
                             key=lambda x: x[1]['beta'], 
                             reverse=True)
        
        for symbol, data in sorted_assets:
            beta_value = data['beta']
            
            # Calculate ROC arrow
            roc_arrow = "→"
            if previous_betas and symbol in previous_betas:
                prev_beta = previous_betas[symbol]
                roc_arrow = self._get_roc_arrow(beta_value, prev_beta)
            
            # Format beta value
            beta_str = f"{beta_value:.2f}"
            
            # Color coding for beta values
            if beta_value > 1.5:
                beta_style = "bright_red"
            elif beta_value > 1.0:
                beta_style = "red"
            elif beta_value > 0.5:
                beta_style = "yellow"
            else:
                beta_style = "green"
            
            table.add_row(
                symbol,
                Text(beta_str, style=beta_style),
                Text(f" {roc_arrow} ", style="bright_yellow")
            )
        
        self.console.print(table)
        
        # Print summary statistics
        self._print_summary_stats(beta_results)
    
    def _display_dual_column_table(self, beta_results: Dict[str, Dict], 
                                  previous_betas: Optional[Dict[str, float]] = None,
                                  max_assets_per_column: int = 15) -> None:
        """Display beta table in dual column format (like TradingView)."""
        
        table = Table(title="💎 Beta Table | BTC Benchmark | V1 💎", 
                     box=box.DOUBLE_EDGE,
                     title_style="bold cyan")
        
        # Add columns for dual layout with proper widths
        table.add_column("TICKER", style="white", no_wrap=True, width=15)
        table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
        table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)
        table.add_column("TICKER", style="white", no_wrap=True, width=15)
        table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
        table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)
        
        # Sort assets by beta value (descending)
        sorted_assets = sorted(beta_results.items(), 
                             key=lambda x: x[1]['beta'], 
                             reverse=True)
        
        # Split into two columns
        col1_assets = sorted_assets[:max_assets_per_column]
        col2_assets = sorted_assets[max_assets_per_column:]
        
        # Fill rows
        max_rows = max(len(col1_assets), len(col2_assets))
        
        for i in range(max_rows):
            row_data = []
            
            # Column 1
            if i < len(col1_assets):
                symbol, data = col1_assets[i]
                beta_value = data['beta']
                roc_arrow = self._get_roc_arrow_with_previous(symbol, beta_value, previous_betas)
                beta_str = f"{beta_value:.2f}"
                beta_style = self._get_beta_color_style(beta_value)
                
                row_data.extend([
                    symbol,
                    Text(beta_str, style=beta_style),
                    Text(f" {roc_arrow} ", style="bright_yellow")
                ])
            else:
                row_data.extend(["", "", ""])
            
            # Column 2
            if i < len(col2_assets):
                symbol, data = col2_assets[i]
                beta_value = data['beta']
                roc_arrow = self._get_roc_arrow_with_previous(symbol, beta_value, previous_betas)
                beta_str = f"{beta_value:.2f}"
                beta_style = self._get_beta_color_style(beta_value)
                
                row_data.extend([
                    symbol,
                    Text(beta_str, style=beta_style),
                    Text(f" {roc_arrow} ", style="bright_yellow")
                ])
            else:
                row_data.extend(["", "", ""])
            
            table.add_row(*row_data)
        
        self.console.print(table)
        
        # Print summary statistics
        self._print_summary_stats(beta_results)

    def _display_multi_column_table(self, beta_results: Dict[str, Dict],
                                   previous_betas: Optional[Dict[str, float]] = None,
                                   max_assets_per_column: int = 20) -> None:
        """Display beta table in multiple columns for large asset lists."""

        # Calculate optimal number of columns (max 4 columns to fit screen)
        num_assets = len(beta_results)
        num_columns = min(4, (num_assets + max_assets_per_column - 1) // max_assets_per_column)

        table = Table(title=f"💎 Beta Table | BTC Benchmark | {num_assets} Assets | V1 💎",
                     box=box.DOUBLE_EDGE,
                     title_style="bold cyan")

        # Add columns dynamically based on number of columns needed
        for i in range(num_columns):
            table.add_column("TICKER", style="white", no_wrap=True, width=15)
            table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
            table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)

        # Sort assets by beta value (descending)
        sorted_assets = sorted(beta_results.items(),
                             key=lambda x: x[1]['beta'],
                             reverse=True)

        # Split assets into columns
        assets_per_column = (num_assets + num_columns - 1) // num_columns
        columns_data = []
        for i in range(num_columns):
            start_idx = i * assets_per_column
            end_idx = min(start_idx + assets_per_column, num_assets)
            columns_data.append(sorted_assets[start_idx:end_idx])

        # Fill rows
        max_rows = max(len(col) for col in columns_data)

        for row_idx in range(max_rows):
            row_data = []

            for col_idx in range(num_columns):
                if row_idx < len(columns_data[col_idx]):
                    symbol, data = columns_data[col_idx][row_idx]
                    beta_value = data['beta']
                    roc_arrow = self._get_roc_arrow_with_previous(symbol, beta_value, previous_betas)
                    beta_str = f"{beta_value:.2f}"
                    beta_style = self._get_beta_color_style(beta_value)

                    row_data.extend([
                        symbol,
                        Text(beta_str, style=beta_style),
                        Text(f" {roc_arrow} ", style="bright_yellow")
                    ])
                else:
                    row_data.extend(["", "", ""])

            table.add_row(*row_data)

        self.console.print(table)

        # Print summary statistics
        self._print_summary_stats(beta_results)

    def _display_beta_brackets(self, beta_results: Dict[str, Dict],
                              previous_betas: Optional[Dict[str, float]] = None) -> None:
        """Display beta results in three separate tables based on beta thresholds."""

        # Define beta thresholds
        HIGH_BETA_THRESHOLD = 2.0
        MEDIUM_BETA_THRESHOLD = 1.5

        # Categorize assets by beta value
        high_beta_assets = {}
        medium_beta_assets = {}
        low_beta_assets = {}

        for symbol, data in beta_results.items():
            beta_value = data['beta']
            if beta_value >= HIGH_BETA_THRESHOLD:
                high_beta_assets[symbol] = data
            elif beta_value >= MEDIUM_BETA_THRESHOLD:
                medium_beta_assets[symbol] = data
            else:
                low_beta_assets[symbol] = data

        # Display High Beta Bracket (β ≥ 2.0)
        if high_beta_assets:
            self._display_bracket_table(
                high_beta_assets,
                previous_betas,
                title=f"🔥 HIGH BETA BRACKET (β ≥ {HIGH_BETA_THRESHOLD}) - {len(high_beta_assets)} Assets",
                title_style="bold bright_red"
            )
            self.console.print()  # Add spacing

        # Display Medium Beta Bracket (1.5 ≤ β < 2.0)
        if medium_beta_assets:
            self._display_bracket_table(
                medium_beta_assets,
                previous_betas,
                title=f"⚡ MEDIUM BETA BRACKET ({MEDIUM_BETA_THRESHOLD} ≤ β < {HIGH_BETA_THRESHOLD}) - {len(medium_beta_assets)} Assets",
                title_style="bold yellow"
            )
            self.console.print()  # Add spacing

        # Display Low Beta Bracket (β < 1.5)
        if low_beta_assets:
            self._display_bracket_table(
                low_beta_assets,
                previous_betas,
                title=f"🛡️ LOW BETA BRACKET (β < {MEDIUM_BETA_THRESHOLD}) - {len(low_beta_assets)} Assets",
                title_style="bold green"
            )
            self.console.print()  # Add spacing

        # Print overall summary statistics
        self._print_bracket_summary_stats(beta_results, high_beta_assets, medium_beta_assets, low_beta_assets)

    def _display_bracket_table(self, bracket_assets: Dict[str, Dict],
                              previous_betas: Optional[Dict[str, float]] = None,
                              title: str = "Beta Bracket",
                              title_style: str = "bold cyan") -> None:
        """Display a single bracket table with assets sorted by beta value."""

        table = Table(title=title,
                     box=box.DOUBLE_EDGE,
                     title_style=title_style)

        # Determine number of columns based on asset count
        num_assets = len(bracket_assets)
        if num_assets <= 15:
            # Single column for small brackets
            table.add_column("TICKER", style="white", no_wrap=True, width=15)
            table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
            table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)

            # Sort assets by beta value (descending)
            sorted_assets = sorted(bracket_assets.items(),
                                 key=lambda x: x[1]['beta'],
                                 reverse=True)

            for symbol, data in sorted_assets:
                beta_value = data['beta']
                roc_arrow = self._get_roc_arrow_with_previous(symbol, beta_value, previous_betas)
                beta_str = f"{beta_value:.2f}"
                beta_style = self._get_beta_color_style(beta_value)

                table.add_row(
                    symbol,
                    Text(beta_str, style=beta_style),
                    Text(f" {roc_arrow} ", style="bright_yellow")
                )
        else:
            # Dual column for larger brackets
            table.add_column("TICKER", style="white", no_wrap=True, width=15)
            table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
            table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)
            table.add_column("TICKER", style="white", no_wrap=True, width=15)
            table.add_column("Beta - BTC", style="yellow", justify="right", width=12)
            table.add_column("+/- ROC", style="bright_yellow", justify="center", width=9)

            # Sort assets by beta value (descending)
            sorted_assets = sorted(bracket_assets.items(),
                                 key=lambda x: x[1]['beta'],
                                 reverse=True)

            # Split into two columns
            mid_point = (len(sorted_assets) + 1) // 2
            col1_assets = sorted_assets[:mid_point]
            col2_assets = sorted_assets[mid_point:]

            # Fill rows
            max_rows = max(len(col1_assets), len(col2_assets))

            for i in range(max_rows):
                row_data = []

                # Column 1
                if i < len(col1_assets):
                    symbol, data = col1_assets[i]
                    beta_value = data['beta']
                    roc_arrow = self._get_roc_arrow_with_previous(symbol, beta_value, previous_betas)
                    beta_str = f"{beta_value:.2f}"
                    beta_style = self._get_beta_color_style(beta_value)

                    row_data.extend([
                        symbol,
                        Text(beta_str, style=beta_style),
                        Text(f" {roc_arrow} ", style="bright_yellow")
                    ])
                else:
                    row_data.extend(["", "", ""])

                # Column 2
                if i < len(col2_assets):
                    symbol, data = col2_assets[i]
                    beta_value = data['beta']
                    roc_arrow = self._get_roc_arrow_with_previous(symbol, beta_value, previous_betas)
                    beta_str = f"{beta_value:.2f}"
                    beta_style = self._get_beta_color_style(beta_value)

                    row_data.extend([
                        symbol,
                        Text(beta_str, style=beta_style),
                        Text(f" {roc_arrow} ", style="bright_yellow")
                    ])
                else:
                    row_data.extend(["", "", ""])

                table.add_row(*row_data)

        self.console.print(table)

    def _print_bracket_summary_stats(self, all_results: Dict[str, Dict],
                                   high_beta: Dict[str, Dict],
                                   medium_beta: Dict[str, Dict],
                                   low_beta: Dict[str, Dict]) -> None:
        """Print summary statistics for beta brackets."""

        stats_table = Table(title="📊 Beta Bracket Statistics", box=box.SIMPLE)
        stats_table.add_column("Bracket", style="cyan", width=20)
        stats_table.add_column("Count", style="white", justify="right", width=8)
        stats_table.add_column("Avg Beta", style="yellow", justify="right", width=10)
        stats_table.add_column("Range", style="green", justify="right", width=15)

        # Calculate stats for each bracket
        brackets = [
            ("🔥 High Beta (≥2.0)", high_beta, "bright_red"),
            ("⚡ Medium Beta (1.5-2.0)", medium_beta, "yellow"),
            ("🛡️ Low Beta (<1.5)", low_beta, "green")
        ]

        for bracket_name, bracket_data, color in brackets:
            if bracket_data:
                betas = [data['beta'] for data in bracket_data.values()]
                avg_beta = np.mean(betas)
                min_beta = np.min(betas)
                max_beta = np.max(betas)

                stats_table.add_row(
                    Text(bracket_name, style=color),
                    str(len(bracket_data)),
                    f"{avg_beta:.3f}",
                    f"{min_beta:.2f}-{max_beta:.2f}"
                )
            else:
                stats_table.add_row(
                    Text(bracket_name, style=color),
                    "0",
                    "N/A",
                    "N/A"
                )

        # Add total row
        all_betas = [data['beta'] for data in all_results.values()]
        stats_table.add_row(
            Text("📈 Total", style="bold cyan"),
            str(len(all_results)),
            f"{np.mean(all_betas):.3f}",
            f"{np.min(all_betas):.2f}-{np.max(all_betas):.2f}"
        )

        self.console.print(stats_table)

    def _get_roc_arrow(self, current_beta: float, previous_beta: float,
                      threshold: float = 0.05) -> str:
        """Get ROC arrow based on beta change."""
        if pd.isna(previous_beta) or pd.isna(current_beta):
            return "→"
        
        change = current_beta - previous_beta
        
        if change > threshold:
            return "↑"
        elif change < -threshold:
            return "↓"
        elif change > 0:
            return "→"
        else:
            return "←"
    
    def _get_roc_arrow_with_previous(self, symbol: str, current_beta: float, 
                                   previous_betas: Optional[Dict[str, float]]) -> str:
        """Get ROC arrow with previous betas check."""
        if previous_betas and symbol in previous_betas:
            return self._get_roc_arrow(current_beta, previous_betas[symbol])
        return "→"
    
    def _get_beta_color_style(self, beta_value: float) -> str:
        """Get color style based on beta value."""
        if beta_value > 1.5:
            return "bright_red"
        elif beta_value > 1.0:
            return "red"
        elif beta_value > 0.5:
            return "yellow"
        else:
            return "green"
    
    def _print_summary_stats(self, beta_results: Dict[str, Dict]) -> None:
        """Print summary statistics."""
        if not beta_results:
            return
        
        betas = [data['beta'] for data in beta_results.values()]
        
        stats_table = Table(title="Beta Statistics", box=box.SIMPLE)
        stats_table.add_column("Metric", style="cyan")
        stats_table.add_column("Value", style="white", justify="right")
        
        stats_table.add_row("Total Assets", str(len(betas)))
        stats_table.add_row("Average Beta", f"{np.mean(betas):.3f}")
        stats_table.add_row("Median Beta", f"{np.median(betas):.3f}")
        stats_table.add_row("Min Beta", f"{np.min(betas):.3f}")
        stats_table.add_row("Max Beta", f"{np.max(betas):.3f}")
        stats_table.add_row("Std Dev", f"{np.std(betas):.3f}")
        
        # Count assets by beta ranges
        high_beta = sum(1 for b in betas if b > 1.5)
        medium_beta = sum(1 for b in betas if 1.0 < b <= 1.5)
        low_beta = sum(1 for b in betas if b <= 1.0)
        
        stats_table.add_row("High Beta (>1.5)", str(high_beta))
        stats_table.add_row("Medium Beta (1.0-1.5)", str(medium_beta))
        stats_table.add_row("Low Beta (≤1.0)", str(low_beta))
        
        self.console.print(stats_table)
    
    def save_beta_results(self, beta_results: Dict[str, Dict], 
                         filename: Optional[str] = None) -> str:
        """
        Save beta results to JSON file.
        
        Args:
            beta_results: Beta calculation results
            filename: Optional filename, if None uses timestamp
            
        Returns:
            Filename of saved file
        """
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"beta_results_{timestamp}.json"
        
        filepath = os.path.join("Beta_detection", filename)
        
        # Ensure directory exists
        os.makedirs("Beta_detection", exist_ok=True)
        
        # Add metadata
        output_data = {
            "timestamp": datetime.now().isoformat(),
            "benchmark": "BTC/USDT",
            "total_assets": len(beta_results),
            "results": beta_results
        }
        
        with open(filepath, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        logger.info(f"Beta results saved to {filepath}")
        return filepath
    
    def load_previous_betas(self, filename: str) -> Optional[Dict[str, float]]:
        """
        Load previous beta values from JSON file.
        
        Args:
            filename: Path to previous beta results file
            
        Returns:
            Dictionary of previous beta values or None if file not found
        """
        try:
            with open(filename, 'r') as f:
                data = json.load(f)
            
            previous_betas = {}
            for symbol, result in data.get('results', {}).items():
                if 'beta' in result:
                    previous_betas[symbol] = result['beta']
            
            logger.info(f"Loaded {len(previous_betas)} previous beta values from {filename}")
            return previous_betas
            
        except FileNotFoundError:
            logger.warning(f"Previous beta file {filename} not found")
            return None
        except Exception as e:
            logger.error(f"Error loading previous betas from {filename}: {e}")
            return None
