#!/usr/bin/env python3

import re

def fix_unicode_debug_messages():
    """Fix Unicode emoji debug messages in background_service.py and executor.py"""
    
    files_to_fix = [
        'background_service.py',
        'src/trading/executor.py',
        'src/scoring.py'
    ]
    
    for file_path in files_to_fix:
        try:
            print(f"Fixing {file_path}...")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Replace emoji debug messages
            replacements = [
                (r'🚨 BACKGROUND SERVICE DEBUG', '[DEBUG] BACKGROUND SERVICE'),
                (r'🚨 TRADING EXECUTOR DEBUG', '[DEBUG] TRADING EXECUTOR'),
                (r'🚨 STRATEGY RESULTS DEBUG', '[DEBUG] STRATEGY RESULTS'),
                (r'🚨 ASSET SELECTION DEBUG', '[DEBUG] ASSET SELECTION'),
                (r'🚨 TRADE DEBUG', '[DEBUG] TRADE'),
                (r'🚨 PRICE DEBUG', '[DEBUG] PRICE'),
                (r'🚨 FINAL ASSET SELECTION SUMMARY', '[DEBUG] FINAL ASSET SELECTION SUMMARY'),
                (r'🚨 TIE DETECTED', '[DEBUG] TIE DETECTED'),
                (r'🚨 BTC vs TRX TIE', '[DEBUG] BTC vs TRX TIE'),
                (r'🚨 SELECTED BEST ASSET', '[DEBUG] SELECTED BEST ASSET'),
                (r'🚨   -', '[DEBUG]   -'),
                (r'🚨 ❌', '[DEBUG] ERROR'),
                (r'🔍 ASSET SELECTION DEBUG', '[DEBUG] ASSET SELECTION'),
            ]
            
            original_content = content
            for pattern, replacement in replacements:
                content = re.sub(pattern, replacement, content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ Fixed Unicode issues in {file_path}")
            else:
                print(f"  ℹ️  No changes needed in {file_path}")
                
        except Exception as e:
            print(f"  ❌ Error fixing {file_path}: {e}")

if __name__ == "__main__":
    fix_unicode_debug_messages()
