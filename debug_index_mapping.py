#!/usr/bin/env python3
"""
Debug script to check index mapping between CSV and main program
"""
import pandas as pd
import ast

def debug_index_mapping():
    """Debug the index mapping between CSV rows and main program loop"""
    
    print("=== DEBUGGING INDEX MAPPING ===\n")
    
    # Read the CSV
    df = pd.read_csv('allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv')
    
    # Find key dates
    analysis_start = '2023-10-19'
    violation_date = '2024-01-06 00:00:00+00:00'
    
    # Find the start index in the CSV
    start_row = None
    violation_row = None
    
    for i, row in df.iterrows():
        date_str = pd.to_datetime(row['date']).strftime('%Y-%m-%d')
        if date_str == analysis_start:
            start_row = i
        if row['date'] == violation_date:
            violation_row = i
    
    print(f"Analysis start date: {analysis_start}")
    print(f"Start row in CSV: {start_row}")
    print(f"Violation date: {violation_date}")
    print(f"Violation row in CSV: {violation_row}")
    print()
    
    if start_row is not None and violation_row is not None:
        print(f"Days from start to violation: {violation_row - start_row}")
        print(f"Main program loop index for violation: {violation_row - start_row}")
        print()
        
        # Check the actual dates around the violation
        print("Dates around violation:")
        for i in range(max(0, violation_row-2), min(len(df), violation_row+3)):
            row = df.iloc[i]
            loop_idx = i - start_row if start_row is not None else i
            print(f"  CSV row {i}, loop index {loop_idx}: {row['date']}")
    
    # Check if there's a mismatch in how the main program processes dates
    print("\n--- Checking date processing ---")
    
    # Simulate how main program processes dates
    dates = pd.to_datetime(df['date'])
    print(f"First date in CSV: {dates.iloc[0]}")
    print(f"Last date in CSV: {dates.iloc[-1]}")
    print(f"Total dates: {len(dates)}")
    
    # Check for any gaps or irregularities
    date_diffs = dates.diff().dropna()
    unique_diffs = date_diffs.unique()
    print(f"Unique date differences: {unique_diffs}")
    
    if len(unique_diffs) > 1:
        print("⚠️  WARNING: Non-uniform date spacing detected!")
        for i, diff in enumerate(date_diffs):
            if diff != pd.Timedelta(days=1):
                print(f"  Gap at index {i+1}: {dates.iloc[i]} -> {dates.iloc[i+1]} ({diff})")

def check_csv_generation_date():
    """Check when the CSV was last generated"""
    
    print("\n=== CHECKING CSV GENERATION ===\n")
    
    import os
    csv_file = 'allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv'
    
    if os.path.exists(csv_file):
        mod_time = os.path.getmtime(csv_file)
        import datetime
        mod_date = datetime.datetime.fromtimestamp(mod_time)
        print(f"CSV file last modified: {mod_date}")
        
        # Check if it was modified recently (after our momentum fix)
        now = datetime.datetime.now()
        time_diff = now - mod_date
        print(f"Time since last modification: {time_diff}")
        
        if time_diff.total_seconds() < 3600:  # Less than 1 hour
            print("✓ CSV was generated recently (likely with our momentum fix)")
        else:
            print("⚠️  CSV might be old (generated before momentum fix)")
    else:
        print("❌ CSV file not found")

if __name__ == "__main__":
    debug_index_mapping()
    check_csv_generation_date()
