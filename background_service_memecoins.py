#!/usr/bin/env python3
"""
Memecoin Background Service

A specialized background service for running GeckoTerminal memecoin signal strategy.
This service focuses on signal generation and notifications only, without trading functionality.

Features:
- Signal-only strategy execution
- Separate Telegram bot for memecoin notifications
- Independent scheduling from Binance strategy
- Memecoin-specific risk alerts and performance tracking
- Complete isolation from trading operations

Author: Asset Rotation Strategy Team
Version: 1.0.0
"""

import os
import sys
import time
import signal
import logging
import schedule
import argparse
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Import configuration management
from src.config_manager import load_config, load_notification_config

# Import core strategy components
from main_program import run_strategy_for_web

# Import network watchdog and recovery manager
from src.network_watchdog import NetworkWatchdog
from src.recovery_manager import RecoveryManager

# Import notification modules
try:
    from src.notification.notification_manager import NotificationManager
    NOTIFICATIONS_AVAILABLE = True
except ImportError:
    NOTIFICATIONS_AVAILABLE = False
    logging.warning("Notification modules not available. Notifications will be disabled.")

# Import utility functions
from src.performance_tracker import ensure_metrics_directory

class MemecoinBackgroundService:
    """
    Background service for running the GeckoTerminal memecoin signal strategy continuously.

    This service focuses exclusively on signal generation and notifications for memecoins,
    without any trading functionality. It operates independently from the main Binance
    trading strategy.
    """

    def __init__(self, config_path=None, test_mode=False):
        """Initialize the memecoin background service."""
        print("\n" + "=" * 60)
        print("INITIALIZING MEMECOIN SIGNAL STRATEGY SERVICE")
        if test_mode:
            print("*** RUNNING IN TEST MODE ***")
        print("=" * 60)

        # Store test mode flag
        self.test_mode = test_mode

        # Load configuration
        print("Loading memecoin strategy configuration...")
        self.config = self._load_memecoin_config(config_path)
        self.notification_config = self._load_memecoin_notification_config()

        # Log key configuration settings
        settings = self.config.get('settings', {})
        timeframe = settings.get('timeframe', '1d')
        n_assets = settings.get('n_assets', 5)
        allocation_approach = settings.get('allocation_approach', 'equal')

        print(f"Strategy: {self.config.get('strategy', {}).get('name', 'Memecoin Signal Strategy')}")
        print(f"Timeframe: {timeframe}")
        print(f"Number of assets: {n_assets}")
        print(f"Allocation approach: {allocation_approach}")
        print(f"Signal-only mode: {self.config.get('execution', {}).get('signal_only', True)}")
        print(f"MTPI enabled: {settings.get('use_mtpi_signal', True)}")

        # Initialize components
        self.initialize_components()

        # State variables
        self.is_running = False
        self.scheduler_thread = None
        self.execution_count = 0
        self.last_execution_time = None
        self.last_mtpi_signal = None
        self.last_mtpi_score = None
        self.last_best_assets = []
        self.last_asset_scores = {}
        self.missed_executions = 0

        # Initialize network watchdog and recovery manager
        self.initialize_network_watchdog()
        self.initialize_recovery_manager()

        # Set up metrics directory
        metrics_config = self.config.get('metrics', {})
        self.metrics_dir = metrics_config.get('directory', 'Performance_Metrics/memecoins')
        ensure_metrics_directory(self.metrics_dir)
        logging.info(f"Ensured metrics directory exists: {self.metrics_dir}")
        print(f"Metrics will be saved to: {self.metrics_dir}")

        # Set up logging
        self._setup_logging()

        # Register signal handlers
        signal.signal(signal.SIGINT, self.handle_shutdown)
        signal.signal(signal.SIGTERM, self.handle_shutdown)

        logging.info("Memecoin background service initialized")
        print("Memecoin background service initialized successfully")

    def _load_memecoin_config(self, config_path=None):
        """Load memecoin-specific configuration."""
        if config_path is None:
            config_path = 'config/settings_memecoins.yaml'

        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Memecoin config file not found: {config_path}")

        return load_config(config_path)

    def _load_memecoin_notification_config(self):
        """Load memecoin-specific notification configuration."""
        config_path = 'config/notifications_memecoins.json'

        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Memecoin notification config file not found: {config_path}")

        return load_notification_config(config_path)

    def _setup_logging(self):
        """Set up logging for the memecoin service."""
        log_config = self.config.get('logging', {})
        log_file = log_config.get('file', 'logs/memecoin_strategy.log')
        log_level = log_config.get('level', 'INFO')

        # Ensure logs directory exists
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler() if log_config.get('console_output', True) else logging.NullHandler()
            ]
        )

        print(f"Logging configured: {log_file} (level: {log_level})")

    def initialize_components(self):
        """Initialize service components."""
        try:
            # Initialize notification manager
            if NOTIFICATIONS_AVAILABLE and self.notification_config.get('telegram', {}).get('enabled', False):
                # Modify notification config for test mode
                notification_config = self.notification_config.copy()
                if self.test_mode:
                    # Use test bot credentials
                    telegram_config = notification_config.get('telegram', {}).copy()
                    test_token = os.getenv('TELEGRAM_BOT_TOKEN_MEMECOINS_TEST')
                    test_chat_id = os.getenv('TELEGRAM_CHAT_ID_MEMECOINS_TEST')

                    if test_token and test_chat_id:
                        telegram_config['token'] = test_token
                        telegram_config['chat_id'] = test_chat_id
                        notification_config['telegram'] = telegram_config
                        logging.info("Using test Telegram bot credentials for memecoins")
                        print("Using test Telegram bot credentials for memecoins")
                    else:
                        logging.warning("Test mode enabled but test memecoin Telegram credentials not found")
                        print("WARNING: Test mode enabled but test memecoin Telegram credentials not found")
                        print("Please set TELEGRAM_BOT_TOKEN_MEMECOINS_TEST and TELEGRAM_CHAT_ID_MEMECOINS_TEST")
                        print("DISABLING NOTIFICATIONS IN TEST MODE to avoid conflicts")
                        # Disable notifications in test mode if credentials not found
                        self.notification_manager = None
                        return
                else:
                    # Use production credentials from environment
                    telegram_config = notification_config.get('telegram', {}).copy()
                    prod_token = os.getenv('TELEGRAM_BOT_TOKEN_MEMECOINS')
                    prod_chat_id = os.getenv('TELEGRAM_CHAT_ID_MEMECOINS')

                    if prod_token and prod_chat_id:
                        telegram_config['token'] = prod_token
                        telegram_config['chat_id'] = prod_chat_id
                        notification_config['telegram'] = telegram_config
                        logging.info("Using production Telegram bot credentials for memecoins")
                        print("Using production Telegram bot credentials for memecoins")
                    else:
                        logging.warning("Production memecoin Telegram credentials not found in environment")
                        print("WARNING: Production memecoin Telegram credentials not found")
                        print("Please set TELEGRAM_BOT_TOKEN_MEMECOINS and TELEGRAM_CHAT_ID_MEMECOINS")

                self.notification_manager = NotificationManager(notification_config)
                logging.info("Memecoin notification manager initialized")
                print("Memecoin notification manager initialized")
            else:
                self.notification_manager = None
                logging.warning("Telegram notifications disabled or not available for memecoins")
                print("WARNING: Telegram notifications disabled for memecoins")

        except Exception as e:
            logging.error(f"Error initializing components: {e}")
            print(f"ERROR: Failed to initialize components: {e}")
            raise

    def initialize_network_watchdog(self):
        """Initialize the network watchdog for memecoin service."""
        print("\nInitializing memecoin network watchdog...")

        # Define recovery callback
        def network_recovery_callback(downtime_seconds):
            logging.info(f"Memecoin network recovery callback triggered after {downtime_seconds:.1f} seconds downtime")
            print(f"\n🔄 MEMECOIN NETWORK RECOVERED after {downtime_seconds:.1f} seconds")

            # Check for missed executions
            self.check_missed_executions()

            # Send recovery notification
            if self.notification_manager:
                try:
                    # Get timeframe from config
                    timeframe = self.config.get('settings', {}).get('timeframe', '1d')

                    self.notification_manager.notify(
                        'network_status',
                        {
                            'status': '✅ RECONNECTED',
                            'downtime_str': f"{downtime_seconds:.1f} seconds",
                            'in_critical_window': 'Yes' if self.network_watchdog._is_in_critical_window(datetime.now(), timeframe) else 'No',
                            'missed_executions_str': f"{self.missed_executions} missed" if self.missed_executions > 0 else "None"
                        }
                    )
                    print("✓ Memecoin network recovery notification sent")
                except Exception as e:
                    print(f"✗ Failed to send memecoin network recovery notification: {e}")
                    logging.error(f"Failed to send memecoin network recovery notification: {e}")

            return True

        # Define state save callback
        def state_save_callback():
            try:
                # Save current state when network issues are detected
                state = {
                    'last_execution_time': self.last_execution_time.isoformat() if self.last_execution_time else None,
                    'last_mtpi_signal': self.last_mtpi_signal,
                    'last_mtpi_score': self.last_mtpi_score,
                    'last_best_assets': self.last_best_assets,
                    'last_asset_scores': self.last_asset_scores,
                    'execution_count': self.execution_count,
                    'missed_executions': self.missed_executions,
                    'service_type': 'memecoin'
                }

                # Save to memecoin-specific state file
                state_file = 'data/state/memecoin_service_state.json'
                os.makedirs(os.path.dirname(state_file), exist_ok=True)

                with open(state_file, 'w') as f:
                    import json
                    json.dump(state, f, indent=2)

                logging.info(f"Memecoin service state saved to {state_file}")
                print(f"✓ Memecoin service state saved")

                # Send network disconnection notification
                if self.notification_manager:
                    try:
                        # Get timeframe from config
                        timeframe = self.config.get('settings', {}).get('timeframe', '1d')

                        last_exec_str = "Never" if not self.last_execution_time else f"{(datetime.now() - self.last_execution_time).total_seconds():.0f}s ago"

                        self.notification_manager.notify(
                            'network_status',
                            {
                                'status': '❌ DISCONNECTED',
                                'downtime_str': last_exec_str,
                                'in_critical_window': 'Yes' if self.network_watchdog._is_in_critical_window(datetime.now(), timeframe) else 'No',
                                'missed_executions_str': ''
                            }
                        )
                        print("✓ Memecoin network disconnection notification sent")
                    except Exception as e:
                        print(f"✗ Failed to send memecoin network disconnection notification: {e}")
                        logging.error(f"Failed to send memecoin network disconnection notification: {e}")

                return True
            except Exception as e:
                logging.error(f"Error saving memecoin service state: {e}")
                print(f"✗ FAILED TO SAVE MEMECOIN STATE: {e}")
                return False

        # Create the network watchdog with memecoin-specific configuration
        timeframe = self.config.get('settings', {}).get('timeframe', '1d')
        check_interval = 60  # Default to 60 seconds for memecoin (less frequent than Binance)

        # Adjust check interval based on timeframe
        if timeframe == '1m':
            check_interval = 10  # Check every 10 seconds for 1m timeframe
        elif timeframe == '1h':
            check_interval = 30  # Check every 30 seconds for 1h timeframe

        self.network_watchdog = NetworkWatchdog(
            check_interval=check_interval,
            recovery_callback=network_recovery_callback,
            state_save_callback=state_save_callback,
            ping_targets=['8.8.8.8', '1.1.1.1'],
            http_targets=[
                'https://api.geckoterminal.com',  # GeckoTerminal API
                'https://www.google.com'
            ],
            max_failures=1  # Consider network down after 1 consecutive failure
        )

        logging.info("Memecoin network watchdog initialized")
        print("Memecoin network watchdog initialized")

    def initialize_recovery_manager(self):
        """Initialize the recovery manager for memecoin service."""
        print("\nInitializing memecoin recovery manager...")

        # Define recovery callbacks
        recovery_callbacks = {
            'missed_execution': self.recover_missed_execution,
            'network_failure': self.recover_from_network_failure,
            'data_fetch_failure': self.recover_from_data_fetch_failure,
            'strategy_execution_failure': self.recover_from_strategy_failure
        }

        # Create the recovery manager with memecoin-specific state directory
        self.recovery_manager = RecoveryManager(
            state_dir='data/state/memecoins',  # Separate from Binance
            checkpoint_interval=300,  # 5 minutes
            max_recovery_attempts=5,
            recovery_callbacks=recovery_callbacks
        )

        # Load previous state if available
        self.load_previous_state()

        logging.info("Memecoin recovery manager initialized")
        print("Memecoin recovery manager initialized")

    def start(self):
        """Start the memecoin background service."""
        if self.is_running:
            logging.warning("Service is already running")
            print("WARNING: Service is already running")
            return

        self.is_running = True
        logging.info("Starting memecoin background service...")

        print("\n" + "=" * 80)
        print("MEMECOIN SIGNAL STRATEGY SERVICE STARTED")
        print("=" * 80)
        print("This service provides:")
        print("✓ GeckoTerminal memecoin signal generation")
        print("✓ MTPI-based market timing signals")
        print("✓ Asset rotation recommendations")
        print("✓ Telegram notifications for memecoin signals")
        print("✓ Performance tracking and risk alerts")
        print("✓ Independent operation from Binance trading strategy")
        print("=" * 80)

        # Schedule strategy execution
        self._setup_schedule()

        # Send startup notification
        if self.notification_manager:
            settings = self.config.get('settings', {})
            mtpi_info = self._get_mtpi_info_for_notification(settings)
            self.notification_manager.notify(
                'service_status',
                {
                    'status': 'started',
                    'mode': 'signal-only',  # Required field for service_status template
                    'trading_enabled': 'false',  # Required field for service_status template
                    'timeframe': settings.get('timeframe', '1d'),
                    'assets': f"GeckoTerminal memecoins ({settings.get('n_assets', 5)} selected)",
                    'allocation_approach': settings.get('allocation_approach', 'equal'),
                    'mtpi_info': mtpi_info
                }
            )

        # Start network watchdog
        self.network_watchdog.start()
        print("✓ Memecoin network watchdog started")

        # Start scheduler in a separate thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        logging.info("Memecoin background service started")

    def stop(self):
        """Stop the memecoin background service."""
        if not self.is_running:
            logging.warning("Service is not running")
            return

        logging.info("Stopping memecoin background service...")
        print("\nStopping memecoin background service...")

        self.is_running = False

        # Stop network watchdog
        if hasattr(self, 'network_watchdog'):
            self.network_watchdog.stop()
            print("✓ Memecoin network watchdog stopped")

        # Send shutdown notification
        if self.notification_manager:
            settings = self.config.get('settings', {})
            mtpi_info = self._get_mtpi_info_for_notification(settings)
            self.notification_manager.notify(
                'service_status',
                {
                    'status': 'stopped',
                    'mode': 'signal-only',  # Required field for service_status template
                    'trading_enabled': 'false',  # Required field for service_status template
                    'timeframe': settings.get('timeframe', '1d'),
                    'assets': f"GeckoTerminal memecoins ({settings.get('n_assets', 5)} selected)",
                    'allocation_approach': settings.get('allocation_approach', 'equal'),
                    'mtpi_info': mtpi_info
                }
            )

        # Wait for scheduler thread to finish
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)

        logging.info("Memecoin background service stopped")
        print("Memecoin background service stopped")

    def handle_shutdown(self, signum, frame):
        """Handle shutdown signals."""
        logging.info(f"Received signal {signum}, shutting down...")
        print(f"\nReceived signal {signum}, shutting down...")
        self.stop()
        sys.exit(0)

    def _setup_schedule(self):
        """Set up the execution schedule for the memecoin strategy."""
        execution_config = self.config.get('execution', {})
        schedule_minute = execution_config.get('schedule_minute', 2)

        settings = self.config.get('settings', {})
        timeframe = settings.get('timeframe', '1d')

        # Clear any existing scheduled jobs
        schedule.clear()

        # Schedule based on timeframe
        if timeframe == '1d':
            # Run daily at 00:02 UTC (offset from Binance strategy)
            schedule.every().day.at(f"00:0{schedule_minute}").do(self.execute_strategy)
            print(f"Scheduled to run daily at 00:0{schedule_minute} UTC")
        elif timeframe == '4h':
            # Run every 4 hours at :02 minutes
            for hour in [0, 4, 8, 12, 16, 20]:
                schedule.every().day.at(f"{hour:02d}:0{schedule_minute}").do(self.execute_strategy)
            print(f"Scheduled to run every 4 hours at :0{schedule_minute} minutes")
        elif timeframe == '1h':
            # Run every hour at :02 minutes
            schedule.every().hour.at(f":0{schedule_minute}").do(self.execute_strategy)
            print(f"Scheduled to run every hour at :0{schedule_minute} minutes")
        elif timeframe == '1m':
            # For 1m timeframe, run every minute (offset by 2 seconds)
            schedule.every(1).minutes.do(self.execute_strategy)
            print("Scheduled to run every minute")
        else:
            # Default: run daily
            schedule.every().day.at(f"00:0{schedule_minute}").do(self.execute_strategy)
            print(f"Unknown timeframe '{timeframe}', defaulting to daily at 00:0{schedule_minute} UTC")

        # Schedule daily summary
        notification_settings = self.notification_config.get('schedule', {})
        daily_summary_time = notification_settings.get('daily_summary_time', '23:45')
        schedule.every().day.at(daily_summary_time).do(self.send_daily_summary)
        print(f"Scheduled daily summary at {daily_summary_time} UTC")

        # Schedule weekly report
        weekly_day = notification_settings.get('weekly_report_day', 'sunday')
        weekly_time = notification_settings.get('weekly_report_time', '20:00')
        getattr(schedule.every(), weekly_day).at(weekly_time).do(self.send_weekly_report)
        print(f"Scheduled weekly report on {weekly_day} at {weekly_time} UTC")

        logging.info(f"Schedule set up for {timeframe} timeframe with {schedule_minute} minute offset")

    def _run_scheduler(self):
        """Run the scheduler loop."""
        # Run immediately on start
        print("\nRunning memecoin strategy immediately on start...")
        self.execute_strategy()

        # Then run according to schedule
        print("\nMemecoin scheduler loop started - waiting for scheduled executions")

        # Get timeframe from config
        timeframe = self.config.get('settings', {}).get('timeframe', '1d')

        # Determine sleep time based on timeframe
        if timeframe == '1m':
            sleep_time = 1  # Check every second for 1m timeframe
        else:
            sleep_time = 60  # Check every minute for other timeframes

        print(f"Checking for scheduled tasks every {sleep_time} second(s)")

        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(sleep_time)
            except Exception as e:
                logging.error(f"Error in scheduler loop: {e}")
                print(f"ERROR in scheduler loop: {e}")
                time.sleep(60)  # Wait a minute before retrying

    def execute_strategy(self, is_recovery=False):
        """Execute the memecoin signal strategy."""
        if not self.is_running:
            return

        try:
            # Check network connectivity first
            if not self.network_watchdog.is_connected and not is_recovery:
                logging.warning("Network is down, skipping memecoin strategy execution")
                print("NETWORK IS DOWN, SKIPPING MEMECOIN STRATEGY EXECUTION")
                return False
            self.execution_count += 1
            execution_start_time = datetime.now()

            logging.info(f"Executing memecoin strategy (run #{self.execution_count})...")
            print("\n" + "=" * 60)
            print(f"EXECUTING MEMECOIN STRATEGY (RUN #{self.execution_count}) - {execution_start_time}")
            print("=" * 60)

            # Get GeckoTerminal assets from configuration
            geckoterminal_assets = self._get_geckoterminal_assets()
            if not geckoterminal_assets:
                logging.error("No GeckoTerminal assets configured")
                print("ERROR: No GeckoTerminal assets configured")
                return

            print(f"Analyzing {len(geckoterminal_assets)} GeckoTerminal assets...")

            # Log PGO parameters being used
            settings = self.config.get('settings', {})
            # MTPI PGO parameters
            mtpi_pgo_length = settings.get('mtpi_pgo_length', 35)
            mtpi_upper_threshold = settings.get('mtpi_upper_threshold', 1.1)
            mtpi_lower_threshold = settings.get('mtpi_lower_threshold', -0.58)
            # Asset trend PGO parameters
            pgo_length = settings.get('pgo_length', 35)
            pgo_upper_threshold = settings.get('pgo_upper_threshold', 1.1)
            pgo_lower_threshold = settings.get('pgo_lower_threshold', -0.58)
            print(f"Using MTPI PGO parameters: length={mtpi_pgo_length}, upper_threshold={mtpi_upper_threshold}, lower_threshold={mtpi_lower_threshold}")
            print(f"Using Asset PGO parameters: length={pgo_length}, upper_threshold={pgo_upper_threshold}, lower_threshold={pgo_lower_threshold}")

            # Run strategy analysis
            strategy_result = self._run_strategy_analysis(geckoterminal_assets)

            if not strategy_result:
                logging.error("Strategy analysis failed")
                print("ERROR: Strategy analysis failed")
                return

            # Extract signals and scores
            mtpi_signal = strategy_result.get('mtpi_signal')
            mtpi_score = strategy_result.get('mtpi_score')
            asset_scores = strategy_result.get('asset_scores', {})
            best_assets = strategy_result.get('best_assets', [])
            performance_metrics = strategy_result.get('performance_metrics', {})

            # Check for signal changes
            mtpi_signal_changed = self.last_mtpi_signal != mtpi_signal
            best_assets_changed = self.last_best_assets != best_assets
            is_first_run = self.execution_count == 1

            # Log current signals and changes with enhanced bearish warnings
            if mtpi_signal == -1:
                print("=" * 80)
                print("❌❌❌ BEARISH MTPI SIGNAL DETECTED ❌❌❌")
                print("🚨 CAUTION: STAY OUT OF MEMECOIN MARKET 🚨")
                print("🔴 HIGH RISK PERIOD - AVOID NEW INVESTMENTS 🔴")
                print("=" * 80)
                print(f"MTPI Signal: {mtpi_signal} (❌❌❌ BEARISH ❌❌❌)")
            else:
                print(f"MTPI Signal: {mtpi_signal} ({'BULLISH' if mtpi_signal == 1 else 'NEUTRAL'})")

            print(f"Previous MTPI Signal: {self.last_mtpi_signal}")
            print(f"MTPI Signal Changed: {mtpi_signal_changed}")
            print(f"Best Assets: {', '.join(best_assets[:5])}")
            print(f"Previous Best Assets: {', '.join(self.last_best_assets[:5]) if self.last_best_assets else 'None'}")
            print(f"Best Assets Changed: {best_assets_changed}")
            print(f"Is First Run: {is_first_run}")

            if mtpi_signal == -1:
                print("⚠️ NOTE: Asset rankings shown for informational purposes only during bearish conditions")
                print("=" * 80)

            # Send comprehensive strategy execution notifications (like Binance strategy)
            if self.notification_manager:
                # Send started notification first
                self._send_strategy_execution_started_notification()

                # Skip individual signal change notifications - only send comprehensive notifications

                # Send completed notification with full details
                self._send_strategy_execution_completed_notification(
                    mtpi_signal, best_assets, asset_scores,
                    mtpi_signal_changed, best_assets_changed, mtpi_score
                )

            # Update state
            self.last_execution_time = execution_start_time
            self.last_mtpi_signal = mtpi_signal
            self.last_mtpi_score = mtpi_score
            self.last_best_assets = best_assets
            self.last_asset_scores = asset_scores

            execution_time = (datetime.now() - execution_start_time).total_seconds()
            logging.info(f"Memecoin strategy execution completed in {execution_time:.2f} seconds")
            print(f"Strategy execution completed in {execution_time:.2f} seconds")

        except Exception as e:
            logging.error(f"Error executing memecoin strategy: {e}", exc_info=True)
            print(f"ERROR executing memecoin strategy: {e}")

            # Send error notification
            if self.notification_manager:
                self.notification_manager.notify(
                    'error',
                    {
                        'error_type': 'strategy_execution_error',
                        'error_message': str(e),
                        'execution_count': self.execution_count
                    }
                )

    def _get_geckoterminal_assets(self):
        """Get the list of GeckoTerminal assets to analyze from settings."""
        try:
            # Get assets directly from the memecoin settings configuration
            settings = self.config.get('settings', {})
            asset_list = settings.get('assets', [])

            if not asset_list:
                logging.error("No assets found in memecoin settings configuration")
                return []

            # Convert asset strings to the format expected by the strategy
            geckoterminal_assets = []
            for asset_string in asset_list:
                # Parse the asset string format: "gt:network:token_address:symbol"
                if asset_string.startswith('gt:'):
                    parts = asset_string.split(':')
                    if len(parts) >= 4:
                        network = parts[1]
                        token_address = parts[2]
                        symbol = parts[3]

                        # Create GeckoTerminal token dictionary format expected by AllocationTester
                        token_dict = {
                            'network': network,
                            'token_address': token_address,
                            'symbol': symbol
                        }
                        geckoterminal_assets.append(token_dict)
                        logging.debug(f"Added asset: {symbol} on {network}")
                    else:
                        logging.warning(f"Invalid asset format: {asset_string}")
                else:
                    logging.warning(f"Asset does not start with 'gt:': {asset_string}")

            logging.info(f"Loaded {len(geckoterminal_assets)} GeckoTerminal assets from settings")
            # Debug logging to see what we're actually passing
            for i, asset in enumerate(geckoterminal_assets[:3]):  # Log first 3 for debugging
                logging.info(f"Asset {i+1}: {asset['symbol']} on {asset['network']}")

            return geckoterminal_assets

        except Exception as e:
            logging.error(f"Error loading GeckoTerminal assets from settings: {e}")
            return []

    def _run_strategy_analysis(self, geckoterminal_assets):
        """Run the strategy analysis for GeckoTerminal assets."""
        try:
            settings = self.config.get('settings', {})

            # Load MTPI multi-indicator configuration from YAML
            mtpi_config = settings.get('mtpi_indicators', {})
            enabled_indicators = mtpi_config.get('enabled_indicators', ['pgo', 'bollinger_bands'])
            combination_method = mtpi_config.get('combination_method', 'consensus')
            long_threshold = mtpi_config.get('long_threshold', 0.1)
            short_threshold = mtpi_config.get('short_threshold', -0.1)

            logging.info(f"Memecoin MTPI Multi-Indicator Configuration:")
            logging.info(f"  - Enabled indicators: {enabled_indicators}")
            logging.info(f"  - Combination method: {combination_method}")
            logging.info(f"  - Long threshold: {long_threshold}")
            logging.info(f"  - Short threshold: {short_threshold}")

            print(f"MEMECOIN MTPI MULTI-INDICATOR SETUP:")
            print(f"  - Indicators: {', '.join(enabled_indicators)}")
            print(f"  - Method: {combination_method}")
            print(f"  - Thresholds: {long_threshold}/{short_threshold}")

            # Prepare arguments for strategy execution with multi-indicator MTPI
            strategy_args = {
                'timeframe': settings.get('timeframe', '1d'),
                'mtpi_timeframe': settings.get('mtpi_timeframe', '1d'),
                'analysis_start_date': settings.get('analysis_start_date', '2023-10-20'),
                'n_assets': settings.get('n_assets', 5),
                'transaction_fee_rate': settings.get('transaction_fee_rate', 0.001),
                'selected_assets': [],  # No Binance assets
                'use_cache': settings.get('use_cache', True),
                'initial_capital': settings.get('initial_capital', 10000),
                'use_mtpi_signal': settings.get('use_mtpi_signal', True),
                'use_weighted_allocation': settings.get('allocation_approach') == 'weighted',
                'weights': settings.get('weights', []),
                'trend_method': settings.get('trend_method', 'PGO For Loop'),
                # Multi-indicator MTPI parameters
                'mtpi_indicators': enabled_indicators,
                'mtpi_combination_method': combination_method,
                'mtpi_long_threshold': long_threshold,
                'mtpi_short_threshold': short_threshold,
                # Asset trend PGO parameters (for asset scoring, not MTPI)
                'pgo_length': settings.get('pgo_length', 35),
                'pgo_upper_threshold': settings.get('pgo_upper_threshold', 1.1),
                'pgo_lower_threshold': settings.get('pgo_lower_threshold', -0.58),
                'geckoterminal_tokens': geckoterminal_assets,
                'force_refresh_cache': settings.get('force_refresh_cache', False),
                # Ratio calculation method for asset scoring
                'ratio_calculation': settings.get('ratio_calculation', 'manual_inversion'),
                # Tie-breaking strategy for asset selection
                'tie_breaking_strategy': settings.get('tie_breaking_strategy', 'momentum')
            }

            # Add context parameter for rate limiting
            strategy_args['context'] = 'live_service'  # Enable rate limiting for background service

            # Run the strategy
            result = run_strategy_for_web(**strategy_args)

            if not result:
                logging.error("Strategy execution returned no results")
                return None

            # Log the full result structure for debugging
            logging.info(f"Strategy result keys: {list(result.keys()) if result else 'None'}")
            logging.info(f"Strategy result success: {result.get('success', 'Not set') if result else 'None'}")

            # Extract relevant information
            performance_metrics = result.get('performance_metrics', {})
            asset_scores = performance_metrics.get('latest_scores', {})

            logging.info(f"Performance metrics keys: {list(performance_metrics.keys()) if performance_metrics else 'None'}")
            logging.info(f"Asset scores: {asset_scores}")

            # Get best assets by sorting scores
            best_assets = []
            if asset_scores:
                sorted_assets = sorted(asset_scores.items(), key=lambda x: x[1], reverse=True)
                best_assets = [asset for asset, _ in sorted_assets]
                logging.info(f"Best assets (sorted by score): {best_assets}")
            else:
                logging.warning("No asset scores found in performance metrics")

            # Extract MTPI signal from the latest signals
            mtpi_signals = result.get('mtpi_signals')
            current_mtpi_signal = None

            logging.info(f"MTPI signals type: {type(mtpi_signals)}")
            logging.info(f"MTPI signals length: {len(mtpi_signals) if mtpi_signals is not None else 'None'}")

            if mtpi_signals is not None and len(mtpi_signals) > 0:
                if hasattr(mtpi_signals, 'iloc'):
                    current_mtpi_signal = mtpi_signals.iloc[-1]
                    logging.info(f"Extracted MTPI signal from pandas series: {current_mtpi_signal}")
                else:
                    # Handle case where mtpi_signals might be a list or other type
                    current_mtpi_signal = mtpi_signals[-1]
                    logging.info(f"Extracted MTPI signal from list/array: {current_mtpi_signal}")
            else:
                logging.warning("No MTPI signals found or signals are empty")

            # Extract MTPI score from the result
            current_mtpi_score = result.get('mtpi_score')
            logging.info(f"Extracted MTPI score from result: {current_mtpi_score}")

            strategy_result = {
                'mtpi_signal': current_mtpi_signal,
                'mtpi_score': current_mtpi_score,
                'asset_scores': asset_scores,
                'best_assets': best_assets,
                'performance_metrics': performance_metrics,
                'allocation_data': result.get('allocation_data', {}),
                'execution_time': result.get('execution_time', 0)
            }

            logging.info(f"Final strategy result - MTPI signal: {current_mtpi_signal}, Best assets: {best_assets}")

            return strategy_result

        except Exception as e:
            logging.error(f"Error running strategy analysis: {e}", exc_info=True)
            return None

    def _get_mtpi_info_for_notification(self, settings=None, mtpi_signal=None, mtpi_score=None):
        """Get MTPI information for notifications."""
        if settings is None:
            settings = self.config.get('settings', {})

        use_mtpi_signal = settings.get('use_mtpi_signal', True)
        if not use_mtpi_signal:
            return "MTPI: Disabled\n"

        # Get MTPI configuration
        mtpi_config = settings.get('mtpi_indicators', {})
        enabled_indicators = mtpi_config.get('enabled_indicators', [])
        combination_method = mtpi_config.get('combination_method', 'consensus')
        long_threshold = mtpi_config.get('long_threshold', 0.1)
        short_threshold = mtpi_config.get('short_threshold', -0.1)

        # Format the MTPI info
        num_indicators = len(enabled_indicators)
        if num_indicators > 0:
            # Get current signal if available
            signal_text = ""
            score_text = ""

            # Use passed mtpi_signal parameter first, then fall back to last_mtpi_signal
            current_signal = mtpi_signal if mtpi_signal is not None else (self.last_mtpi_signal if hasattr(self, 'last_mtpi_signal') else None)

            if current_signal is not None:
                if current_signal == 1:
                    signal_text = " (🟢 BULLISH)"
                elif current_signal == -1:
                    signal_text = " (🔴 BEARISH)"
                else:
                    signal_text = " (⚪ NEUTRAL)"

            # Add score information if available
            if mtpi_score is not None:
                score_text = f", score: {mtpi_score:.3f}"
            elif hasattr(self, 'last_mtpi_score') and self.last_mtpi_score is not None:
                score_text = f", score: {self.last_mtpi_score:.3f}"

            return f"MTPI: {num_indicators} indicators, {combination_method} method{signal_text}{score_text}\n"
        else:
            return "MTPI: No indicators configured\n"

    def _send_mtpi_signal_notification(self, current_signal, previous_signal):
        """Send notification for MTPI signal change."""
        try:
            signal_text = {1: 'BULLISH', -1: 'BEARISH', 0: 'NEUTRAL'}

            # Use special bearish template if signal is bearish
            if current_signal == -1:
                template_name = 'mtpi_signal_change_bearish'
                signal_display = f"BEARISH ❌❌❌"
            else:
                template_name = 'mtpi_signal_change'
                signal_display = signal_text.get(current_signal, 'UNKNOWN')

            # Get MTPI info for the notification
            mtpi_info = self._get_mtpi_info_for_notification(mtpi_score=self.last_mtpi_score if hasattr(self, 'last_mtpi_score') else None)

            self.notification_manager.notify(
                template_name,
                {
                    'signal': signal_display,
                    'previous_signal': signal_text.get(previous_signal, 'UNKNOWN') if previous_signal is not None else 'NONE',
                    'strategy_type': 'memecoin',
                    'mtpi_info': mtpi_info
                }
            )

            logging.info(f"Sent MTPI signal change notification: {previous_signal} -> {current_signal}")

        except Exception as e:
            logging.error(f"Error sending MTPI signal notification: {e}")

    def _send_asset_rotation_notification(self, best_assets, asset_scores):
        """Send notification for asset rotation."""
        try:
            # Get current and previous best assets for the template
            new_asset = best_assets[0] if best_assets else 'N/A'
            previous_asset = self.last_best_assets[0] if self.last_best_assets else 'N/A'

            self.notification_manager.notify(
                'asset_rotation',
                {
                    'new_asset': new_asset,
                    'previous_asset': previous_asset,
                    'strategy_type': 'memecoin'
                }
            )

            logging.info(f"Sent asset rotation notification: {previous_asset} -> {new_asset}")

        except Exception as e:
            logging.error(f"Error sending asset rotation notification: {e}")

    def _send_performance_update(self, performance_metrics, asset_scores):
        """Send performance update notification."""
        try:
            # Extract key performance metrics
            total_return = performance_metrics.get('total_return', 0)
            sharpe_ratio = performance_metrics.get('sharpe_ratio', 0)
            max_drawdown = performance_metrics.get('max_drawdown', 0)

            # Get top performing assets
            sorted_assets = sorted(asset_scores.items(), key=lambda x: x[1], reverse=True)
            top_assets = [asset for asset, _ in sorted_assets[:3]]

            self.notification_manager.notify(
                'performance_update',
                {
                    'strategy_type': 'memecoin',
                    'total_return': total_return,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'top_assets': top_assets,
                    'execution_count': self.execution_count
                }
            )

            logging.info("Sent performance update notification")

        except Exception as e:
            logging.error(f"Error sending performance update: {e}")

    def _get_allocation_approach_description(self, allocation_approach, n_assets, settings):
        """Get a human-readable description of the allocation approach."""
        if allocation_approach == 'weighted':
            weights = settings.get('weights', [])
            if weights and len(weights) >= n_assets:
                # Format weights as percentages
                weight_percentages = [f"{w*100:.0f}" for w in weights[:n_assets]]
                if n_assets == 2:
                    return f"Weighted allocation ({'-'.join(weight_percentages)} split for top {n_assets} assets)"
                else:
                    return f"Weighted allocation ({'-'.join(weight_percentages)} split for top {n_assets} assets)"
            else:
                return f"Weighted allocation (top {n_assets} assets)"
        else:
            if n_assets == 1:
                return "Single asset selection"
            else:
                return f"Equal allocation ({n_assets} assets)"

    def _send_strategy_execution_started_notification(self):
        """Send strategy execution started notification."""
        try:
            settings = self.config.get('settings', {})
            allocation_approach_desc = self._get_allocation_approach_description(
                settings.get('allocation_approach', 'equal'),
                settings.get('n_assets', 1),
                settings
            )

            # Get previous state for display (from last execution)
            # Only show previous values if we have them, otherwise show current state without "(previous)"
            if self.last_best_assets and self.last_mtpi_signal is not None:
                previous_best_asset = self.last_best_assets[0]
                previous_mtpi_signal = 'bullish' if self.last_mtpi_signal == 1 else 'bearish' if self.last_mtpi_signal == -1 else 'neutral'
                best_asset_display = f"{previous_best_asset} (previous)"
                mtpi_signal_display = f"{previous_mtpi_signal} (previous)"
            else:
                # First run - no previous data available
                best_asset_display = "Starting analysis..."
                mtpi_signal_display = "Calculating..."

            # Get MTPI information for notification (use previous values if available)
            mtpi_info = self._get_mtpi_info_for_notification(
                settings,
                mtpi_signal=self.last_mtpi_signal,
                mtpi_score=self.last_mtpi_score
            )

            self.notification_manager.notify(
                'strategy_execution',
                {
                    'status': 'started',
                    'best_asset': best_asset_display,
                    'mtpi_info': mtpi_info,
                    'allocation_approach': allocation_approach_desc,
                    'market_warning': '',     # Empty for start notification
                    'assets_traded_str': '',  # Empty for start notification
                    'asset_scores_str': ''    # Empty for start notification
                }
            )

            logging.info("Sent strategy execution started notification")

        except Exception as e:
            logging.error(f"Error sending strategy execution started notification: {e}")

    def _send_strategy_execution_completed_notification(self, mtpi_signal, best_assets, asset_scores,
                                                       mtpi_signal_changed, best_assets_changed, mtpi_score):
        """Send comprehensive strategy execution completed notification (like Binance strategy)."""
        try:
            settings = self.config.get('settings', {})
            n_assets = settings.get('n_assets', 1)
            allocation_approach = settings.get('allocation_approach', 'equal')

            # Get allocation approach description
            allocation_approach_desc = self._get_allocation_approach_description(allocation_approach, n_assets, settings)

            # Format best asset display
            best_asset_display = best_assets[0] if best_assets else 'N/A'

            # Check if this is the first run (no previous data to compare against)
            is_first_run = self.last_mtpi_signal is None and not self.last_best_assets

            # Format MTPI signal with enhanced bearish warnings
            if mtpi_signal == -1:
                # Bearish signal - use enhanced formatting with red X's
                mtpi_signal_text = 'BEARISH ❌❌❌'
                if mtpi_signal_changed and not is_first_run:
                    mtpi_display = f"{mtpi_signal_text} (signal changed) ❌❌❌"
                else:
                    mtpi_display = mtpi_signal_text
            else:
                # Normal formatting for bullish/neutral
                mtpi_signal_text = 'bullish' if mtpi_signal == 1 else 'neutral'
                if mtpi_signal_changed and not is_first_run:
                    mtpi_display = f"{mtpi_signal_text} (signal changed)"
                else:
                    mtpi_display = mtpi_signal_text

            # Format best asset with change status (only show "changed" if actually changed and not first run)
            if best_assets_changed and not is_first_run:
                best_asset_status = " (changed)"
            else:
                best_asset_status = ""

            if n_assets == 1:
                best_asset_status += " (highest score)"

            # Determine which template to use based on MTPI signal
            template_name = 'strategy_execution_bearish' if mtpi_signal == -1 else 'strategy_execution'

            # Get MTPI information for notification
            settings = self.config.get('settings', {})
            mtpi_info = self._get_mtpi_info_for_notification(
                settings,
                mtpi_signal=mtpi_signal,
                mtpi_score=mtpi_score
            )

            notification_data = {
                'status': 'completed',
                'best_asset': f"{best_asset_display}{best_asset_status}",
                'mtpi_info': mtpi_info,
                'mtpi_signal': mtpi_signal_text,  # For bearish template
                'allocation_approach': allocation_approach_desc,
                'market_warning': ""  # Will be populated by template if needed
            }

            # Format asset scores for display (like Binance strategy)
            asset_scores_str = ""
            if asset_scores:
                # Sort assets by score in descending order
                sorted_assets = sorted(asset_scores.items(), key=lambda x: x[1], reverse=True)

                # Format the scores with ranking
                score_lines = []
                for i, (asset, score) in enumerate(sorted_assets):
                    # Add ranking position (1-based)
                    rank = i + 1
                    # Check if this asset is in the selected best assets
                    is_selected = asset in best_assets[:n_assets]
                    # Format with emoji indicator if selected
                    indicator = "✅ " if is_selected else ""
                    # Add the score line
                    score_lines.append(f"{indicator}{rank}. {asset}: {int(score)}")

                # Join the score lines and add a header
                asset_scores_str = "Asset scores:\n" + "\n".join(score_lines) + "\n\n"
                notification_data['asset_scores_str'] = asset_scores_str
            else:
                notification_data['asset_scores_str'] = ""

            # No "Assets analyzed" section for memecoin strategy (signal-only)
            notification_data['assets_traded_str'] = ""

            # Send the comprehensive notification using appropriate template
            self.notification_manager.notify(template_name, notification_data)

            logging.info("Sent strategy execution completed notification")

        except Exception as e:
            logging.error(f"Error sending strategy execution completed notification: {e}")

    def load_previous_state(self):
        """Load previous state if available."""
        try:
            state_file = 'data/state/memecoin_service_state.json'
            if os.path.exists(state_file):
                with open(state_file, 'r') as f:
                    import json
                    state = json.load(f)

                # Restore state
                if state.get('last_execution_time'):
                    self.last_execution_time = datetime.fromisoformat(state['last_execution_time'])
                self.last_mtpi_signal = state.get('last_mtpi_signal')
                self.last_mtpi_score = state.get('last_mtpi_score')
                self.last_best_assets = state.get('last_best_assets', [])
                self.last_asset_scores = state.get('last_asset_scores', {})
                self.execution_count = state.get('execution_count', 0)
                self.missed_executions = state.get('missed_executions', 0)

                logging.info(f"Loaded previous memecoin service state from {state_file}")
                print(f"✓ Loaded previous memecoin service state")
            else:
                logging.info("No previous memecoin service state found")
                print("No previous memecoin service state found")
        except Exception as e:
            logging.error(f"Error loading previous memecoin service state: {e}")
            print(f"✗ Error loading previous memecoin service state: {e}")

    def check_missed_executions(self):
        """Check for missed executions during network downtime."""
        try:
            if not self.last_execution_time:
                logging.info("No previous execution time, cannot check for missed executions")
                return

            now = datetime.now()
            time_since_last = (now - self.last_execution_time).total_seconds()

            # Get execution interval based on timeframe
            timeframe = self.config.get('settings', {}).get('timeframe', '1d')
            if timeframe == '1d':
                expected_interval = 24 * 3600  # 24 hours
            elif timeframe == '4h':
                expected_interval = 4 * 3600   # 4 hours
            elif timeframe == '1h':
                expected_interval = 3600       # 1 hour
            elif timeframe == '1m':
                expected_interval = 60         # 1 minute
            else:
                expected_interval = 24 * 3600  # Default to daily

            # Calculate how many executions we might have missed
            missed_count = max(0, int(time_since_last / expected_interval) - 1)

            if missed_count > 0:
                self.missed_executions += missed_count
                logging.warning(f"Detected {missed_count} missed memecoin strategy executions")
                print(f"⚠️  DETECTED {missed_count} MISSED MEMECOIN STRATEGY EXECUTIONS")

                # Execute strategy immediately to catch up
                logging.info("Executing memecoin strategy immediately to catch up")
                print("🔄 Executing memecoin strategy immediately to catch up...")
                self.execute_strategy(is_recovery=True)
            else:
                logging.info("No missed memecoin strategy executions detected")
                print("✓ No missed memecoin strategy executions detected")

        except Exception as e:
            logging.error(f"Error checking for missed memecoin executions: {e}")
            print(f"✗ Error checking for missed memecoin executions: {e}")

    def recover_missed_execution(self, details):
        """Recover from a missed execution."""
        logging.info(f"Recovering from missed memecoin execution: {details}")
        print(f"\nRECOVERING FROM MISSED MEMECOIN EXECUTION: {details}")

        # Execute strategy immediately
        return self.execute_strategy(is_recovery=True)

    def recover_from_network_failure(self, details):
        """Recover from a network failure."""
        logging.info(f"Recovering from memecoin network failure: {details}")
        print(f"\nRECOVERING FROM MEMECOIN NETWORK FAILURE: {details}")

        # Check if we're connected now
        if not self.network_watchdog.is_connected:
            logging.warning("Still not connected to network, cannot recover yet")
            print("STILL NOT CONNECTED TO NETWORK, CANNOT RECOVER YET")
            return False

        # Check for missed executions
        self.check_missed_executions()

        return True

    def recover_from_data_fetch_failure(self, details):
        """Recover from a data fetch failure."""
        logging.info(f"Recovering from memecoin data fetch failure: {details}")
        print(f"\nRECOVERING FROM MEMECOIN DATA FETCH FAILURE: {details}")

        # Try to execute strategy again
        return self.execute_strategy(is_recovery=True)

    def recover_from_strategy_failure(self, details):
        """Recover from a strategy execution failure."""
        logging.info(f"Recovering from memecoin strategy failure: {details}")
        print(f"\nRECOVERING FROM MEMECOIN STRATEGY FAILURE: {details}")

        # Try to execute strategy again
        return self.execute_strategy(is_recovery=True)

    def send_daily_summary(self):
        """Send daily summary notification."""
        try:
            if not self.notification_manager:
                return

            # Prepare summary data with enhanced bearish formatting
            if self.last_mtpi_signal == -1:
                mtpi_signal_display = '❌❌❌ BEARISH ❌❌❌'
            else:
                mtpi_signal_display = {1: 'BULLISH', -1: 'BEARISH', 0: 'NEUTRAL'}.get(self.last_mtpi_signal, 'UNKNOWN')

            summary_data = {
                'strategy_type': 'memecoin',
                'execution_count': self.execution_count,
                'last_execution': self.last_execution_time.isoformat() if self.last_execution_time else None,
                'current_mtpi_signal': mtpi_signal_display,
                'top_assets': [asset for asset in self.last_best_assets[:3]],
                'total_assets_tracked': len(self.last_asset_scores)
            }

            # Use bearish template if signal is bearish
            template_name = 'daily_summary_bearish' if self.last_mtpi_signal == -1 else 'daily_summary'
            self.notification_manager.notify(template_name, summary_data)
            logging.info("Sent daily summary notification")

        except Exception as e:
            logging.error(f"Error sending daily summary: {e}")

    def send_weekly_report(self):
        """Send weekly report notification."""
        try:
            if not self.notification_manager:
                return

            # Prepare weekly report data with enhanced bearish formatting
            if self.last_mtpi_signal == -1:
                mtpi_signal_display = '❌❌❌ BEARISH ❌❌❌'
            else:
                mtpi_signal_display = {1: 'BULLISH', -1: 'BEARISH', 0: 'NEUTRAL'}.get(self.last_mtpi_signal, 'UNKNOWN')

            report_data = {
                'strategy_type': 'memecoin',
                'week_executions': self.execution_count,  # Simplified for now
                'current_mtpi_signal': mtpi_signal_display,
                'top_performers': [asset for asset in self.last_best_assets[:5]]
            }

            self.notification_manager.notify('weekly_report', report_data)
            logging.info("Sent weekly report notification")

        except Exception as e:
            logging.error(f"Error sending weekly report: {e}")


def main():
    """Main function to run the memecoin background service."""
    parser = argparse.ArgumentParser(description='Memecoin Signal Strategy Background Service')
    parser.add_argument('--config', type=str, help='Path to configuration file')
    parser.add_argument('--test', action='store_true', help='Run in test mode with test Telegram bot')
    parser.add_argument('--log-level', type=str, default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='Set the logging level')

    args = parser.parse_args()

    # Set up logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level.upper()))

    print("=" * 80)
    print("MEMECOIN SIGNAL STRATEGY BACKGROUND SERVICE")
    print("=" * 80)
    print(f"Log level set to: {args.log_level}")
    print(f"Logs will be saved to: logs/memecoin_strategy.log")
    if args.config:
        print(f"Using config file: {args.config}")
    else:
        print("Using default config file: config/settings_memecoins.yaml")
    if args.test:
        print("*** RUNNING IN TEST MODE - Using test Telegram bot ***")

    # Create and start the service
    try:
        service = MemecoinBackgroundService(args.config, test_mode=args.test)
        service.start()

        # Keep the main thread alive
        print("\nMemecoin service is running. Press Ctrl+C to stop.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\nKeyboard interrupt received, shutting down...")
        logging.info("Keyboard interrupt received, shutting down...")
        if 'service' in locals():
            service.stop()
    except Exception as e:
        print(f"\nError in main thread: {e}")
        logging.error(f"Error in main thread: {e}", exc_info=True)
        if 'service' in locals():
            service.stop()


if __name__ == "__main__":
    main()
