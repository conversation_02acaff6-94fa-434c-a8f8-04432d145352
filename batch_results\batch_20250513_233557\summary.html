<html><head><title>Batch Strategy Runner Summary</title><style>body { font-family: Arial, sans-serif; margin: 20px; }table { border-collapse: collapse; width: 100%; }th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }th { background-color: #f2f2f2; }tr:nth-child(even) { background-color: #f9f9f9; }tr:hover { background-color: #f5f5f5; }.success { color: green; }.failure { color: red; }</style></head><body><h1>Batch Strategy Runner Summary</h1><p>Batch ID: 20250513_233557</p><p>Total Duration: 55s</p><p>Total Configurations: 4</p><p>Successful Configurations: 4</p><h2>Configuration Summary</h2><table><tr><th>Config Name</th><th>Timeframe</th><th>MTPI Timeframe</th><th>N Assets</th><th>Weighted</th><th>Use MTPI</th><th>Trend Method</th><th>Success</th><th>Duration (s)</th><th>Output Path</th></tr><tr><td>1d_1d_n1_equal_PGO_For_Loop</td><td>1d</td><td>1d</td><td>1</td><td>False</td><td>True</td><td>PGO For Loop</td><td class='success'>True</td><td>10s</td><td>None</td></tr><tr><td>1d_1d_n3_equal_PGO_For_Loop</td><td>1d</td><td>1d</td><td>3</td><td>False</td><td>True</td><td>PGO For Loop</td><td class='success'>True</td><td>10s</td><td>None</td></tr><tr><td>1d_1d_n3_weighted_70-20-10_PGO_For_Loop</td><td>1d</td><td>1d</td><td>3</td><td>True</td><td>True</td><td>PGO For Loop</td><td class='success'>True</td><td>10s</td><td>None</td></tr><tr><td>1d_1d_n3_equal_PGO_For_Loop_with_AUTISM</td><td>1d</td><td>1d</td><td>3</td><td>False</td><td>True</td><td>PGO For Loop</td><td class='success'>True</td><td>14s</td><td>None</td></tr></table></body></html>