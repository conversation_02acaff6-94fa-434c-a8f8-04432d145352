2025-06-26 18:37:42,754 - root - INFO - Loaded environment variables from .env file
2025-06-26 18:37:43,510 - root - INFO - Loaded 59 trade records from logs/trades\trade_log_********.json
2025-06-26 18:37:43,511 - root - INFO - Loaded 34 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 18:37:43,511 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 18:37:44,530 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:44,532 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:44,542 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:44,550 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:44,550 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:44,557 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:44,557 - root - INFO - Loading notification configuration from config/notifications_kraken.json...
2025-06-26 18:37:44,557 - root - INFO - Notification configuration loaded successfully.
2025-06-26 18:37:45,500 - root - INFO - Telegram command handlers registered
2025-06-26 18:37:45,500 - root - INFO - Telegram bot polling started
2025-06-26 18:37:45,500 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 18:37:45,500 - root - INFO - Telegram notification channel initialized
2025-06-26 18:37:45,500 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 18:37:45,500 - root - INFO - Loaded 24 templates from file
2025-06-26 18:37:45,500 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 18:37:45,500 - root - INFO - Notification manager initialized
2025-06-26 18:37:45,500 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 18:37:45,500 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 18:37:45,500 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 18:37:45,504 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 18:37:45,510 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 18:37:45,510 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 18:37:45,510 - root - INFO - Recovery manager initialized
2025-06-26 18:37:45,510 - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: kraken
2025-06-26 18:37:45,510 - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'kraken', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'AAVE/EUR': 5.0, 'ADA/EUR': 2.0, 'AVAX/EUR': 4.0, 'BNB/EUR': 3.0, 'BTC/EUR': 3.5, 'DOGE/EUR': 5.0, 'DOT/EUR': 4.5, 'ETH/EUR': 7.5, 'LINK/EUR': 3.5, 'PEPE/EUR': 5.0, 'SOL/EUR': 4.5, 'SUI/EUR': 2.0, 'TRX/EUR': 3.0, 'XRP/EUR': 5.0, 'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.004}
2025-06-26 18:37:45,512 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:45,520 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:45,520 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:37:45,521 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:37:45,521 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:37:45,521 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:37:45,521 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:37:45,522 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:37:45,522 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:37:45,522 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:37:45,522 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:45,530 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:45,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getMe "HTTP/1.1 200 OK"
2025-06-26 18:37:45,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 18:37:45,574 - telegram.ext.Application - INFO - Application started
2025-06-26 18:37:46,822 - root - INFO - Successfully loaded markets for kraken.
2025-06-26 18:37:46,822 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:37:46,822 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:37:46,822 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:37:46,822 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:37:46,822 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:46,840 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:46,840 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:46,850 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:46,850 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:37:46,860 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:46,860 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 18:37:46,860 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 18:37:46,860 - root - INFO - Trading executor initialized for kraken
2025-06-26 18:37:46,860 - root - INFO - Trading mode: paper
2025-06-26 18:37:46,860 - root - INFO - Trading enabled: True
2025-06-26 18:37:46,860 - root - INFO - Getting credentials for exchange_id: kraken
2025-06-26 18:37:46,860 - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-06-26 18:37:46,860 - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-06-26 18:37:46,860 - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-06-26 18:37:46,860 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:46,872 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:48,050 - root - INFO - Successfully loaded markets for kraken.
2025-06-26 18:37:48,053 - root - INFO - Trading enabled in paper mode
2025-06-26 18:37:48,054 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:37:48,054 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 18:37:48,054 - root - INFO - Reset paper trading account to initial balance
2025-06-26 18:37:48,055 - root - INFO - Generated run ID: ********_183748
2025-06-26 18:37:48,055 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 18:37:48,056 - root - INFO - Background service initialized
2025-06-26 18:37:48,057 - root - INFO - Network watchdog started
2025-06-26 18:37:48,057 - root - INFO - Network watchdog started
2025-06-26 18:37:48,059 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 18:37:48,060 - root - INFO - Background service started
2025-06-26 18:37:48,061 - root - INFO - Executing strategy (run #1)...
2025-06-26 18:37:48,061 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 18:37:48,061 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 18:37:48,062 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 18:37:48,063 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 18:37:48,130 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:37:54,073 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 18:37:54,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:37:54,154 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 18:37:54,154 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 18:37:54,154 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 18:37:54,156 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 18:37:54,185 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,186 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,187 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,187 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 18:37:54,188 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,201 - root - INFO - Loaded 2137 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,201 - root - INFO - Last timestamp in cache for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,202 - root - INFO - Expected last timestamp for BTC/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,202 - root - INFO - Data is up to date for BTC/USDT
2025-06-26 18:37:54,203 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,215 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,215 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,215 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,215 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 18:37:54,217 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,224 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,224 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,225 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,225 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 18:37:54,225 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,237 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,237 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,238 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,238 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 18:37:54,239 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,252 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,252 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,253 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,253 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 18:37:54,255 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,270 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,271 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,272 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,272 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 18:37:54,273 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,292 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,293 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,294 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,294 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 18:37:54,295 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,318 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,318 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,318 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,319 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 18:37:54,319 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,337 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,337 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,339 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,339 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 18:37:54,340 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,351 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,351 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,351 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,352 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 18:37:54,352 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,373 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,373 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,374 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,374 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 18:37:54,374 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,391 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,391 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,392 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,392 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 18:37:54,393 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,407 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,408 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,408 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,408 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 18:37:54,409 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,410 - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-06-26 18:37:54,410 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 18:37:54,410 - root - INFO -   - Number of indicators: 8
2025-06-26 18:37:54,410 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:37:54,410 - root - INFO -   - Combination method: consensus
2025-06-26 18:37:54,411 - root - INFO -   - Long threshold: 0.1
2025-06-26 18:37:54,411 - root - INFO -   - Short threshold: -0.1
2025-06-26 18:37:54,411 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:37:54,411 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:37:54,411 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-06-26 18:37:54,411 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-06-26 18:37:54,411 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 18:37:54,412 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 18:37:54,412 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 18:37:54,412 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 18:37:54,412 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 18:37:54,412 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 18:37:54,412 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 18:37:54,412 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:54,420 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:54,420 - root - INFO - Saving configuration to config/settings_kraken_eur.yaml...
2025-06-26 18:37:54,425 - root - INFO - Configuration saved successfully.
2025-06-26 18:37:54,425 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:37:54,425 - root - INFO - Number of trend detection assets: 14
2025-06-26 18:37:54,425 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 18:37:54,425 - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:37:54,425 - root - INFO - Number of trading assets: 14
2025-06-26 18:37:54,425 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 18:37:54,610 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:37:54,618 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:54,625 - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-06-26 18:37:54,634 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:54,634 - root - INFO - Execution context: backtesting
2025-06-26 18:37:54,634 - root - INFO - Execution timing: candle_close
2025-06-26 18:37:54,634 - root - INFO - Ratio calculation method: independent
2025-06-26 18:37:54,634 - root - INFO - Tie-breaking strategy: momentum
2025-06-26 18:37:54,634 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 18:37:54,634 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:37:54,635 - root - INFO - MTPI combination method override: consensus
2025-06-26 18:37:54,635 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 18:37:54,635 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 18:37:54,635 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 18:37:54,635 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:37:54,636 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,636 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,637 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,637 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,637 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,637 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,638 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,638 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,638 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,639 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,639 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,639 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,639 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,639 - root - INFO - Loaded metadata for 48 assets
2025-06-26 18:37:54,639 - root - INFO - Checking cache for 14 symbols (1d)...
2025-06-26 18:37:54,654 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,655 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,656 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,656 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 18:37:54,668 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,670 - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,670 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,671 - root - INFO - Loaded 196 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:37:54,682 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,684 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,684 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,684 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 18:37:54,692 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,693 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,693 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,693 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 18:37:54,706 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,707 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,708 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,708 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 18:37:54,719 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,721 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,722 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,722 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 18:37:54,734 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,736 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,737 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,737 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 18:37:54,749 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,750 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,752 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,752 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 18:37:54,764 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,765 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,766 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,767 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 18:37:54,779 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,780 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,780 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,780 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 18:37:54,788 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,788 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,790 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,791 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 18:37:54,802 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,804 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,805 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,805 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 18:37:54,819 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,820 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,821 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,821 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 18:37:54,833 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,835 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 18:37:54,835 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,835 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 18:37:54,836 - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:37:54,836 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,836 - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,837 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,837 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,837 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,837 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,838 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,838 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,838 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,838 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,839 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,839 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,839 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,839 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 18:37:54,853 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 18:37:54,853 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 18:37:54,854 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 18:37:54,854 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:37:54,854 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:37:54,862 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:54,862 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 18:37:54,863 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 18:37:54,863 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 18:37:54,863 - root - INFO - Override: combination_method = consensus
2025-06-26 18:37:54,863 - root - INFO - Override: long_threshold = 0.1
2025-06-26 18:37:54,864 - root - INFO - Override: short_threshold = -0.1
2025-06-26 18:37:54,864 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 18:37:54,864 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 18:37:54,864 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 18:37:54,864 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 18:37:54,880 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 18:37:54,881 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 18:37:54,881 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 18:37:54,881 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 18:37:54,881 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:37:54,882 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 18:37:54,908 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 18:37:54,908 - root - INFO - Generated pgo signals: 256 values
2025-06-26 18:37:54,908 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 18:37:54,908 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 18:37:54,919 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 18:37:54,920 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 18:37:54,920 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 18:37:55,278 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 18:37:55,278 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 18:37:55,319 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 18:37:55,319 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 18:37:55,319 - root - INFO - Generated DEMA Super Score signals
2025-06-26 18:37:55,319 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 18:37:55,393 - root - INFO - Generated DPSD signals
2025-06-26 18:37:55,393 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 18:37:55,394 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 18:37:55,394 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 18:37:55,402 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 18:37:55,402 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 18:37:55,402 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 18:37:55,450 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 18:37:55,450 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 18:37:55,450 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 18:37:55,526 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 18:37:55,531 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 18:37:55,532 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:37:55,533 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 18:37:55,533 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 18:37:55,534 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 18:37:55,535 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 18:37:55,541 - root - INFO - Configuration saved successfully.
2025-06-26 18:37:55,541 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 18:37:55,541 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:37:55,549 - root - INFO - Configuration loaded successfully.
2025-06-26 18:37:55,549 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 18:37:55,549 - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-06-26 18:37:55,549 - root - INFO - Using ratio calculation method: independent
2025-06-26 18:37:55,567 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,585 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:55,602 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:55,602 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,617 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:55,618 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:37:55,625 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:55,642 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:37:55,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,657 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:37:55,661 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:55,677 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:37:55,677 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,690 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:37:55,695 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:55,711 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:37:55,712 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,724 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:37:55,730 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:55,751 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:37:55,751 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,764 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 18:37:55,768 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:55,785 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:37:55,786 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,798 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:37:55,804 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:55,818 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:37:55,818 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,831 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:37:55,836 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:55,850 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:37:55,851 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,864 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:37:55,870 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:55,886 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:55,886 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,900 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:55,905 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:55,919 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:55,920 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,934 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:55,938 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:55,952 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,968 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:55,983 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:37:55,984 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:55,997 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:37:56,002 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:56,017 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:37:56,018 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,031 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:37:56,035 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:56,050 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,069 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:56,085 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:56,085 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,101 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:56,106 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:56,124 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:37:56,124 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,136 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:37:56,142 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:56,159 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:37:56,160 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,175 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:37:56,180 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:56,198 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:37:56,198 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,217 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:37:56,224 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:56,242 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:56,242 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,255 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:56,261 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:56,276 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:37:56,277 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,291 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:37:56,295 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:56,311 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:56,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,326 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:56,331 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:56,350 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:56,350 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,365 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:56,372 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:56,388 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,406 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:56,422 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,442 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:56,458 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:37:56,458 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,473 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 18:37:56,477 - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:56,495 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:37:56,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,509 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 18:37:56,513 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:56,537 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,558 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:56,579 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:56,579 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,599 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:56,608 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:56,627 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:56,627 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,642 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:56,646 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:56,667 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:37:56,667 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,685 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 18:37:56,691 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:56,707 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:37:56,707 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,720 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:37:56,725 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:56,741 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:37:56,741 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,759 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:37:56,764 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:56,779 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:37:56,780 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,794 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:37:56,799 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:56,816 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:37:56,817 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,831 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 18:37:56,836 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:56,852 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:37:56,853 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,869 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 18:37:56,874 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:56,890 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:56,890 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,904 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:56,909 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:56,924 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:56,925 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,937 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:56,943 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:56,958 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:37:56,958 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:56,972 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:37:56,978 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:56,995 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:56,996 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,011 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:57,017 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:57,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,050 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:57,064 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,083 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:57,098 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,117 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:57,131 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:37:57,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,148 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:37:57,152 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:57,167 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,185 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:57,199 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,219 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:57,235 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,235 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,250 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,257 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:57,274 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,293 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:57,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,330 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:57,347 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:57,348 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,364 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:57,368 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:57,387 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,407 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:57,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,443 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:57,458 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:37:57,460 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,472 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:37:57,479 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:57,494 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,511 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:57,525 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,543 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:57,557 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:37:57,557 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,569 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:37:57,575 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:57,590 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,590 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,605 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,611 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:57,629 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:37:57,629 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,648 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:37:57,652 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:57,670 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:37:57,670 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,684 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:37:57,690 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:57,706 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,707 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,721 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,726 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:57,742 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:37:57,742 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,757 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:37:57,761 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:57,781 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,781 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,796 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 18:37:57,801 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:57,823 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,841 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:57,857 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,875 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:57,891 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:37:57,891 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,908 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:37:57,913 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:57,933 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,956 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:57,970 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:57,989 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:58,005 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,027 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:58,042 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,060 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:58,078 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,095 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:58,110 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:58,110 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,124 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:37:58,128 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:58,145 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:58,145 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,158 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:58,164 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:58,184 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:37:58,185 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,202 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:37:58,208 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:58,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,250 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:58,266 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,284 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:58,300 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:37:58,301 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,313 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:37:58,318 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:58,334 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,352 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:58,368 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:58,368 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,382 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:58,389 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:58,403 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,424 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:58,446 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,468 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:58,491 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,512 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:58,532 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,556 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:58,577 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,601 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:58,616 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:37:58,616 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,630 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:37:58,639 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:58,656 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,677 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:58,694 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:37:58,694 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,710 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 18:37:58,716 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:58,732 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,754 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:58,770 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,788 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:58,804 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,823 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:58,841 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,860 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:58,874 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,895 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:58,912 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:58,912 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,925 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:58,931 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:58,946 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,963 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:58,978 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:58,997 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:59,013 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:37:59,013 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,030 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:37:59,034 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:59,056 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,075 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:59,093 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:37:59,094 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,110 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:37:59,116 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:59,131 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:59,132 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,146 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:37:59,151 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:59,167 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:37:59,167 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,186 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 18:37:59,191 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:37:59,206 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,225 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:59,240 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:37:59,241 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,255 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:37:59,260 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:59,278 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:59,278 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,292 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:37:59,297 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:59,316 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,337 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:59,353 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:59,353 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,367 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:59,373 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:59,388 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,406 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:59,428 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,459 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:37:59,483 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,514 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:37:59,536 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,562 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:37:59,580 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,600 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:37:59,622 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:37:59,622 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,639 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:37:59,647 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:37:59,669 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,693 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:37:59,710 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,735 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:37:59,754 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,775 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:37:59,792 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:59,793 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,810 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:37:59,817 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:37:59,835 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,856 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:37:59,878 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,906 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:37:59,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,943 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:37:59,960 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:37:59,979 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:37:59,997 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,044 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:38:00,072 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:38:00,072 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,088 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:38:00,095 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:38:00,112 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,134 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:38:00,153 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:00,154 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,172 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:00,178 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:38:00,198 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:00,199 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,217 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:00,224 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:38:00,250 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:38:00,251 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,267 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:38:00,275 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:38:00,297 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,324 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:38:00,342 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:00,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,356 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:00,362 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:38:00,379 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:38:00,379 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,395 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:38:00,400 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:38:00,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,439 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:38:00,456 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,477 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:38:00,498 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,523 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:38:00,541 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,561 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:38:00,577 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,597 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:38:00,617 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,642 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:38:00,659 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,681 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:38:00,698 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,719 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:38:00,736 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:38:00,736 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,753 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 18:38:00,760 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:38:00,777 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:38:00,777 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,792 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 18:38:00,798 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:38:00,819 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:38:00,819 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,838 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 18:38:00,845 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:38:00,867 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:38:00,868 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,888 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 18:38:00,895 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:38:00,919 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,941 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:38:00,958 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:00,978 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:38:00,993 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,013 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:38:01,033 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,055 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:38:01,073 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,092 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:38:01,109 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,132 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:38:01,150 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,174 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:38:01,192 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:38:01,192 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,208 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:38:01,214 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:38:01,233 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:38:01,234 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,253 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 18:38:01,259 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:38:01,278 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:01,278 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,296 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:01,303 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:38:01,322 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:38:01,322 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,339 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 18:38:01,346 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:38:01,371 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:38:01,372 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,387 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 18:38:01,396 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:38:01,417 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:01,417 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,439 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:01,444 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:38:01,461 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:38:01,462 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,480 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 18:38:01,487 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:38:01,507 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:38:01,507 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,525 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 18:38:01,531 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:38:01,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,572 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:38:01,589 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:01,589 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,608 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:01,614 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:38:01,634 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,657 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:38:01,676 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,703 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:38:01,721 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:01,722 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,739 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:01,746 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:38:01,773 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:01,774 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,792 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:01,800 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:38:01,823 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:38:01,823 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,840 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 18:38:01,846 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:38:01,867 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:38:01,867 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,884 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 18:38:01,892 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:38:01,914 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:38:01,915 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:01,938 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 18:38:01,947 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:38:01,985 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:01,987 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,014 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:02,023 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:38:02,052 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:38:02,052 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,075 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 18:38:02,082 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:38:02,109 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:02,110 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,125 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 18:38:02,131 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:38:02,148 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:02,148 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,165 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:02,172 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:38:02,189 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,210 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:38:02,230 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,253 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 18:38:02,272 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,294 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 18:38:02,312 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,334 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-06-26 18:38:02,355 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,381 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 18:38:02,408 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:02,408 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,430 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 18:38:02,461 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 18:38:02,507 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:38:02,507 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,522 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 18:38:02,527 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 18:38:02,545 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:38:02,545 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,558 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 18:38:02,568 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 18:38:02,585 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:38:02,585 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,606 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 18:38:02,613 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 18:38:02,631 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:38:02,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,646 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 18:38:02,651 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 18:38:02,667 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:38:02,668 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,684 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 18:38:02,689 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 18:38:02,706 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,729 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 18:38:02,748 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:02,748 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,764 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 18:38:02,768 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 18:38:02,784 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,801 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 18:38:02,817 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 18:38:02,837 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 18:38:04,540 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 18:38:04,540 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 14)
2025-06-26 18:38:04,540 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 18:38:04,542 - root - INFO - Date ranges for each asset:
2025-06-26 18:38:04,542 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   BTC/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,542 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,547 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,547 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,547 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,547 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,547 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 18:38:04,547 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 18:38:04,550 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 18:38:04,550 - root - INFO -    Execution Method: candle_close
2025-06-26 18:38:04,550 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 18:38:04,550 - root - INFO -    Signal generated and executed immediately
2025-06-26 18:38:04,556 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,556 - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,557 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,559 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,559 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,559 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,559 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,559 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 18:38:04,559 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 18:38:04,559 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 18:38:04,559 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,559 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:38:04,559 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 18:38:04,560 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,560 - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,560 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,560 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,560 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,560 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,561 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,561 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,561 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,561 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,561 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,562 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,562 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,562 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,563 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,563 - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,563 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,563 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,563 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,563 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,564 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,564 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,564 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,564 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,564 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,564 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,565 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,565 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,565 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 18:38:04,565 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 18:38:04,565 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 18:38:04,565 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,566 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:38:04,566 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:38:04,566 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 18:38:04,567 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,567 - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,568 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,568 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,568 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,568 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,568 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,569 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,571 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,572 - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,572 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,572 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,573 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,573 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,573 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,573 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,574 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,574 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,575 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,575 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,575 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,575 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 18:38:04,577 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 18:38:04,577 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 18:38:04,578 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 18:38:04,578 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,578 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:38:04,578 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:38:04,578 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 18:38:04,580 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 18:38:04,580 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 18:38:04,581 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 18:38:04,581 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,581 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:38:04,581 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:38:04,582 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 18:38:04,583 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 18:38:04,583 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 18:38:04,584 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 18:38:04,584 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,584 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:38:04,584 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:38:04,585 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 18:38:04,587 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 18:38:04,589 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 18:38:04,589 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 18:38:04,589 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,589 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:38:04,589 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:38:04,589 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 18:38:04,592 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 18:38:04,592 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 18:38:04,592 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 18:38:04,592 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,592 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:38:04,594 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:38:04,594 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 18:38:04,596 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 18:38:04,596 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 18:38:04,596 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 18:38:04,596 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,596 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:38:04,596 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 18:38:04,597 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 18:38:04,598 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 18:38:04,598 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 18:38:04,598 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 18:38:04,600 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,600 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 18:38:04,600 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:38:04,600 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 18:38:04,602 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 18:38:04,602 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 18:38:04,602 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 18:38:04,602 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,602 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:38:04,602 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 18:38:04,603 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 18:38:04,605 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 18:38:04,605 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 18:38:04,605 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 18:38:04,605 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,606 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 18:38:04,606 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:38:04,606 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 18:38:04,608 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 18:38:04,608 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 18:38:04,608 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 18:38:04,610 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,610 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:38:04,610 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 18:38:04,610 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 18:38:04,611 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 18:38:04,611 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 18:38:04,611 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 18:38:04,611 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,612 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 18:38:04,612 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:38:04,612 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:38:04,614 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 18:38:04,614 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 18:38:04,614 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 18:38:04,614 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,614 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:38:04,614 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 18:38:04,615 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 18:38:04,616 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 18:38:04,616 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 18:38:04,617 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 18:38:04,617 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,617 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 18:38:04,617 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:38:04,617 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 18:38:04,624 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 18:38:04,625 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 18:38:04,625 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 18:38:04,625 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,625 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 18:38:04,626 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 18:38:04,626 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 18:38:04,628 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 18:38:04,628 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 18:38:04,628 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 18:38:04,628 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,628 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 18:38:04,628 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 18:38:04,629 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 18:38:04,636 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 18:38:04,636 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 18:38:04,637 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 18:38:04,637 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,637 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 18:38:04,637 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:38:04,637 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:38:04,644 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 18:38:04,644 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 18:38:04,644 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 18:38:04,644 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,644 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:38:04,645 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:38:04,645 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 18:38:04,646 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 18:38:04,647 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 18:38:04,647 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 18:38:04,647 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,647 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:38:04,647 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 18:38:04,648 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 18:38:04,649 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 18:38:04,650 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 18:38:04,650 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 18:38:04,650 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,650 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 18:38:04,651 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 18:38:04,651 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 18:38:04,663 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 18:38:04,664 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 18:38:04,664 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 18:38:04,664 - root - INFO -    Execution Delay: 0 hours
2025-06-26 18:38:04,664 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 18:38:04,666 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 18:38:04,667 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 18:38:04,703 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 18:38:04,703 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:38:04,703 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 18:38:04,704 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 18:38:04,705 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 18:38:04,705 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 18:38:04,705 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 18:38:04,705 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 18:38:04,706 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 18:38:04,706 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 18:38:04,706 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 18:38:04,706 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:38:04,706 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 18:38:04,706 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 18:38:04,707 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 18:38:04,707 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 18:38:04,708 - root - INFO - Strategy execution completed in 0s
2025-06-26 18:38:04,708 - root - INFO - DEBUG: self.elapsed_time = 0.16651296615600586 seconds
2025-06-26 18:38:04,710 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 18:38:04,710 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 18:38:04,710 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 18:38:04,710 - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-06-26 18:38:04,710 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 18:38:04,711 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 18:38:04,712 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 18:38:04,713 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,714 - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,715 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,716 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,717 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,718 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,719 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,720 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,721 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,722 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,723 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,724 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,725 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,727 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 18:38:04,729 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 18:38:04,729 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 18:38:04,730 - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 196 points
2025-06-26 18:38:04,730 - root - INFO - BTC/USDT B&H total return: 10.17%
2025-06-26 18:38:04,732 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 18:38:04,732 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 18:38:04,734 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 18:38:04,735 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 18:38:04,736 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 18:38:04,736 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 18:38:04,738 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 18:38:04,738 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 18:38:04,739 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 18:38:04,739 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 18:38:04,741 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 18:38:04,741 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 18:38:04,743 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 18:38:04,743 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 18:38:04,744 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 18:38:04,745 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 18:38:04,746 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 18:38:04,746 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 18:38:04,748 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 18:38:04,748 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 18:38:04,749 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 18:38:04,750 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 18:38:04,751 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 18:38:04,751 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 18:38:04,752 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:38:04,760 - root - INFO - Configuration loaded successfully.
2025-06-26 18:38:04,768 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 18:38:04,956 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 18:38:04,966 - root - INFO - Configuration loaded successfully.
2025-06-26 18:38:05,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:38:06,509 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,510 - root - INFO - Added BTC/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,510 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,510 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,510 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,511 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,511 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,511 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,511 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,511 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,512 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,512 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,512 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,512 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 18:38:06,512 - root - INFO - Added 14 buy-and-hold curves to results
2025-06-26 18:38:06,512 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,514 - root - INFO -   - BTC/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,514 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,514 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,514 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,514 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,514 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,515 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,515 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,515 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,515 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,515 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,517 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,519 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 18:38:06,536 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 18:38:06,536 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:38:06,539 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 18:38:06,539 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 18:38:06,542 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 18:38:06,542 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - TIED USDT ASSETS: ['BTC/USDT', 'TRX/USDT'] (score: 12.0)
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 8.0)
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 12.0)
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 6.0)
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 2.0)
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 9.0)
2025-06-26 18:38:06,543 - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 9.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 3.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 3.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 6.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 12.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 0.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 3.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 11.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 1.0)
2025-06-26 18:38:06,544 - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:38:06,545 - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:38:06,545 - root - ERROR - [DEBUG] CONVERSION - TIED EUR ASSETS: ['BTC/EUR', 'TRX/EUR'] (score: 12.0)
2025-06-26 18:38:06,545 - root - ERROR - [DEBUG] CONVERSION - First EUR asset (should be selected): BTC/EUR
2025-06-26 18:38:06,545 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:38:06,555 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_183748.csv
2025-06-26 18:38:06,556 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_since_20250619_run_********_183748.csv
2025-06-26 18:38:06,557 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 18:38:06,558 - root - INFO - Results type: <class 'dict'>
2025-06-26 18:38:06,558 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 18:38:06,558 - root - INFO - Success flag set to: True
2025-06-26 18:38:06,558 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 18:38:06,558 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:38:06,558 - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-06-26 18:38:06,558 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 18:38:06,559 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 18:38:06,559 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 18:38:06,559 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 18:38:06,560 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 18:38:06,560 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 18:38:06,560 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 18:38:06,560 - root - INFO -   - success: <class 'bool'>
2025-06-26 18:38:06,560 - root - INFO -   - message: <class 'str'>
2025-06-26 18:38:06,560 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 18:38:06,560 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 18:38:06,560 - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 18:38:06,563 - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 18:38:06,563 - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:38:06,563 - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:38:06,563 - root - INFO - [DEBUG] TIE-BREAKING - Strategy: momentum
2025-06-26 18:38:06,563 - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 12.0
2025-06-26 18:38:06,564 - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:38:06,564 - root - ERROR - [DEBUG] TIE DETECTED - 2 assets tied at score 12.0
2025-06-26 18:38:06,564 - root - ERROR - [DEBUG] TIE DETECTED - Tied assets: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:38:06,564 - root - ERROR - [DEBUG] TIE DETECTED - Current best_asset from series: TRX/EUR
2025-06-26 18:38:06,564 - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-06-26 18:38:06,565 - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:38:06,565 - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:38:06,565 - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 12.0
2025-06-26 18:38:06,565 - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 12.0: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:38:06,565 - root - ERROR - [DEBUG] TIE DETECTED: 2 assets have the same maximum score of 12.0
2025-06-26 18:38:06,565 - root - ERROR - [DEBUG] Tied assets: ['BTC/EUR', 'TRX/EUR']
2025-06-26 18:38:06,565 - root - ERROR - [DEBUG] Dictionary iteration order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:38:06,566 - root - ERROR - [DEBUG] Tie-breaking: Selecting first asset in dictionary order: BTC/EUR
2025-06-26 18:38:06,566 - root - ERROR - [DEBUG] Complete asset ranking:
2025-06-26 18:38:06,566 - root - ERROR - [DEBUG]   1. BTC/EUR: 12.0 (TIED) <- SELECTED
2025-06-26 18:38:06,567 - root - ERROR - [DEBUG]   2. TRX/EUR: 12.0 (TIED)
2025-06-26 18:38:06,567 - root - ERROR - [DEBUG]   3. BNB/EUR: 11.0
2025-06-26 18:38:06,567 - root - ERROR - [DEBUG]   4. XRP/EUR: 9.0
2025-06-26 18:38:06,568 - root - ERROR - [DEBUG]   5. AAVE/EUR: 9.0
2025-06-26 18:38:06,569 - root - ERROR - [DEBUG]   6. ETH/EUR: 8.0
2025-06-26 18:38:06,570 - root - ERROR - [DEBUG]   7. SOL/EUR: 6.0
2025-06-26 18:38:06,570 - root - ERROR - [DEBUG]   8. LINK/EUR: 6.0
2025-06-26 18:38:06,570 - root - ERROR - [DEBUG]   9. AVAX/EUR: 3.0
2025-06-26 18:38:06,571 - root - ERROR - [DEBUG]   10. ADA/EUR: 3.0
2025-06-26 18:38:06,571 - root - ERROR - [DEBUG]   11. DOGE/EUR: 3.0
2025-06-26 18:38:06,571 - root - ERROR - [DEBUG]   12. SUI/EUR: 2.0
2025-06-26 18:38:06,571 - root - ERROR - [DEBUG]   13. DOT/EUR: 1.0
2025-06-26 18:38:06,571 - root - ERROR - [DEBUG]   14. PEPE/EUR: 0.0
2025-06-26 18:38:06,572 - root - ERROR - [DEBUG] SELECTED BEST ASSET: BTC/EUR (score: 12.0)
2025-06-26 18:38:06,572 - root - ERROR - [DEBUG] MOMENTUM APPROACH - Using current day tie-breaking: BTC/EUR
2025-06-26 18:38:06,572 - root - ERROR - [DEBUG] TIE-BREAKING CHANGED SELECTION: TRX/EUR -> BTC/EUR
2025-06-26 18:38:06,598 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-06-26 18:38:06,598 - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 18:38:06,598 - root - INFO - Single asset strategy with best asset: BTC/EUR
2025-06-26 18:38:06,599 - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 18:38:06,599 - root - INFO - [DEBUG]   - Best asset selected: BTC/EUR
2025-06-26 18:38:06,599 - root - INFO - [DEBUG]   - Assets held: {'BTC/EUR': 1.0}
2025-06-26 18:38:06,599 - root - INFO - [DEBUG]   - MTPI signal: 1
2025-06-26 18:38:06,600 - root - INFO - [DEBUG]   - Use MTPI signal: False
2025-06-26 18:38:06,607 - root - INFO - Executing single-asset strategy with best asset: BTC/EUR
2025-06-26 18:38:06,607 - root - INFO - Executing strategy signal: best_asset=BTC/EUR, mtpi_signal=1, mode=paper
2025-06-26 18:38:06,608 - root - INFO - Incremented daily trade counter for BTC/EUR: 1/5
2025-06-26 18:38:06,608 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: BTC/EUR
2025-06-26 18:38:06,608 - root - INFO - Attempting to enter position for BTC/EUR in paper mode
2025-06-26 18:38:06,608 - root - INFO - [DEBUG] TRADE - BTC/EUR: Starting enter_position attempt
2025-06-26 18:38:06,608 - root - INFO - [DEBUG] TRADE - BTC/EUR: Trading mode: paper
2025-06-26 18:38:06,608 - root - INFO - TRADE ATTEMPT - BTC/EUR: Getting current market price...
2025-06-26 18:38:06,609 - root - INFO - [DEBUG] PRICE - BTC/EUR: Starting get_current_price
2025-06-26 18:38:06,610 - root - INFO - [DEBUG] PRICE - BTC/EUR: Exchange ID: kraken
2025-06-26 18:38:06,610 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Initializing exchange kraken
2025-06-26 18:38:06,614 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange initialized successfully
2025-06-26 18:38:06,615 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Exchange markets not loaded, loading now...
2025-06-26 18:38:07,751 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Symbol found after loading markets
2025-06-26 18:38:07,753 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Attempting to fetch ticker...
2025-06-26 18:38:08,640 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker fetched successfully
2025-06-26 18:38:08,640 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Ticker data: {'symbol': 'BTC/EUR', 'timestamp': None, 'datetime': None, 'high': 92625.0, 'low': 91186.6, 'bid': 91359.7, 'bidVolume': 1.0, 'ask': 91362.1, 'askVolume': 1.0, 'vwap': 91897.2196, 'open': 91958.9, 'close': 91362.6, 'last': 91362.6, 'previousClose': None, 'change': -596.3, 'percentage': -0.6484418582649422, 'average': 91660.7, 'baseVolume': 370.29572767, 'quoteVolume': 34029147.80263179, 'info': {'a': ['91362.10000', '1', '1.000'], 'b': ['91359.70000', '1', '1.000'], 'c': ['91362.60000', '0.00086314'], 'v': ['268.00740178', '370.29572767'], 'p': ['91775.13284', '91897.21960'], 't': [13817, 19720], 'l': ['91186.60000', '91186.60000'], 'h': ['92598.50000', '92625.00000'], 'o': '91958.90000'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 18:38:08,640 - root - ERROR - [DEBUG] PRICE - BTC/EUR: Last price: 91362.6
2025-06-26 18:38:08,640 - root - INFO - [DEBUG] TRADE - BTC/EUR: get_current_price returned: 91362.6
2025-06-26 18:38:08,640 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price type: <class 'float'>
2025-06-26 18:38:08,640 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - not price: False
2025-06-26 18:38:08,640 - root - INFO - [DEBUG] TRADE - BTC/EUR: Price evaluation - price <= 0: False
2025-06-26 18:38:08,640 - root - INFO - TRADE ATTEMPT - BTC/EUR: Current price: 91362.********
2025-06-26 18:38:08,640 - root - INFO - Available balance for EUR: 100.********
2025-06-26 18:38:08,640 - root - INFO - Reserved 0.599940 USDC for fees (rate: 0.4% with buffer)
2025-06-26 18:38:08,640 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 18:38:08,640 - root - INFO - Calculated position size for BTC/EUR: 0.******** (using 99.99% of 100, accounting for fees)
2025-06-26 18:38:08,640 - root - INFO - Calculated position size: 0.******** BTC
2025-06-26 18:38:08,640 - root - INFO - Entering position for BTC/EUR: 0.******** units at 91362.******** (value: 98.******** EUR)
2025-06-26 18:38:08,640 - root - INFO - Executing paper market buy order for BTC/EUR
2025-06-26 18:38:08,640 - root - INFO - Paper trading buy for BTC/EUR: original=0.********, adjusted=0.********, after_fee=0.********
2025-06-26 18:38:08,649 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 18:38:08,649 - root - INFO - Created paper market buy order: BTC/EUR, amount: 0.0010759352897941662, price: 91362.6
2025-06-26 18:38:08,649 - root - INFO - Filled amount: 0.******** BTC
2025-06-26 18:38:08,649 - root - INFO - Order fee: 0.******** EUR
2025-06-26 18:38:08,649 - root - INFO - Successfully entered position: BTC/EUR, amount: 0.********, price: 91362.********
2025-06-26 18:38:08,660 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91362.********, filled=0.********
2025-06-26 18:38:08,660 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:38:08,660 - root - INFO - TRADE SUCCESS - BTC/EUR: Successfully updated current asset
2025-06-26 18:38:08,660 - root - INFO - Trade executed: BUY BTC/EUR, amount=0.********, price=91362.********, filled=0.********
2025-06-26 18:38:08,660 - root - INFO -   Fee: 0.******** EUR
2025-06-26 18:38:08,660 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 18:38:08,660 - root - INFO - Trade executed: {'success': True, 'symbol': 'BTC/EUR', 'side': 'buy', 'amount': 0.********44242173492, 'price': 91362.6, 'order': {'id': 'paper-1750955888-BTC/EUR-buy-0.0010759352897941662', 'symbol': 'BTC/EUR', 'side': 'buy', 'type': 'market', 'amount': 0.0010759352897941662, 'price': 91362.6, 'cost': 98.3986441515, 'fee': {'cost': 0.********41515, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 0.0010759352897941662, 'remaining': 0, 'timestamp': 1750955888640, 'datetime': '2025-06-26T18:38:08.640736', 'trades': [], 'average': 91362.6, 'average_price': 91362.6}, 'filled_amount': 0.0010759352897941662, 'fee': {'cost': 0.********41515, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'BTC', 'timestamp': '2025-06-26T18:38:08.650801'}
2025-06-26 18:38:08,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:38:08,807 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 18:38:08,807 - root - INFO - Asset scores (sorted by score):
2025-06-26 18:38:08,808 - root - INFO -   BTC/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 18:38:08,808 - root - INFO -   TRX/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,808 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,808 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,808 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,808 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,808 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,808 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,809 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,809 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,810 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,810 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,810 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,810 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 18:38:08,810 - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-06-26 18:38:08,810 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 18:38:08,810 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'BTC/EUR': 12.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 18:38:08,919 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-06-26 18:38:08,919 - root - INFO - Strategy execution completed successfully in 20.86 seconds
2025-06-26 18:38:08,923 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 18:38:15,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:38:25,666 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:38:35,678 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:38:45,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:38:55,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:39:05,722 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:39:15,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:39:25,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:39:35,761 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:39:45,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:39:55,782 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:40:05,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:40:15,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:40:25,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:40:35,832 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:40:45,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:40:55,860 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:41:05,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:41:15,883 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:41:25,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:41:35,911 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:41:45,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:41:55,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:42:05,952 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:42:15,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:42:25,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:42:36,000 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:42:46,022 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:42:56,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:43:06,050 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:43:16,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:43:26,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:43:36,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:43:46,112 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:43:56,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:44:06,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:44:16,156 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:44:26,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:44:36,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:44:46,200 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:44:56,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:45:06,230 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:45:16,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:45:26,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:45:36,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:45:46,285 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:45:56,298 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:46:06,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:46:16,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:46:26,339 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:46:36,350 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:46:46,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:46:56,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:47:06,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:47:16,395 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:47:26,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:47:36,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:47:46,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:47:56,445 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:48:06,457 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:48:16,470 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:48:26,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:48:36,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:48:46,516 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:48:56,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:49:06,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:49:16,560 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:49:26,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:49:36,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:49:46,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:49:56,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:50:06,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:50:16,676 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:50:26,682 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:50:36,702 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:50:46,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:50:56,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:51:06,740 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:51:16,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:51:26,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:51:36,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:51:46,795 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:51:56,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:52:06,824 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:52:16,838 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:52:26,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:52:36,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:52:46,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:52:56,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:53:06,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:53:16,919 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:53:26,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:53:36,950 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:53:46,963 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:53:56,978 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:54:06,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:54:17,004 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:54:27,014 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:54:37,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:54:47,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:54:57,098 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:55:07,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:55:17,115 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:55:27,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:55:37,155 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:55:47,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:55:57,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:56:07,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:56:17,210 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:56:27,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:56:37,245 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:56:47,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:56:57,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:57:07,346 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:57:17,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:57:27,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:57:37,399 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:57:47,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:57:57,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:58:07,444 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:58:17,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:58:27,473 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:58:37,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:58:47,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:58:57,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:59:07,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:59:17,542 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:59:27,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:59:37,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:59:47,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 18:59:57,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:00:07,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:00:17,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:00:27,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:00:37,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:00:47,681 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:00:57,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:01:07,711 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:01:17,720 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:01:27,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:01:37,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:01:47,769 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:01:57,789 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:02:07,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:02:17,814 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:02:27,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:02:37,840 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:02:47,854 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:02:57,871 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:03:07,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:03:17,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:03:27,918 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:03:37,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:03:47,947 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:03:57,960 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:04:07,974 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:04:17,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:04:27,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:04:38,009 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:04:48,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:04:58,043 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:05:08,059 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:05:18,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:05:28,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:05:38,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:05:48,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:05:58,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:06:08,169 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:06:18,184 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:06:28,193 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:06:38,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:06:48,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:06:58,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:07:08,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:07:18,359 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:07:28,374 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:07:38,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:07:48,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:07:58,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:08:08,435 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:08:18,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:08:28,460 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:08:38,478 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:08:48,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:08:58,499 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:09:08,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:09:18,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:09:28,541 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:09:38,558 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:09:48,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:09:58,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:10:08,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:10:18,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:10:28,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:10:38,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:10:48,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:10:58,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:11:08,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:11:18,733 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:11:28,750 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:11:38,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:11:48,769 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:11:58,790 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:12:08,802 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:12:18,808 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:12:28,829 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:12:38,844 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:12:48,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:12:58,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:13:08,894 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:13:18,907 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:13:28,922 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:13:38,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:13:48,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:13:58,975 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:14:08,988 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:14:18,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:14:29,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:14:39,033 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:14:49,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:14:59,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:15:09,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:15:19,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:15:29,119 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:15:39,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:15:49,149 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:15:59,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:16:09,177 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:16:19,190 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:16:29,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:16:39,223 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:16:49,243 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:16:59,256 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:17:09,269 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:17:19,285 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:17:29,293 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:17:39,309 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:17:49,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:17:59,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:18:09,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:18:19,365 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:18:29,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:18:39,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:18:49,407 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:18:59,417 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:19:09,431 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:19:19,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:19:29,475 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:19:39,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:19:49,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:19:59,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:20:09,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:20:19,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:20:29,569 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:20:39,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:20:49,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:20:59,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:21:09,639 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:21:19,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:21:29,669 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:21:39,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:21:49,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:21:59,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:22:09,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:22:19,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:22:29,763 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:22:39,785 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:22:49,799 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:22:59,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:23:09,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:23:19,944 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:23:29,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:23:39,972 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:23:49,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:24:00,002 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:24:10,014 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:24:20,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:24:30,035 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:24:40,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:24:50,068 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:25:00,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:25:10,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:25:20,124 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:25:30,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:25:40,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:25:50,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:26:00,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:26:10,211 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:26:20,225 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:26:30,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:26:40,252 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:26:50,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:27:00,279 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:27:10,287 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:27:20,304 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:27:30,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:27:40,334 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:27:50,349 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:28:00,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:28:10,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:28:20,413 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:28:30,436 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:28:40,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:28:50,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:29:00,511 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:29:10,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:29:20,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:29:30,554 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:29:40,564 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:29:50,580 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:30:00,596 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:30:10,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:30:20,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:30:30,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:30:40,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:30:50,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:31:00,712 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:31:10,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:31:20,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:31:30,754 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:31:40,771 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:31:50,786 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:32:00,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:32:10,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:32:20,835 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:32:30,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:32:40,868 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:32:50,885 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:33:00,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:33:10,919 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:33:20,932 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:33:30,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:33:40,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:33:50,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:34:00,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:34:10,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:34:21,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:34:31,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:34:41,050 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:34:51,060 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:35:01,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:35:11,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:35:21,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:35:31,128 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:35:41,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:35:51,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:36:01,179 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:36:11,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:36:21,208 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:36:31,215 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:36:41,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:36:51,255 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:37:01,265 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:37:11,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:37:21,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:37:31,344 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:37:41,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:37:51,372 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:38:01,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:38:11,405 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:38:21,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:38:31,432 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:38:41,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:38:51,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:39:01,477 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:39:11,489 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:39:21,508 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:39:31,521 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:39:41,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:39:51,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:40:01,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:40:11,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:40:21,608 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:40:31,614 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:40:41,633 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:40:51,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:41:01,659 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:41:11,677 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:41:21,690 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:41:31,703 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:41:41,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:41:51,732 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:42:01,744 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:42:11,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:42:21,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:42:31,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:42:41,810 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:42:51,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:43:01,835 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:43:11,846 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:43:21,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:43:31,875 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:43:41,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:43:51,902 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:44:01,914 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:44:11,931 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:44:21,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:44:31,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:44:42,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:44:52,028 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:45:02,044 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:45:12,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:45:22,069 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:45:32,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:45:42,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:45:52,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:46:02,127 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:46:12,145 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:46:22,162 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:46:32,182 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:46:42,234 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:46:52,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:47:02,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:47:12,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:47:22,289 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:47:32,293 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:47:42,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:47:52,329 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:48:02,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:48:12,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:48:22,378 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:48:32,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:48:42,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:48:52,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:49:02,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:49:12,436 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:49:22,466 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:49:32,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:49:42,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:49:52,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:50:02,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:50:12,563 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:50:22,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:50:32,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:50:42,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:50:52,620 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:51:02,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:51:12,648 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:51:22,656 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:51:32,675 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:51:42,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:51:52,708 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:52:02,714 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:52:12,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:52:22,749 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:52:32,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:52:42,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:52:52,785 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:53:02,806 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:53:12,821 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:53:22,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:53:32,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:53:42,879 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:53:52,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:54:02,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:54:12,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:54:22,936 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:54:32,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:54:42,961 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:54:52,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:55:02,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:55:13,008 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:55:23,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:55:33,035 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:55:43,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:55:53,063 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:56:03,076 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:56:13,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:56:23,104 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:56:33,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:56:43,131 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:56:53,140 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:57:03,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:57:13,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:57:23,183 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:57:33,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:57:43,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:57:53,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:58:03,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:58:13,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:58:23,389 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:58:33,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:58:43,424 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:58:53,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:59:03,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:59:13,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:59:23,479 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:59:33,492 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:59:43,504 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 19:59:53,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:00:03,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:00:13,541 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:00:23,560 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:00:33,571 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:00:43,587 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:00:53,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:01:03,616 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:01:13,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:01:23,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:01:33,693 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:01:43,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:01:53,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:02:03,741 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:02:13,750 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:02:23,773 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:02:33,778 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:02:43,795 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:02:53,818 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:03:03,849 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:03:13,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:03:23,878 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:03:33,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:03:43,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:03:53,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:04:03,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:04:13,983 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:04:23,988 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:04:33,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:04:44,019 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:04:54,034 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:05:04,062 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:05:14,081 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:05:24,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:05:34,105 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:05:44,117 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:05:54,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:06:04,151 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:06:14,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:06:24,181 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:06:34,196 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:06:44,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:06:54,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:07:04,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:07:14,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:07:24,303 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:07:34,315 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:07:44,335 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:07:54,353 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:08:04,377 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:08:14,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:08:24,406 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:08:34,419 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:08:44,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:08:54,470 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:09:04,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:09:14,503 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:09:24,514 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:09:34,538 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:09:44,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:09:54,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:10:04,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:10:14,595 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:10:24,613 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:10:34,627 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:10:44,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:10:54,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:11:04,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:11:14,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:11:24,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:11:34,723 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:11:44,745 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:11:54,765 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:12:04,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:12:14,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:12:24,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:12:34,844 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:12:44,869 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:12:54,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:13:04,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:13:14,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:13:24,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:13:34,947 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:13:44,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:13:54,983 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:14:04,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:14:15,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:14:25,038 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:14:35,043 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:14:45,082 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:14:55,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:15:05,114 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:15:15,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:15:25,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:15:35,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:15:45,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:15:55,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:16:05,234 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:16:15,253 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:16:25,267 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:16:35,283 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:16:45,311 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:16:55,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:17:05,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:17:15,365 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:17:25,393 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:17:35,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:17:45,449 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:17:55,463 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:18:05,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:18:15,529 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:18:25,544 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:18:35,554 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:18:45,576 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:18:55,600 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:19:05,618 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:19:15,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:19:25,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:19:35,662 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:19:45,685 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:19:55,709 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:20:05,720 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:20:15,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:20:25,757 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:20:35,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:20:45,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:20:55,803 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:21:05,820 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:21:15,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:21:25,847 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:21:35,873 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:21:45,884 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:21:55,901 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:22:05,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:22:15,929 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:22:25,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:22:35,959 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:22:45,979 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:22:56,002 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:23:06,015 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:23:16,025 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:23:26,053 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:23:36,073 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:23:46,091 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:23:56,103 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:24:06,110 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:24:16,135 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:24:26,143 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:24:36,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:24:46,188 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:24:56,202 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:25:06,219 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:25:16,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:25:26,244 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:25:36,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:25:46,282 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:25:56,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:26:06,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:26:16,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:26:26,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:26:36,389 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:26:46,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:26:56,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:27:06,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:27:16,459 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:27:26,481 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:27:36,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:27:46,522 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:27:56,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:28:06,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:28:16,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:28:26,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:28:36,604 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:28:46,631 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:28:56,636 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:29:06,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:29:16,673 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:29:26,689 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:29:36,698 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:29:46,716 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:29:56,735 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:30:06,746 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:30:16,766 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:30:26,782 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:30:36,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:30:46,809 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:30:56,824 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:31:06,841 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:31:16,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:31:26,872 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:31:36,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:31:46,900 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:31:56,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:32:06,924 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:32:16,949 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:32:26,966 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:32:36,980 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:32:46,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:32:57,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:33:07,020 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:33:17,024 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:33:27,045 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:33:37,063 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:33:47,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:33:57,089 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:34:07,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:34:17,132 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:34:27,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:34:37,160 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:34:47,203 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:34:57,220 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:35:07,234 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:35:17,282 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:35:27,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:35:37,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:35:47,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:35:57,369 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:36:07,381 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:36:17,395 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:36:27,408 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:36:37,415 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:36:47,429 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:36:57,450 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:37:07,464 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:37:17,482 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:37:27,494 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:37:37,498 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:37:47,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:37:57,534 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:38:07,549 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:38:17,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:38:27,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:38:37,585 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:38:47,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:38:57,618 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:39:07,625 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:39:17,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:39:27,655 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:39:37,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:39:47,691 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:39:57,705 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:40:07,715 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:40:17,742 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:40:27,755 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:40:37,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:40:47,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:40:57,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:41:07,813 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:41:17,833 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:41:27,844 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:41:37,853 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:41:47,870 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:41:57,883 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:42:07,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:42:17,911 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:42:27,923 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:42:37,931 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:42:47,956 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:42:57,968 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:43:07,982 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:43:17,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:43:28,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:43:38,453 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:43:48,465 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:43:58,487 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:44:08,500 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:44:18,515 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:44:28,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:44:38,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:44:48,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:44:58,573 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:45:08,584 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:45:18,608 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:45:28,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:45:38,635 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:45:48,652 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:45:58,665 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:46:08,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:46:18,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:46:28,705 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:46:38,718 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:46:48,724 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:46:58,745 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:47:08,760 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 20:47:18,772 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:22:47,825 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:22:47,960 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 69, in map_httpcore_exceptions
    yield
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 373, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 216, in handle_async_request
    raise exc from None
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection_pool.py", line 196, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\connection.py", line 101, in handle_async_request
    return await self._connection.handle_async_request(request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\http11.py", line 143, in handle_async_request
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\http11.py", line 113, in handle_async_request
    ) = await self._receive_response_headers(**kwargs)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\http11.py", line 186, in _receive_response_headers
    event = await self._receive_event(timeout=timeout)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_async\http11.py", line 224, in _receive_event
    data = await self._network_stream.read(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_backends\anyio.py", line 32, in read
    with map_exceptions(exc_map):
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 273, in do_request
    res = await self._client.request(
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1574, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1661, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1689, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1726, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_client.py", line 1763, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 372, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Python312\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\httpx\_transports\default.py", line 86, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ReadError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_baserequest.py", line 304, in _request_wrapper
    code, payload = await self.do_request(
                    ^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Python312\Lib\site-packages\telegram\request\_httpxrequest.py", line 297, in do_request
    raise NetworkError(f"httpx.{err.__class__.__name__}: {err}") from err
telegram.error.NetworkError: httpx.ReadError: 
2025-06-26 22:22:59,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:23:09,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:23:19,150 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:23:29,308 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:23:40,065 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:23:50,312 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:24:00,329 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:24:10,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:24:20,347 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:24:30,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:24:40,383 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:24:50,397 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:25:00,418 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:25:10,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:25:20,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:25:30,461 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:25:40,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:25:50,488 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:26:00,512 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:26:10,525 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:26:20,545 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:26:30,567 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:26:40,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:26:50,603 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:27:00,622 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:27:10,647 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:27:20,660 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:27:30,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:27:40,699 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:27:50,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:28:00,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:28:10,775 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:28:20,787 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:28:30,801 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:28:40,815 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:28:50,830 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:29:00,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:29:10,898 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:29:20,916 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:29:30,925 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:29:40,946 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:29:50,962 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:30:00,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:30:10,998 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:30:21,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:30:31,023 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:30:41,037 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:30:51,047 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:31:01,057 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:31:11,078 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:31:21,096 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:31:31,113 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:31:41,127 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:31:51,144 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:32:01,152 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:32:11,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:32:21,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:32:31,197 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:32:41,218 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:32:51,229 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:33:01,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:33:11,257 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:33:21,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:33:31,294 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:33:41,309 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:33:51,324 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:34:01,335 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:34:11,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:34:21,358 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:34:31,375 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:34:41,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:34:51,409 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:35:01,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:35:11,438 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:35:21,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:35:31,467 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:35:41,482 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:35:51,501 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:36:01,518 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:36:11,533 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:36:21,546 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:36:31,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:36:41,867 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:36:51,880 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:37:01,893 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:37:12,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:37:22,143 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:37:32,387 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:37:42,398 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:37:52,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:38:02,461 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:38:14,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:38:24,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:38:34,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:38:44,421 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:38:54,967 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:39:04,986 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:39:14,999 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:39:25,017 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:39:35,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:39:45,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:39:55,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:40:05,624 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:40:15,864 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:40:25,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:40:35,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:40:45,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:40:55,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:41:05,976 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:41:16,224 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:41:26,238 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:41:36,274 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:41:46,292 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:41:56,308 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:42:06,331 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:42:16,345 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:42:26,355 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:42:36,373 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:42:46,396 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:42:56,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:43:06,433 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:43:16,448 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:43:26,468 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:43:36,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:43:46,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:43:56,527 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:44:06,538 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:44:16,554 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:44:26,568 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:44:36,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:44:46,592 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:44:56,615 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:45:06,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:45:16,646 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:45:26,666 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:45:36,680 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:45:46,695 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:45:56,713 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:46:06,726 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:46:16,739 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:46:26,753 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:46:36,770 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:46:46,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:46:56,797 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:47:06,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:47:16,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:47:26,839 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:47:36,844 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:47:46,866 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:47:56,882 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:48:06,895 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:48:16,910 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:48:26,926 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:48:36,940 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:48:46,954 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:48:56,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:49:06,987 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:49:17,003 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:49:27,018 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:49:37,031 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:49:47,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:49:57,072 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:50:07,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:50:17,114 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:50:27,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:50:37,147 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:50:47,164 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:50:57,174 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:51:07,187 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:51:17,217 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:51:27,235 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:51:37,260 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:51:47,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:51:57,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:52:07,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:52:17,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:52:27,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:52:37,355 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:52:47,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:52:57,392 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:53:12,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:53:22,472 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:53:32,484 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:53:42,504 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:53:52,530 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:54:02,541 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:54:12,577 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:54:22,599 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:54:32,612 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:54:42,688 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:54:52,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:55:02,782 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:55:12,798 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:55:22,828 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:55:32,845 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:55:42,861 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:55:52,881 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:56:02,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:56:12,965 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:56:23,031 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:56:33,086 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:56:43,109 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:56:53,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:57:03,180 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:57:13,204 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:57:23,247 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:57:33,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:57:43,321 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:57:53,356 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:58:03,390 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:58:13,425 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:58:23,462 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:58:33,474 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:58:43,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:58:53,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:59:03,543 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:59:13,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:59:23,578 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:59:33,585 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:59:43,645 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 22:59:53,659 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:00:03,706 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:00:13,736 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:00:23,776 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:00:33,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:00:43,808 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:00:53,823 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:01:03,921 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:01:13,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:01:23,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:01:33,987 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:01:43,996 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:01:54,013 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:02:04,031 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:02:14,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:02:24,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:02:34,118 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:02:44,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:02:54,189 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:03:04,242 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:03:14,259 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:03:24,313 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:03:34,347 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:03:44,362 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:03:54,414 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:04:04,428 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:04:14,491 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:04:24,517 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:04:34,557 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:04:44,586 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:04:54,603 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:05:04,666 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:05:14,802 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:05:24,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:05:34,876 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:05:44,889 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:05:54,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:06:04,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:06:14,953 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:06:24,977 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:06:35,051 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:06:45,068 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:06:55,084 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:07:05,093 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:07:15,122 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:07:25,157 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:07:35,189 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:07:45,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:07:55,266 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:08:05,451 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:08:15,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:08:25,553 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:08:35,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:08:45,745 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:08:55,780 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:09:05,816 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:09:15,846 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:09:25,877 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:09:35,907 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:09:45,958 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:09:55,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:10:05,985 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:10:16,063 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:10:26,097 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:10:36,133 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:10:46,165 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:10:56,195 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:11:06,239 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:11:16,268 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:11:26,309 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:11:36,352 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:11:46,379 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:11:56,415 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:12:06,426 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:12:16,439 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:12:26,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:12:36,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:12:46,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:12:56,607 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:13:06,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:13:16,696 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:13:26,730 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:13:36,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:13:46,768 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:13:56,791 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:14:06,816 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:14:16,835 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:14:26,897 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:14:36,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:14:47,012 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:14:57,027 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:15:07,085 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:15:17,095 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:15:27,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:15:37,186 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:15:47,226 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:15:57,258 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:16:07,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:16:17,325 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:16:27,363 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:16:37,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:16:47,435 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:16:57,447 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:17:07,509 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:17:17,538 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:17:27,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:17:37,610 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:17:47,626 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:17:57,640 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:18:07,710 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:18:17,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:18:27,785 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:18:37,802 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:18:47,857 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:18:57,995 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:19:08,030 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:19:18,043 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:19:28,111 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:19:38,129 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:19:48,148 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:19:58,206 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:20:08,232 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:20:18,275 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:20:28,310 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:20:38,342 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:20:48,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:20:58,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:21:08,452 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:21:18,486 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:21:28,524 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:21:38,552 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:21:48,588 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:21:58,630 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:22:08,666 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:22:18,694 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:22:28,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:22:38,770 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:22:48,793 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:22:58,945 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:23:08,978 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:23:26,904 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:23:37,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:23:47,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:23:58,311 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:24:08,828 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:24:19,121 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:24:29,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:24:39,192 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:24:49,225 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:24:59,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:25:09,295 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:25:19,434 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:25:29,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:25:39,505 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:25:49,540 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:25:59,555 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:26:09,611 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:26:19,634 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:26:29,683 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:26:39,717 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:26:49,728 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:26:59,783 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:27:09,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:27:19,852 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:27:29,890 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:27:39,915 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:27:49,961 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:27:59,984 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:28:09,999 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:28:20,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:28:30,100 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:28:40,120 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:28:50,136 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:29:00,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:29:10,241 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:29:20,251 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:29:30,271 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:29:40,330 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:29:50,343 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:00,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:10,456 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:20,491 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:30,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:40,561 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:50,598 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:30:50,637 - root - INFO - Generating performance report from CSV metrics data
2025-06-26 23:30:50,640 - root - INFO - Using asset count 14 for metrics filename
2025-06-26 23:30:50,642 - root - WARNING - No metrics file found at: Performance_Metrics\metrics_BestAsset_1d_1d_assets14_run_********_183748.csv
2025-06-26 23:30:50,675 - root - WARNING - No metrics data found
2025-06-26 23:31:00,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:31:10,659 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:31:20,706 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:31:30,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:31:40,751 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:31:50,804 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:32:00,837 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:32:10,877 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:32:20,887 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:32:30,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:32:40,938 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:32:51,021 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:33:01,055 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:33:11,071 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:33:21,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:33:31,161 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:33:41,175 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:33:51,229 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:34:01,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:34:11,281 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:34:21,296 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:34:31,365 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:34:41,407 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:34:51,436 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:35:01,471 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:35:11,513 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:35:21,537 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:35:31,581 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:35:41,595 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:35:51,650 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:36:01,721 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:36:11,738 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:36:21,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:36:31,892 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:36:41,909 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:36:51,926 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:37:01,994 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:37:12,014 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:37:22,070 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:37:32,106 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:37:42,154 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:37:52,209 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:38:02,263 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:38:13,373 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:38:23,920 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:38:34,316 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:38:44,605 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:38:58,575 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:39:09,386 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:39:19,403 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:39:29,416 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:39:39,438 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:39:49,455 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:39:59,562 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:40:09,594 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:40:19,632 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:40:29,663 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:40:39,704 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:40:49,731 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:40:59,752 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:41:09,805 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:41:19,817 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:41:29,874 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:41:39,913 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:41:49,927 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:41:59,942 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:42:09,964 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:42:20,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:42:30,090 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:42:40,125 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:42:50,138 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:43:00,158 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:43:10,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:43:20,261 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:43:30,301 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:43:40,333 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:43:50,360 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:44:00,376 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:44:10,440 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:44:20,458 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:44:30,506 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:44:40,547 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:44:50,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:45:00,617 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:45:10,654 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:45:20,686 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:45:30,725 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:45:40,758 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:45:50,794 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:46:00,807 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:46:10,863 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:46:20,896 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:46:30,935 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:46:40,969 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:46:51,001 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:47:01,142 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:47:11,173 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:47:21,211 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:47:31,248 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:47:41,284 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:47:51,317 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:48:01,335 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:48:11,350 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:48:21,364 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:48:31,382 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:48:41,495 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:48:51,528 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:49:01,566 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:49:11,602 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:49:21,621 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:49:31,670 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:49:41,707 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:49:51,724 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:50:01,774 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:50:11,811 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:50:21,847 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:50:31,983 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:50:41,997 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:50:52,049 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:51:02,088 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:51:12,221 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:51:22,262 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:51:32,290 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:51:42,332 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:51:52,361 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:52:02,404 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:52:12,442 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:52:22,454 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:52:32,507 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:52:42,520 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:52:52,539 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
2025-06-26 23:53:02,906 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/getUpdates "HTTP/1.1 200 OK"
