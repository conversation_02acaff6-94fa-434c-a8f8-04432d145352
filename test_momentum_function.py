#!/usr/bin/env python3
"""
Test the momentum function directly with the exact data from the violation case.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from momentum_scoring import find_top_n_assets_with_momentum
import logging

# Set up logging to see debug output
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(message)s')

def test_momentum_violation():
    """Test the momentum function with the exact violation case data."""
    
    print("=== TESTING MOMENTUM FUNCTION DIRECTLY ===")
    
    # Data from 2024-01-06 violation case
    current_scores = {
        'ETH/USDT': 1.0,
        'BTC/USDT': 2.0, 
        'SOL/USDT': 3.0,  # Tied with SUI
        'SUI/USDT': 3.0,  # Tied with SOL
        'XRP/USDT': 0.0
    }
    
    previous_scores = {
        'ETH/USDT': 1.0,
        'BTC/USDT': 2.0,
        'SOL/USDT': 4.0,  # Was higher, now declined
        'SUI/USDT': 3.0,  # Stayed same, better momentum
        'XRP/USDT': 0.0
    }
    
    print(f"Current scores: {current_scores}")
    print(f"Previous scores: {previous_scores}")
    
    # Calculate expected momentum
    print(f"\nExpected momentum calculation:")
    for asset in ['SOL/USDT', 'SUI/USDT']:
        curr = current_scores[asset]
        prev = previous_scores[asset]
        momentum = curr - prev
        print(f"{asset}: {prev} → {curr} (Δ={momentum:+.1f})")
    
    print(f"\nExpected result: SUI/USDT should be selected (better momentum: +0.0 > -1.0)")
    
    # Call the momentum function
    print(f"\n=== CALLING MOMENTUM FUNCTION ===")
    result = find_top_n_assets_with_momentum(
        current_scores=current_scores,
        previous_scores=previous_scores,
        n=1,
        mtpi_signal=1  # Bullish signal
    )
    
    print(f"\nActual result: {result}")
    
    if result == ['SUI/USDT']:
        print(f"✅ SUCCESS: Momentum function works correctly!")
    elif result == ['SOL/USDT']:
        print(f"❌ FAILURE: Momentum function selected wrong asset!")
        print(f"   This confirms the bug is in the momentum function logic")
    else:
        print(f"❓ UNEXPECTED: Got {result}")
    
    # Let's also test the sorting logic manually
    print(f"\n=== MANUAL SORTING TEST ===")
    
    asset_data = []
    for asset, current_score in current_scores.items():
        prev_score = previous_scores.get(asset, 0)
        momentum = current_score - prev_score
        asset_data.append({
            'asset': asset,
            'current_score': current_score,
            'prev_score': prev_score,
            'momentum': momentum
        })
    
    print(f"Asset data before sorting:")
    for data in asset_data:
        print(f"  {data['asset']}: score={data['current_score']}, momentum={data['momentum']:+.1f}")
    
    # Apply the same sorting logic as the function
    asset_order = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']
    
    def sort_key(data):
        asset = data['asset']
        order_index = asset_order.index(asset) if asset in asset_order else len(asset_order)
        key = (-data['current_score'], -data['momentum'], order_index)
        print(f"  Sort key for {asset}: {key}")
        return key
    
    print(f"\nSort keys:")
    sorted_assets = sorted(asset_data, key=sort_key)
    
    print(f"\nAsset data after sorting:")
    for i, data in enumerate(sorted_assets, 1):
        print(f"  {i}. {data['asset']}: score={data['current_score']}, momentum={data['momentum']:+.1f}")
    
    top_asset = sorted_assets[0]['asset']
    print(f"\nTop asset from manual sorting: {top_asset}")

if __name__ == "__main__":
    test_momentum_violation()
