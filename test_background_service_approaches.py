#!/usr/bin/env python3
"""
Test script to verify that background_service.py works correctly with all approaches:
- manual_inversion vs independent calculation methods
- momentum vs incumbent tie-breaking strategies
"""

import os
import sys
import logging
import tempfile
import shutil
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_logging():
    """Setup logging for the test."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def create_test_config(ratio_calculation='manual_inversion', tie_breaking_strategy='momentum'):
    """Create a temporary test configuration."""
    config_content = f"""settings:
  # Basic settings
  timeframe: "1d"
  mtpi_timeframe: "1d"
  use_mtpi_signal: true
  
  # Test assets (small set for faster testing)
  assets:
    - BTC/EUR
    - ETH/EUR
    - SOL/EUR
  
  # Strategy parameters
  n_assets: 1
  initial_capital: 10000
  transaction_fee_rate: 0.001
  
  # Test parameters
  ratio_calculation: "{ratio_calculation}"
  tie_breaking_strategy: "{tie_breaking_strategy}"
  
  # MTPI settings
  mtpi_indicators:
    combination_method: consensus
    enabled_indicators:
      - pgo
    long_threshold: 0.1
    short_threshold: -0.1
  
  # PGO parameters
  pgo_length: 35
  pgo_upper_threshold: 1.1
  pgo_lower_threshold: -0.58
  
  # Cache settings
  use_cache: true
  force_refresh_cache: false
  
  # Analysis settings
  start_date: "2023-10-20"
  
trading:
  enabled: false
  mode: paper
"""
    
    # Create temporary config file
    temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
    temp_config.write(config_content)
    temp_config.close()
    
    return temp_config.name

def test_background_service_approach(ratio_calculation, tie_breaking_strategy):
    """Test background service with specific approach."""
    print(f"\n{'='*60}")
    print(f"Testing: ratio_calculation={ratio_calculation}, tie_breaking_strategy={tie_breaking_strategy}")
    print(f"{'='*60}")
    
    # Create test config
    config_path = create_test_config(ratio_calculation, tie_breaking_strategy)
    
    try:
        # Import and test the background service
        from main_program import run_strategy_for_web
        
        print("Loading configuration...")
        from src.config_manager import load_config
        config = load_config(config_path)
        settings = config.get('settings', {})
        
        print(f"Configuration loaded:")
        print(f"  - ratio_calculation: {settings.get('ratio_calculation')}")
        print(f"  - tie_breaking_strategy: {settings.get('tie_breaking_strategy')}")
        print(f"  - assets: {settings.get('assets')}")
        
        print("\nRunning strategy...")
        
        # Run the strategy exactly like background_service.py does
        results = run_strategy_for_web(
            use_mtpi_signal=settings.get('use_mtpi_signal', True),
            mtpi_timeframe=settings.get('mtpi_timeframe', '1d'),
            timeframe=settings.get('timeframe', '1d'),
            analysis_start_date=settings.get('start_date', '2023-10-20'),
            selected_assets=settings.get('assets', ['BTC/EUR', 'ETH/EUR', 'SOL/EUR']),
            n_assets=settings.get('n_assets', 1),
            use_weighted_allocation=settings.get('use_weighted_allocation', False),
            weights=settings.get('weights', None),
            use_cache=True,
            force_refresh_cache=False,
            ratio_calculation=settings.get('ratio_calculation', 'manual_inversion'),
            tie_breaking_strategy=settings.get('tie_breaking_strategy', 'momentum'),
            config_path=config_path
        )
        
        if not results or 'error' in results:
            error_msg = results.get('error', 'Unknown error') if results else 'No results returned'
            print(f"✗ Test FAILED: {error_msg}")
            return False
        
        print("✓ Strategy execution completed successfully!")
        
        # Verify results structure
        expected_keys = ['strategy_equity', 'performance_metrics', 'best_asset_series']
        missing_keys = [key for key in expected_keys if key not in results]
        
        if missing_keys:
            print(f"✗ Test FAILED: Missing result keys: {missing_keys}")
            return False
        
        print("✓ Results structure verified!")
        
        # Check if we have performance metrics
        if 'performance_metrics' in results:
            metrics = results['performance_metrics']
            if 'total_return' in metrics:
                print(f"  - Total return: {metrics['total_return']:.2%}")
            if 'latest_scores' in metrics:
                print(f"  - Latest scores: {metrics['latest_scores']}")
        
        print(f"✓ Test PASSED for {ratio_calculation}/{tie_breaking_strategy}")
        return True
        
    except Exception as e:
        print(f"✗ Test FAILED with exception: {e}")
        logging.error(f"Exception details:", exc_info=True)
        return False
        
    finally:
        # Clean up temporary config file
        try:
            os.unlink(config_path)
        except:
            pass

def main():
    """Main test function."""
    setup_logging()
    
    print("Testing background_service.py with all approaches...")
    print(f"Test started at: {datetime.now()}")
    
    # Test all combinations
    test_cases = [
        ('manual_inversion', 'momentum'),
        ('manual_inversion', 'incumbent'),
        ('independent', 'momentum'),
        ('independent', 'incumbent')
    ]
    
    results = {}
    
    for ratio_calc, tie_breaking in test_cases:
        success = test_background_service_approach(ratio_calc, tie_breaking)
        results[f"{ratio_calc}/{tie_breaking}"] = success
    
    # Summary
    print(f"\n{'='*60}")
    print("TEST SUMMARY")
    print(f"{'='*60}")
    
    all_passed = True
    for test_name, success in results.items():
        status = "✓ PASSED" if success else "✗ FAILED"
        print(f"{test_name:30} {status}")
        if not success:
            all_passed = False
    
    print(f"\nOverall result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'}")
    print(f"Test completed at: {datetime.now()}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
