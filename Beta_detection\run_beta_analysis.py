#!/usr/bin/env python3
"""
Beta Detection System - Command Line Interface
Simple script to run beta analysis with various options.
"""

import sys
import os
import argparse
from typing import List, Optional

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from Beta_detection.beta_runner import BetaDetection<PERSON>unner
from Beta_detection.beta_config_manager import BetaConfigManager

def main():
    """Main command line interface for beta detection."""
    
    parser = argparse.ArgumentParser(
        description='Crypto Beta Detection System - Analyze asset volatility relative to BTC',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run standard analysis with default assets
  python run_beta_analysis.py
  
  # Run quick analysis for DeFi tokens
  python run_beta_analysis.py --group defi_focus --mode quick
  
  # Analyze specific assets
  python run_beta_analysis.py --symbols SOL/USDT ETH/USDT DOGE/USDT
  
  # Run comprehensive analysis since specific date
  python run_beta_analysis.py --mode comprehensive --since 2024-01-01
  
  # Show configuration and available groups
  python run_beta_analysis.py --show-config
        """
    )
    
    # Analysis options
    parser.add_argument('--symbols', nargs='+', 
                       help='Specific symbols to analyze (e.g., SOL/USDT ETH/USDT)')
    
    parser.add_argument('--group', type=str,
                       help='Use predefined symbol group (defi_focus, meme_focus, layer1_focus, ai_computing_focus, gaming_metaverse_focus, layer2_scaling_focus, infrastructure_focus, emerging_projects_focus, top_50_by_volume)')
    
    parser.add_argument('--mode', type=str, default='standard',
                       choices=['quick', 'standard', 'comprehensive'],
                       help='Analysis mode (default: standard)')
    
    parser.add_argument('--since', type=str,
                       help='Start date for analysis (YYYY-MM-DD format)')
    
    parser.add_argument('--length', type=int,
                       help='Beta measurement length in days (overrides config)')
    
    # Display options
    parser.add_argument('--no-save', action='store_true',
                       help='Do not save results to file')

    parser.add_argument('--no-comparison', action='store_true',
                       help='Do not show comparison with previous results')

    parser.add_argument('--no-insights', action='store_true',
                       help='Do not show additional insights')

    parser.add_argument('--gui', action='store_true',
                       help='Show results in GUI window for better display')
    
    # Configuration options
    parser.add_argument('--config', type=str,
                       help='Path to custom configuration file')
    
    parser.add_argument('--show-config', action='store_true',
                       help='Show current configuration and exit')
    
    parser.add_argument('--list-groups', action='store_true',
                       help='List available symbol groups and exit')
    
    # Verbose output
    parser.add_argument('-v', '--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Set up logging
    import logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # Initialize configuration manager
        config_manager = BetaConfigManager(args.config) if args.config else BetaConfigManager()
        
        # Handle configuration display
        if args.show_config:
            config_manager.print_config_summary()
            return
        
        # Handle group listing
        if args.list_groups:
            print("\n📋 Available Symbol Groups:")
            print("="*60)

            groups = [
                'default_symbols', 'defi_focus', 'meme_focus', 'layer1_focus',
                'ai_computing_focus', 'gaming_metaverse_focus', 'layer2_scaling_focus',
                'infrastructure_focus', 'emerging_projects_focus', 'top_50_by_volume'
            ]

            for group in groups:
                symbols = config_manager.get_symbol_group(group) if group != 'default_symbols' else config_manager.get_default_symbols()
                if symbols:
                    print(f"\n{group}:")
                    print(f"  Assets: {len(symbols)}")
                    if len(symbols) <= 10:
                        print(f"  Symbols: {', '.join(symbols)}")
                    else:
                        print(f"  Sample: {', '.join(symbols[:8])}... (+{len(symbols)-8} more)")

            print(f"\n💡 Usage examples:")
            print(f"  python Beta_detection/run_beta_analysis.py --group defi_focus")
            print(f"  python Beta_detection/run_beta_analysis.py --group top_50_by_volume --mode quick")
            print(f"  python Beta_detection/run_beta_analysis.py --group meme_focus --since 2024-01-01")

            return
        
        # Validate arguments
        if args.symbols and args.group:
            print("❌ Error: Cannot specify both --symbols and --group")
            sys.exit(1)
        
        # Initialize runner
        runner = BetaDetectionRunner(
            config_path=args.config,
            measurement_length=args.length
        )
        
        # Print startup info
        print("🚀 Starting Beta Detection Analysis")
        print("="*50)
        
        if args.group:
            print(f"Symbol Group: {args.group}")
        elif args.symbols:
            print(f"Custom Symbols: {len(args.symbols)} assets")
        else:
            print("Using default symbol list")
        
        print(f"Analysis Mode: {args.mode}")
        if args.since:
            print(f"Since Date: {args.since}")
        
        print("="*50)
        
        # Run analysis
        results = runner.run_beta_analysis(
            symbols=args.symbols,
            symbol_group=args.group,
            since=args.since,
            analysis_mode=args.mode,
            save_results=not args.no_save,
            show_previous_comparison=not args.no_comparison,
            show_gui=args.gui
        )
        
        if results:
            print(f"\n✅ Beta analysis completed successfully!")
            print(f"📊 Analyzed {len(results)} assets")
            
            # Show top/bottom performers
            sorted_results = sorted(results.items(), key=lambda x: x[1]['beta'], reverse=True)
            
            print(f"\n🔥 Highest Beta (Most Volatile):")
            for symbol, data in sorted_results[:3]:
                print(f"   {symbol}: {data['beta']:.3f}")
            
            print(f"\n🛡️  Lowest Beta (Least Volatile):")
            for symbol, data in sorted_results[-3:]:
                print(f"   {symbol}: {data['beta']:.3f}")
                
        else:
            print("\n❌ Beta analysis failed - no results generated")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n\n⚠️  Analysis interrupted by user")
        sys.exit(0)
    
    except Exception as e:
        print(f"\n❌ Error during analysis: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
