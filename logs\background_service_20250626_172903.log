2025-06-26 17:29:03,304 - root - INFO - Loaded environment variables from .env file
2025-06-26 17:29:04,154 - root - INFO - Loaded 45 trade records from logs/trades\trade_log_********.json
2025-06-26 17:29:04,155 - root - INFO - Loaded 27 asset selection records from logs/trades\asset_selection_********.json
2025-06-26 17:29:04,155 - root - INFO - Trade logger initialized with log directory: logs/trades
2025-06-26 17:29:05,064 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:05,071 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:05,076 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:05,084 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:05,084 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:05,091 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:05,092 - root - INFO - Loading notification configuration from config/notifications_bitvavo.json...
2025-06-26 17:29:05,092 - root - INFO - Notification configuration loaded successfully.
2025-06-26 17:29:05,982 - root - INFO - Telegram command handlers registered
2025-06-26 17:29:05,982 - root - INFO - Telegram bot polling started
2025-06-26 17:29:05,983 - root - INFO - Telegram notifier initialized with notification level: standard
2025-06-26 17:29:05,983 - root - INFO - Telegram notification channel initialized
2025-06-26 17:29:05,984 - root - INFO - Successfully loaded templates using utf-8 encoding
2025-06-26 17:29:05,985 - root - INFO - Loaded 24 templates from file
2025-06-26 17:29:05,985 - root - INFO - Notification manager initialized with 1 channels
2025-06-26 17:29:05,985 - root - INFO - Notification manager initialized
2025-06-26 17:29:05,986 - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-06-26 17:29:05,986 - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-06-26 17:29:05,986 - root - INFO - Set up critical time windows for 1d timeframe
2025-06-26 17:29:05,986 - root - INFO - Network watchdog initialized with 10s check interval
2025-06-26 17:29:05,991 - root - INFO - Loaded recovery state from data/state\recovery_state.json
2025-06-26 17:29:05,992 - root - INFO - No state file found at C:\Users\<USER>\OneDrive\Stalinis kompiuteris\Asset_Screener_Python\data\state\data/state\background_service_********_114325.json.json
2025-06-26 17:29:05,992 - root - INFO - Recovery manager initialized
2025-06-26 17:29:05,993 - root - ERROR - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-06-26 17:29:05,993 - root - ERROR - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'paper', 'order_type': 'market', 'position_size_pct': 10, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 10}, 'transaction_fee_rate': 0.0025}
2025-06-26 17:29:05,993 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:06,000 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:06,000 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:29:06,001 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:29:06,001 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:29:06,001 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:29:06,001 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:29:06,001 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:29:06,002 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:29:06,002 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:29:06,002 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:06,011 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:06,042 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getMe "HTTP/1.1 200 OK"
2025-06-26 17:29:06,054 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/deleteWebhook "HTTP/1.1 200 OK"
2025-06-26 17:29:06,057 - telegram.ext.Application - INFO - Application started
2025-06-26 17:29:06,278 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:29:06,278 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:29:06,279 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:29:06,279 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:29:06,279 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:29:06,279 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:06,286 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:06,290 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:06,296 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:06,296 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:29:06,304 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:06,305 - root - INFO - Loaded paper trading history from paper_trading_history.json
2025-06-26 17:29:06,305 - root - INFO - Paper trading initialized with EUR as quote currency
2025-06-26 17:29:06,305 - root - INFO - Trading executor initialized for bitvavo
2025-06-26 17:29:06,305 - root - INFO - Trading mode: paper
2025-06-26 17:29:06,306 - root - INFO - Trading enabled: True
2025-06-26 17:29:06,306 - root - INFO - Getting credentials for exchange_id: bitvavo
2025-06-26 17:29:06,306 - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-06-26 17:29:06,306 - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-06-26 17:29:06,306 - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-06-26 17:29:06,306 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:06,315 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:06,651 - root - INFO - Successfully loaded markets for bitvavo.
2025-06-26 17:29:06,652 - root - INFO - Trading enabled in paper mode
2025-06-26 17:29:06,653 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:29:06,654 - root - INFO - Reset paper trading account to initial balance: 100 EUR
2025-06-26 17:29:06,654 - root - INFO - Reset paper trading account to initial balance
2025-06-26 17:29:06,658 - root - INFO - Generated run ID: ********_172906
2025-06-26 17:29:06,658 - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-06-26 17:29:06,659 - root - INFO - Background service initialized
2025-06-26 17:29:06,662 - root - INFO - Network watchdog started
2025-06-26 17:29:06,662 - root - INFO - Network watchdog started
2025-06-26 17:29:06,663 - root - INFO - Schedule set up for 1d timeframe
2025-06-26 17:29:06,664 - root - INFO - Background service started
2025-06-26 17:29:06,664 - root - INFO - Executing strategy (run #1)...
2025-06-26 17:29:06,665 - root - INFO - Resetting daily trade counters for this strategy execution
2025-06-26 17:29:06,666 - root - INFO - No trades recorded today (Max: 5)
2025-06-26 17:29:06,667 - root - INFO - Creating snapshot for candle timestamp: ********
2025-06-26 17:29:06,734 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:29:12,683 - root - WARNING - Failed to send with Markdown formatting: Timed out
2025-06-26 17:29:12,745 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:29:12,747 - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-06-26 17:29:12,748 - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-06-26 17:29:12,748 - root - INFO - Using recent date for performance tracking: 2025-06-19
2025-06-26 17:29:12,751 - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-06-26 17:29:12,776 - root - INFO - Loaded 2137 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,777 - root - INFO - Last timestamp in cache for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,779 - root - INFO - Expected last timestamp for ETH/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,779 - root - INFO - Data is up to date for ETH/USDT
2025-06-26 17:29:12,779 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,794 - root - INFO - Loaded 1780 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,794 - root - INFO - Last timestamp in cache for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,795 - root - INFO - Expected last timestamp for SOL/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,795 - root - INFO - Data is up to date for SOL/USDT
2025-06-26 17:29:12,797 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,803 - root - INFO - Loaded 785 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,804 - root - INFO - Last timestamp in cache for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,804 - root - INFO - Expected last timestamp for SUI/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,804 - root - INFO - Data is up to date for SUI/USDT
2025-06-26 17:29:12,804 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,818 - root - INFO - Loaded 2137 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,819 - root - INFO - Last timestamp in cache for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,819 - root - INFO - Expected last timestamp for XRP/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,819 - root - INFO - Data is up to date for XRP/USDT
2025-06-26 17:29:12,821 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,834 - root - INFO - Loaded 1715 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,835 - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,835 - root - INFO - Expected last timestamp for AAVE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,836 - root - INFO - Data is up to date for AAVE/USDT
2025-06-26 17:29:12,836 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,848 - root - INFO - Loaded 1738 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,848 - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,849 - root - INFO - Expected last timestamp for AVAX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,849 - root - INFO - Data is up to date for AVAX/USDT
2025-06-26 17:29:12,850 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,865 - root - INFO - Loaded 2137 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,866 - root - INFO - Last timestamp in cache for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,866 - root - INFO - Expected last timestamp for ADA/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,866 - root - INFO - Data is up to date for ADA/USDT
2025-06-26 17:29:12,867 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,879 - root - INFO - Loaded 2137 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,879 - root - INFO - Last timestamp in cache for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,879 - root - INFO - Expected last timestamp for LINK/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,879 - root - INFO - Data is up to date for LINK/USDT
2025-06-26 17:29:12,881 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,898 - root - INFO - Loaded 2137 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,899 - root - INFO - Last timestamp in cache for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,899 - root - INFO - Expected last timestamp for TRX/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,899 - root - INFO - Data is up to date for TRX/USDT
2025-06-26 17:29:12,901 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,911 - root - INFO - Loaded 783 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,912 - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,912 - root - INFO - Expected last timestamp for PEPE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,913 - root - INFO - Data is up to date for PEPE/USDT
2025-06-26 17:29:12,913 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,930 - root - INFO - Loaded 2137 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,930 - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,930 - root - INFO - Expected last timestamp for DOGE/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,931 - root - INFO - Data is up to date for DOGE/USDT
2025-06-26 17:29:12,932 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,947 - root - INFO - Loaded 2137 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,947 - root - INFO - Last timestamp in cache for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,948 - root - INFO - Expected last timestamp for BNB/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,949 - root - INFO - Data is up to date for BNB/USDT
2025-06-26 17:29:12,949 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,961 - root - INFO - Loaded 1773 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:12,962 - root - INFO - Last timestamp in cache for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,962 - root - INFO - Expected last timestamp for DOT/USDT: 2025-06-25 00:00:00+00:00
2025-06-26 17:29:12,962 - root - INFO - Data is up to date for DOT/USDT
2025-06-26 17:29:12,963 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:12,966 - root - INFO - Using 13 trend assets (USDT) for analysis and 13 trading assets (EUR) for execution
2025-06-26 17:29:12,966 - root - INFO - MTPI Multi-Indicator Configuration:
2025-06-26 17:29:12,966 - root - INFO -   - Number of indicators: 8
2025-06-26 17:29:12,966 - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:29:12,966 - root - INFO -   - Combination method: consensus
2025-06-26 17:29:12,966 - root - INFO -   - Long threshold: 0.1
2025-06-26 17:29:12,966 - root - INFO -   - Short threshold: -0.1
2025-06-26 17:29:12,967 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:29:12,967 - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:29:12,967 - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: NOT FOUND
2025-06-26 17:29:12,967 - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 9
2025-06-26 17:29:12,967 - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-06-26 17:29:12,967 - root - INFO - Parameters: use_mtpi_signal=False, mtpi_indicator_type=PGO
2025-06-26 17:29:12,968 - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-06-26 17:29:12,968 - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-06-26 17:29:12,968 - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-06-26 17:29:12,968 - root - INFO - n_assets=1, use_weighted_allocation=False
2025-06-26 17:29:12,968 - root - INFO - Using provided trend method: PGO For Loop
2025-06-26 17:29:12,968 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:12,976 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:12,977 - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:12,983 - root - INFO - Configuration saved successfully.
2025-06-26 17:29:12,983 - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-06-26 17:29:12,983 - root - INFO - Number of trend detection assets: 13
2025-06-26 17:29:12,983 - root - INFO - Selected assets type: <class 'list'>
2025-06-26 17:29:12,983 - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:29:12,983 - root - INFO - Number of trading assets: 13
2025-06-26 17:29:12,983 - root - INFO - Trading assets type: <class 'list'>
2025-06-26 17:29:13,175 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:29:13,181 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:13,189 - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-06-26 17:29:13,196 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:13,196 - root - INFO - Execution context: backtesting
2025-06-26 17:29:13,196 - root - INFO - Execution timing: candle_close
2025-06-26 17:29:13,197 - root - INFO - Ratio calculation method: independent
2025-06-26 17:29:13,197 - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-06-26 17:29:13,197 - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:29:13,197 - root - INFO - MTPI combination method override: consensus
2025-06-26 17:29:13,197 - root - INFO - MTPI long threshold override: 0.1
2025-06-26 17:29:13,198 - root - INFO - MTPI short threshold override: -0.1
2025-06-26 17:29:13,198 - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-06-26 17:29:13,198 - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:29:13,199 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,199 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,200 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,200 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,200 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,201 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,201 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,201 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,202 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,202 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,202 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,203 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,203 - root - INFO - Loaded metadata for 48 assets
2025-06-26 17:29:13,203 - root - INFO - Checking cache for 13 symbols (1d)...
2025-06-26 17:29:13,219 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,220 - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,221 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,221 - root - INFO - Loaded 196 rows of ETH/USDT data from cache (after filtering).
2025-06-26 17:29:13,235 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,236 - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,237 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,237 - root - INFO - Loaded 196 rows of SOL/USDT data from cache (after filtering).
2025-06-26 17:29:13,246 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,247 - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,248 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,248 - root - INFO - Loaded 196 rows of SUI/USDT data from cache (after filtering).
2025-06-26 17:29:13,262 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,263 - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,263 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,263 - root - INFO - Loaded 196 rows of XRP/USDT data from cache (after filtering).
2025-06-26 17:29:13,276 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,277 - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,278 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,278 - root - INFO - Loaded 196 rows of AAVE/USDT data from cache (after filtering).
2025-06-26 17:29:13,289 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,292 - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,293 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,293 - root - INFO - Loaded 196 rows of AVAX/USDT data from cache (after filtering).
2025-06-26 17:29:13,305 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,308 - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,310 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,311 - root - INFO - Loaded 196 rows of ADA/USDT data from cache (after filtering).
2025-06-26 17:29:13,323 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,326 - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,326 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,327 - root - INFO - Loaded 196 rows of LINK/USDT data from cache (after filtering).
2025-06-26 17:29:13,339 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,344 - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,344 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,345 - root - INFO - Loaded 196 rows of TRX/USDT data from cache (after filtering).
2025-06-26 17:29:13,352 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,353 - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,354 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,354 - root - INFO - Loaded 196 rows of PEPE/USDT data from cache (after filtering).
2025-06-26 17:29:13,369 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,370 - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,371 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,371 - root - INFO - Loaded 196 rows of DOGE/USDT data from cache (after filtering).
2025-06-26 17:29:13,386 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,387 - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,388 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,388 - root - INFO - Loaded 196 rows of BNB/USDT data from cache (after filtering).
2025-06-26 17:29:13,401 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,402 - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-06-26 17:29:13,403 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,403 - root - INFO - Loaded 196 rows of DOT/USDT data from cache (after filtering).
2025-06-26 17:29:13,403 - root - INFO - All 13 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:29:13,404 - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,404 - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,404 - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,405 - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,405 - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,405 - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,406 - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,406 - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,406 - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,407 - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,407 - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,407 - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,407 - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-06-26 17:29:13,420 - root - INFO - Using standard MTPI warmup period of 120 days
2025-06-26 17:29:13,421 - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-06-26 17:29:13,421 - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-06-26 17:29:13,421 - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:29:13,421 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:29:13,429 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:13,429 - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-06-26 17:29:13,429 - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-06-26 17:29:13,431 - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-06-26 17:29:13,431 - root - INFO - Override: combination_method = consensus
2025-06-26 17:29:13,431 - root - INFO - Override: long_threshold = 0.1
2025-06-26 17:29:13,431 - root - INFO - Override: short_threshold = -0.1
2025-06-26 17:29:13,431 - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-06-26 17:29:13,432 - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-06-26 17:29:13,432 - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-06-26 17:29:13,432 - root - INFO - Checking cache for 1 symbols (1d)...
2025-06-26 17:29:13,446 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (last updated: 2025-06-26)
2025-06-26 17:29:13,446 - root - INFO - No incomplete daily candles to filter for current date 2025-06-26
2025-06-26 17:29:13,447 - root - INFO - Loaded 256 rows of BTC/USDT data from cache (after filtering).
2025-06-26 17:29:13,447 - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-06-26 17:29:13,447 - root - INFO - Fetched BTC data: 256 candles from 2024-10-13 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:13,447 - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-06-26 17:29:13,471 - root - INFO - Generated PGO Score signals: {-1: 104, 0: 34, 1: 118}
2025-06-26 17:29:13,471 - root - INFO - Generated pgo signals: 256 values
2025-06-26 17:29:13,472 - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-06-26 17:29:13,472 - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-06-26 17:29:13,482 - root - INFO - Generated BB Score signals: {-1: 106, 0: 32, 1: 118}
2025-06-26 17:29:13,482 - root - INFO - Generated Bollinger Band signals: 256 values
2025-06-26 17:29:13,483 - root - INFO - Generated bollinger_bands signals: 256 values
2025-06-26 17:29:13,833 - root - INFO - Generated DWMA signals using Weighted SD method
2025-06-26 17:29:13,833 - root - INFO - Generated dwma_score signals: 256 values
2025-06-26 17:29:13,873 - root - INFO - Generated DEMA Supertrend signals
2025-06-26 17:29:13,874 - root - INFO - Signal distribution: {-1: 142, 0: 1, 1: 113}
2025-06-26 17:29:13,874 - root - INFO - Generated DEMA Super Score signals
2025-06-26 17:29:13,874 - root - INFO - Generated dema_super_score signals: 256 values
2025-06-26 17:29:13,947 - root - INFO - Generated DPSD signals
2025-06-26 17:29:13,947 - root - INFO - Signal distribution: {-1: 98, 0: 87, 1: 71}
2025-06-26 17:29:13,948 - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-06-26 17:29:13,948 - root - INFO - Generated dpsd_score signals: 256 values
2025-06-26 17:29:13,954 - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-06-26 17:29:13,954 - root - INFO - Generated AAD Score signals using SMA method
2025-06-26 17:29:13,954 - root - INFO - Generated aad_score signals: 256 values
2025-06-26 17:29:14,001 - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-06-26 17:29:14,002 - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-06-26 17:29:14,002 - root - INFO - Generated dynamic_ema_score signals: 256 values
2025-06-26 17:29:14,079 - root - INFO - Generated quantile_dema_score signals: 256 values
2025-06-26 17:29:14,085 - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-06-26 17:29:14,085 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:29:14,086 - root - INFO - Generated combined MTPI signals: 256 values using consensus method
2025-06-26 17:29:14,086 - root - INFO - Signal distribution: {1: 145, -1: 110, 0: 1}
2025-06-26 17:29:14,086 - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-06-26 17:29:14,089 - root - INFO - Saving configuration to config/settings.yaml...
2025-06-26 17:29:14,096 - root - INFO - Configuration saved successfully.
2025-06-26 17:29:14,096 - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-06-26 17:29:14,096 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:29:14,103 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:14,103 - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-06-26 17:29:14,103 - root - INFO - Calculating ratio PGO signals for 13 assets with PGO(35) using full OHLCV data
2025-06-26 17:29:14,104 - root - INFO - Using ratio calculation method: independent
2025-06-26 17:29:14,124 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:14,125 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,140 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:14,146 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:14,163 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:29:14,164 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,181 - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-06-26 17:29:14,186 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:14,202 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:14,203 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,216 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:14,222 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:14,241 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:29:14,242 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,255 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:29:14,263 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:14,279 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:29:14,279 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,293 - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-06-26 17:29:14,298 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:14,314 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:29:14,314 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,328 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:29:14,332 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:14,349 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:29:14,349 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,362 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:29:14,367 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:14,384 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:29:14,384 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,398 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:29:14,401 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:14,421 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:29:14,421 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,436 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:29:14,442 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:14,458 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:14,459 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,471 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:14,475 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:14,492 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,512 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:14,529 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:29:14,531 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,543 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:29:14,551 - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:14,561 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:29:14,561 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,575 - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-06-26 17:29:14,585 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:14,593 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:14,601 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,611 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:14,619 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:14,635 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:14,637 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,651 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:14,657 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:14,673 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:29:14,673 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,687 - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-06-26 17:29:14,694 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:14,714 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:29:14,714 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,731 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:29:14,737 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:14,757 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:14,759 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,775 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:14,781 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:14,799 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:14,799 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,816 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:14,824 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:14,842 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:29:14,842 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,861 - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-06-26 17:29:14,863 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:14,881 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:29:14,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,893 - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-06-26 17:29:14,901 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:14,923 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:14,923 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,938 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:14,942 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:14,959 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:14,959 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:14,973 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:14,975 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:14,994 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:14,994 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,009 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:15,012 - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:15,029 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:15,029 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,042 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:15,049 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:15,061 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,081 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:15,094 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,116 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:15,131 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:29:15,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,142 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:29:15,152 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:15,169 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,187 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:15,203 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,226 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:15,242 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,243 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,256 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,261 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:15,276 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,298 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:15,315 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,332 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:15,347 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:15,347 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,361 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:15,368 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:15,385 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,409 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:15,425 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,442 - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:15,458 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:29:15,458 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,474 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:29:15,475 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:15,495 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,511 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:15,531 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:15,531 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,541 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:15,541 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:15,584 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,586 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,609 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,613 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:15,631 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:29:15,631 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,642 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:29:15,652 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:15,666 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:29:15,666 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,681 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:29:15,686 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:15,701 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,701 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,718 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,725 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:15,741 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:15,741 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,753 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:15,761 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:15,775 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,777 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,791 - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-06-26 17:29:15,796 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:15,811 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,831 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:15,843 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,861 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:15,881 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:29:15,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,896 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:29:15,901 - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:15,919 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,942 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:15,961 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:15,981 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:16,004 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,029 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:16,049 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,068 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:16,082 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:16,082 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,101 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:16,102 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:29:16,107 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:16,125 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:16,125 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,139 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:16,142 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:16,161 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:29:16,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,175 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:29:16,175 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:16,192 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,213 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:16,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,246 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:16,263 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:29:16,263 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,275 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:29:16,281 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:16,296 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,317 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:16,331 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:16,331 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,342 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:16,352 - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:16,369 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,391 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:16,407 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,426 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:16,442 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,467 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:16,481 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,496 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:16,511 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:29:16,511 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,532 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:29:16,537 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:16,552 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,574 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:16,591 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:29:16,591 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,601 - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-06-26 17:29:16,608 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:16,626 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,642 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:16,661 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,675 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:16,696 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,711 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:16,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,746 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:16,762 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,781 - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:16,797 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:16,797 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,811 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:16,811 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:16,832 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,851 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:16,866 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:29:16,866 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,881 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:29:16,885 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:16,901 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,918 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:16,943 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:29:16,943 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:16,963 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:29:16,971 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:16,997 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:29:16,997 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,022 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:29:17,029 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:17,058 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:29:17,059 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,083 - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-06-26 17:29:17,091 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:17,108 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,131 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:17,148 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:17,148 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,161 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:17,173 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:17,194 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:17,194 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,211 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:17,211 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:17,231 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,253 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:17,273 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:17,274 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,285 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:17,294 - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:17,311 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,325 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:17,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,364 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:17,381 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,398 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:17,416 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,435 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:17,453 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:29:17,453 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,466 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:29:17,471 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:17,489 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,511 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:17,526 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,544 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:17,561 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,575 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:17,592 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:17,592 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,611 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:17,612 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:17,625 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,642 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:17,661 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,681 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:17,698 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,716 - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:17,731 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,751 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:17,765 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:17,765 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,781 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:17,785 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:17,801 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,824 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:17,841 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:17,841 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,853 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:17,858 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:17,875 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:17,875 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,891 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:17,896 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:17,911 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:29:17,911 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,925 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:29:17,931 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:17,946 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,961 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:17,981 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:17,981 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:17,992 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:18,002 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:18,012 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:18,021 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,035 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:18,041 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:18,056 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,075 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:18,091 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,111 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:18,125 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,142 - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:18,161 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,181 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:18,192 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,211 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:18,226 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,251 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:18,261 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,289 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:18,309 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:29:18,309 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,325 - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-06-26 17:29:18,331 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:18,349 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:29:18,349 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,366 - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-06-26 17:29:18,374 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:18,392 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:18,392 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,411 - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-06-26 17:29:18,414 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:18,435 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:29:18,435 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,443 - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-06-26 17:29:18,455 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:18,474 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,492 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:18,510 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,529 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:18,542 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,561 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:18,575 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,592 - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:18,611 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,625 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:18,642 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,661 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:18,675 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:18,675 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,692 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:18,692 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:18,711 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:18,711 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,725 - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-06-26 17:29:18,731 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:18,746 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:18,746 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,759 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:18,761 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:18,775 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:29:18,775 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,792 - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-06-26 17:29:18,792 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:18,816 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:29:18,816 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,831 - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-06-26 17:29:18,835 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:18,846 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:18,846 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,861 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:18,861 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:18,881 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:29:18,881 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,896 - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-06-26 17:29:18,901 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:18,919 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:29:18,919 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,931 - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-06-26 17:29:18,934 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:18,956 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:18,976 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:18,992 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:18,993 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,006 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:19,012 - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:19,029 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,042 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:19,061 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,063 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,076 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,081 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:19,092 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:19,092 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,111 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:19,116 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:19,131 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:19,131 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,142 - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-06-26 17:29:19,151 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:19,166 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:29:19,166 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,181 - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-06-26 17:29:19,184 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:19,201 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:29:19,201 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,211 - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-06-26 17:29:19,211 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:19,235 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,235 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,242 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,251 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:19,272 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:29:19,274 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,286 - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-06-26 17:29:19,292 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:19,308 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:19,308 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,322 - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-06-26 17:29:19,325 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:19,342 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,342 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,359 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,361 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:19,375 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,392 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:19,413 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,431 - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-06-26 17:29:19,449 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,470 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-06-26 17:29:19,492 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,516 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-06-26 17:29:19,537 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:19,537 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,552 - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-06-26 17:29:19,558 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-06-26 17:29:19,575 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:29:19,575 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,592 - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-06-26 17:29:19,600 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-06-26 17:29:19,613 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:29:19,613 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,631 - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-06-26 17:29:19,635 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-06-26 17:29:19,651 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:29:19,651 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,668 - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-06-26 17:29:19,674 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-06-26 17:29:19,690 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:29:19,691 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,701 - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-06-26 17:29:19,708 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-06-26 17:29:19,724 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:29:19,725 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,735 - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-06-26 17:29:19,742 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-06-26 17:29:19,758 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,775 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-06-26 17:29:19,791 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,791 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,801 - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-06-26 17:29:19,811 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-06-26 17:29:19,825 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,844 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-06-26 17:29:19,861 - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-06-26 17:29:19,881 - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-06-26 17:29:21,352 - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-06-26 17:29:21,352 - root - INFO - Finished calculating daily scores. DataFrame shape: (196, 13)
2025-06-26 17:29:21,353 - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering DISABLED
2025-06-26 17:29:21,356 - root - INFO - Date ranges for each asset:
2025-06-26 17:29:21,357 - root - INFO -   ETH/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,358 - root - INFO -   SOL/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,358 - root - INFO -   SUI/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,358 - root - INFO -   XRP/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,358 - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,358 - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,358 - root - INFO -   ADA/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO -   LINK/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO -   TRX/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO -   BNB/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO -   DOT/USDT: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,359 - root - INFO - Common dates range: 2024-12-12 to 2025-06-25 (196 candles)
2025-06-26 17:29:21,360 - root - INFO - Analysis will run from: 2025-02-10 to 2025-06-25 (136 candles)
2025-06-26 17:29:21,364 - root - INFO - EXECUTION TIMING VERIFICATION:
2025-06-26 17:29:21,365 - root - INFO -    Execution Method: candle_close
2025-06-26 17:29:21,365 - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-06-26 17:29:21,365 - root - INFO -    Signal generated and executed immediately
2025-06-26 17:29:21,370 - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,370 - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,370 - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,370 - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,371 - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,372 - root - INFO - ASSET CHANGE DETECTED on 2025-02-11:
2025-06-26 17:29:21,372 - root - INFO -    Signal Date: 2025-02-10 (generated at 00:00 UTC)
2025-06-26 17:29:21,372 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-11 00:00 UTC (immediate)
2025-06-26 17:29:21,372 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,372 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:29:21,372 - root - INFO -    TRX/USDT buy price: $0.2410 (close price)
2025-06-26 17:29:21,374 - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,375 - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,375 - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,375 - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,375 - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,375 - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,375 - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,376 - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,376 - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,376 - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,376 - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,376 - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,376 - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,377 - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,377 - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,378 - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,378 - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,378 - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,378 - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,379 - root - INFO - ASSET CHANGE DETECTED on 2025-02-13:
2025-06-26 17:29:21,379 - root - INFO -    Signal Date: 2025-02-12 (generated at 00:00 UTC)
2025-06-26 17:29:21,379 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-13 00:00 UTC (immediate)
2025-06-26 17:29:21,379 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,379 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:29:21,381 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:29:21,381 - root - INFO -    BNB/USDT buy price: $664.7200 (close price)
2025-06-26 17:29:21,383 - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,383 - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,383 - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,383 - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,383 - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,384 - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,385 - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,385 - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,385 - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,385 - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,386 - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,387 - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,387 - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-06-26 17:29:21,390 - root - INFO - ASSET CHANGE DETECTED on 2025-02-19:
2025-06-26 17:29:21,391 - root - INFO -    Signal Date: 2025-02-18 (generated at 00:00 UTC)
2025-06-26 17:29:21,391 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-19 00:00 UTC (immediate)
2025-06-26 17:29:21,392 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,392 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:29:21,392 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:29:21,392 - root - INFO -    TRX/USDT buy price: $0.2424 (close price)
2025-06-26 17:29:21,396 - root - INFO - ASSET CHANGE DETECTED on 2025-02-23:
2025-06-26 17:29:21,396 - root - INFO -    Signal Date: 2025-02-22 (generated at 00:00 UTC)
2025-06-26 17:29:21,397 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-23 00:00 UTC (immediate)
2025-06-26 17:29:21,397 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,397 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:29:21,397 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:29:21,397 - root - INFO -    BNB/USDT buy price: $658.4200 (close price)
2025-06-26 17:29:21,398 - root - INFO - ASSET CHANGE DETECTED on 2025-02-24:
2025-06-26 17:29:21,398 - root - INFO -    Signal Date: 2025-02-23 (generated at 00:00 UTC)
2025-06-26 17:29:21,398 - root - INFO -    AUTOMATIC EXECUTION: 2025-02-24 00:00 UTC (immediate)
2025-06-26 17:29:21,398 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,399 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:29:21,399 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:29:21,399 - root - INFO -    TRX/USDT buy price: $0.2401 (close price)
2025-06-26 17:29:21,401 - root - INFO - ASSET CHANGE DETECTED on 2025-03-03:
2025-06-26 17:29:21,401 - root - INFO -    Signal Date: 2025-03-02 (generated at 00:00 UTC)
2025-06-26 17:29:21,401 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-03 00:00 UTC (immediate)
2025-06-26 17:29:21,401 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,401 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:29:21,403 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:29:21,403 - root - INFO -    ADA/USDT buy price: $0.8578 (close price)
2025-06-26 17:29:21,405 - root - INFO - ASSET CHANGE DETECTED on 2025-03-11:
2025-06-26 17:29:21,406 - root - INFO -    Signal Date: 2025-03-10 (generated at 00:00 UTC)
2025-06-26 17:29:21,406 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-11 00:00 UTC (immediate)
2025-06-26 17:29:21,406 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,407 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:29:21,408 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:29:21,408 - root - INFO -    TRX/USDT buy price: $0.2244 (close price)
2025-06-26 17:29:21,411 - root - INFO - ASSET CHANGE DETECTED on 2025-03-15:
2025-06-26 17:29:21,411 - root - INFO -    Signal Date: 2025-03-14 (generated at 00:00 UTC)
2025-06-26 17:29:21,412 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-15 00:00 UTC (immediate)
2025-06-26 17:29:21,412 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,412 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:29:21,412 - root - INFO -    Buying: ['ADA/USDT']
2025-06-26 17:29:21,413 - root - INFO -    ADA/USDT buy price: $0.7468 (close price)
2025-06-26 17:29:21,414 - root - INFO - ASSET CHANGE DETECTED on 2025-03-17:
2025-06-26 17:29:21,414 - root - INFO -    Signal Date: 2025-03-16 (generated at 00:00 UTC)
2025-06-26 17:29:21,415 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-17 00:00 UTC (immediate)
2025-06-26 17:29:21,415 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,415 - root - INFO -    Selling: ['ADA/USDT']
2025-06-26 17:29:21,415 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:29:21,415 - root - INFO -    BNB/USDT buy price: $631.6900 (close price)
2025-06-26 17:29:21,416 - root - INFO - ASSET CHANGE DETECTED on 2025-03-20:
2025-06-26 17:29:21,416 - root - INFO -    Signal Date: 2025-03-19 (generated at 00:00 UTC)
2025-06-26 17:29:21,416 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-20 00:00 UTC (immediate)
2025-06-26 17:29:21,417 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,417 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:29:21,417 - root - INFO -    Buying: ['XRP/USDT']
2025-06-26 17:29:21,417 - root - INFO -    XRP/USDT buy price: $2.4361 (close price)
2025-06-26 17:29:21,418 - root - INFO - ASSET CHANGE DETECTED on 2025-03-22:
2025-06-26 17:29:21,418 - root - INFO -    Signal Date: 2025-03-21 (generated at 00:00 UTC)
2025-06-26 17:29:21,418 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-22 00:00 UTC (immediate)
2025-06-26 17:29:21,418 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,418 - root - INFO -    Selling: ['XRP/USDT']
2025-06-26 17:29:21,418 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:29:21,419 - root - INFO -    BNB/USDT buy price: $627.0100 (close price)
2025-06-26 17:29:21,420 - root - INFO - ASSET CHANGE DETECTED on 2025-03-26:
2025-06-26 17:29:21,420 - root - INFO -    Signal Date: 2025-03-25 (generated at 00:00 UTC)
2025-06-26 17:29:21,420 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-26 00:00 UTC (immediate)
2025-06-26 17:29:21,421 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,421 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:29:21,421 - root - INFO -    Buying: ['AVAX/USDT']
2025-06-26 17:29:21,421 - root - INFO -    AVAX/USDT buy price: $22.0400 (close price)
2025-06-26 17:29:21,426 - root - INFO - ASSET CHANGE DETECTED on 2025-03-27:
2025-06-26 17:29:21,426 - root - INFO -    Signal Date: 2025-03-26 (generated at 00:00 UTC)
2025-06-26 17:29:21,426 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-27 00:00 UTC (immediate)
2025-06-26 17:29:21,426 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,426 - root - INFO -    Selling: ['AVAX/USDT']
2025-06-26 17:29:21,427 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:29:21,427 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:29:21,429 - root - INFO - ASSET CHANGE DETECTED on 2025-03-31:
2025-06-26 17:29:21,429 - root - INFO -    Signal Date: 2025-03-30 (generated at 00:00 UTC)
2025-06-26 17:29:21,430 - root - INFO -    AUTOMATIC EXECUTION: 2025-03-31 00:00 UTC (immediate)
2025-06-26 17:29:21,430 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,431 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:29:21,431 - root - INFO -    Buying: ['BNB/USDT']
2025-06-26 17:29:21,431 - root - INFO -    BNB/USDT buy price: $604.8100 (close price)
2025-06-26 17:29:21,432 - root - INFO - ASSET CHANGE DETECTED on 2025-04-01:
2025-06-26 17:29:21,433 - root - INFO -    Signal Date: 2025-03-31 (generated at 00:00 UTC)
2025-06-26 17:29:21,433 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-01 00:00 UTC (immediate)
2025-06-26 17:29:21,433 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,433 - root - INFO -    Selling: ['BNB/USDT']
2025-06-26 17:29:21,433 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:29:21,433 - root - INFO -    TRX/USDT buy price: $0.2378 (close price)
2025-06-26 17:29:21,444 - root - INFO - ASSET CHANGE DETECTED on 2025-04-19:
2025-06-26 17:29:21,444 - root - INFO -    Signal Date: 2025-04-18 (generated at 00:00 UTC)
2025-06-26 17:29:21,445 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-19 00:00 UTC (immediate)
2025-06-26 17:29:21,445 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,446 - root - INFO -    Selling: ['TRX/USDT']
2025-06-26 17:29:21,446 - root - INFO -    Buying: ['SOL/USDT']
2025-06-26 17:29:21,447 - root - INFO -    SOL/USDT buy price: $139.8700 (close price)
2025-06-26 17:29:21,452 - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-06-26 17:29:21,452 - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-06-26 17:29:21,452 - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-06-26 17:29:21,452 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,452 - root - INFO -    Selling: ['SOL/USDT']
2025-06-26 17:29:21,452 - root - INFO -    Buying: ['SUI/USDT']
2025-06-26 17:29:21,453 - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-06-26 17:29:21,462 - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-06-26 17:29:21,463 - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-06-26 17:29:21,463 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-06-26 17:29:21,463 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,463 - root - INFO -    Selling: ['SUI/USDT']
2025-06-26 17:29:21,464 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:29:21,464 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:29:21,469 - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-06-26 17:29:21,469 - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-06-26 17:29:21,470 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-06-26 17:29:21,470 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,470 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:29:21,470 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:29:21,470 - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-06-26 17:29:21,472 - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-06-26 17:29:21,473 - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-06-26 17:29:21,475 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-06-26 17:29:21,475 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,475 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:29:21,477 - root - INFO -    Buying: ['PEPE/USDT']
2025-06-26 17:29:21,477 - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-06-26 17:29:21,478 - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-06-26 17:29:21,478 - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-06-26 17:29:21,479 - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-06-26 17:29:21,479 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,479 - root - INFO -    Selling: ['PEPE/USDT']
2025-06-26 17:29:21,479 - root - INFO -    Buying: ['AAVE/USDT']
2025-06-26 17:29:21,479 - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-06-26 17:29:21,496 - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-06-26 17:29:21,497 - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-06-26 17:29:21,497 - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-06-26 17:29:21,497 - root - INFO -    Execution Delay: 0 hours
2025-06-26 17:29:21,497 - root - INFO -    Selling: ['AAVE/USDT']
2025-06-26 17:29:21,497 - root - INFO -    Buying: ['TRX/USDT']
2025-06-26 17:29:21,498 - root - INFO -    TRX/USDT buy price: $0.2710 (close price)
2025-06-26 17:29:21,545 - root - INFO - Entry trade at 2025-02-11 00:00:00+00:00: TRX/USDT
2025-06-26 17:29:21,545 - root - INFO - Swap trade at 2025-02-13 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:29:21,545 - root - INFO - Swap trade at 2025-02-19 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:29:21,546 - root - INFO - Swap trade at 2025-02-23 00:00:00+00:00: TRX/USDT -> BNB/USDT
2025-06-26 17:29:21,546 - root - INFO - Swap trade at 2025-02-24 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:29:21,546 - root - INFO - Swap trade at 2025-03-03 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:29:21,546 - root - INFO - Swap trade at 2025-03-11 00:00:00+00:00: ADA/USDT -> TRX/USDT
2025-06-26 17:29:21,546 - root - INFO - Swap trade at 2025-03-15 00:00:00+00:00: TRX/USDT -> ADA/USDT
2025-06-26 17:29:21,546 - root - INFO - Swap trade at 2025-03-17 00:00:00+00:00: ADA/USDT -> BNB/USDT
2025-06-26 17:29:21,547 - root - INFO - Swap trade at 2025-03-20 00:00:00+00:00: BNB/USDT -> XRP/USDT
2025-06-26 17:29:21,547 - root - INFO - Swap trade at 2025-03-22 00:00:00+00:00: XRP/USDT -> BNB/USDT
2025-06-26 17:29:21,547 - root - INFO - Swap trade at 2025-03-26 00:00:00+00:00: BNB/USDT -> AVAX/USDT
2025-06-26 17:29:21,547 - root - INFO - Swap trade at 2025-03-27 00:00:00+00:00: AVAX/USDT -> PEPE/USDT
2025-06-26 17:29:21,547 - root - INFO - Swap trade at 2025-03-31 00:00:00+00:00: PEPE/USDT -> BNB/USDT
2025-06-26 17:29:21,547 - root - INFO - Swap trade at 2025-04-01 00:00:00+00:00: BNB/USDT -> TRX/USDT
2025-06-26 17:29:21,548 - root - INFO - Swap trade at 2025-04-19 00:00:00+00:00: TRX/USDT -> SOL/USDT
2025-06-26 17:29:21,548 - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-06-26 17:29:21,548 - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-06-26 17:29:21,548 - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:29:21,548 - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-06-26 17:29:21,548 - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-06-26 17:29:21,549 - root - INFO - Swap trade at 2025-06-21 00:00:00+00:00: AAVE/USDT -> TRX/USDT
2025-06-26 17:29:21,549 - root - INFO - Total trades: 22 (Entries: 1, Exits: 0, Swaps: 21)
2025-06-26 17:29:21,549 - root - INFO - Strategy execution completed in 0s
2025-06-26 17:29:21,549 - root - INFO - DEBUG: self.elapsed_time = 0.19630050659179688 seconds
2025-06-26 17:29:21,552 - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-06-26 17:29:21,552 - root - INFO - Assets included in buy-and-hold comparison:
2025-06-26 17:29:21,552 - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-06-26 17:29:21,552 - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-06-26 17:29:21,552 - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-06-26 17:29:21,552 - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-06-26 17:29:21,552 - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-06-26 17:29:21,553 - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-06-26 17:29:21,559 - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,561 - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,563 - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,564 - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,565 - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,566 - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,567 - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,568 - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,568 - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,569 - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,570 - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,576 - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,578 - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-06-26 17:29:21,579 - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 196 points
2025-06-26 17:29:21,579 - root - INFO - ETH/USDT B&H total return: -9.12%
2025-06-26 17:29:21,582 - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 196 points
2025-06-26 17:29:21,582 - root - INFO - SOL/USDT B&H total return: -28.38%
2025-06-26 17:29:21,583 - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 196 points
2025-06-26 17:29:21,583 - root - INFO - SUI/USDT B&H total return: -15.06%
2025-06-26 17:29:21,585 - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 196 points
2025-06-26 17:29:21,585 - root - INFO - XRP/USDT B&H total return: -9.81%
2025-06-26 17:29:21,587 - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 196 points
2025-06-26 17:29:21,587 - root - INFO - AAVE/USDT B&H total return: 1.32%
2025-06-26 17:29:21,590 - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 196 points
2025-06-26 17:29:21,594 - root - INFO - AVAX/USDT B&H total return: -31.55%
2025-06-26 17:29:21,596 - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 196 points
2025-06-26 17:29:21,597 - root - INFO - ADA/USDT B&H total return: -20.36%
2025-06-26 17:29:21,598 - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 196 points
2025-06-26 17:29:21,599 - root - INFO - LINK/USDT B&H total return: -30.20%
2025-06-26 17:29:21,600 - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 196 points
2025-06-26 17:29:21,600 - root - INFO - TRX/USDT B&H total return: 10.93%
2025-06-26 17:29:21,601 - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 196 points
2025-06-26 17:29:21,601 - root - INFO - PEPE/USDT B&H total return: -1.36%
2025-06-26 17:29:21,603 - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 196 points
2025-06-26 17:29:21,603 - root - INFO - DOGE/USDT B&H total return: -35.56%
2025-06-26 17:29:21,604 - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 196 points
2025-06-26 17:29:21,607 - root - INFO - BNB/USDT B&H total return: 4.43%
2025-06-26 17:29:21,612 - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 196 points
2025-06-26 17:29:21,612 - root - INFO - DOT/USDT B&H total return: -30.82%
2025-06-26 17:29:21,613 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:29:21,620 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:21,633 - root - INFO - Using colored segments for single-asset strategy visualization
2025-06-26 17:29:21,739 - root - INFO - Loading configuration from config/settings.yaml...
2025-06-26 17:29:21,746 - root - INFO - Configuration loaded successfully.
2025-06-26 17:29:23,056 - root - INFO - Added ETH/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,057 - root - INFO - Added SOL/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,057 - root - INFO - Added SUI/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,057 - root - INFO - Added XRP/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,057 - root - INFO - Added AAVE/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,057 - root - INFO - Added AVAX/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,058 - root - INFO - Added ADA/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,058 - root - INFO - Added LINK/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,058 - root - INFO - Added TRX/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,058 - root - INFO - Added PEPE/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,058 - root - INFO - Added DOGE/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,058 - root - INFO - Added BNB/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,059 - root - INFO - Added DOT/USDT buy-and-hold curve with 196 points
2025-06-26 17:29:23,059 - root - INFO - Added 13 buy-and-hold curves to results
2025-06-26 17:29:23,059 - root - INFO -   - ETH/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,059 - root - INFO -   - SOL/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,059 - root - INFO -   - SUI/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,059 - root - INFO -   - XRP/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,061 - root - INFO -   - AAVE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,061 - root - INFO -   - AVAX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,061 - root - INFO -   - ADA/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,062 - root - INFO -   - LINK/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,062 - root - INFO -   - TRX/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,062 - root - INFO -   - PEPE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,063 - root - INFO -   - DOGE/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,063 - root - INFO -   - BNB/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,063 - root - INFO -   - DOT/USDT: 196 points from 2024-12-12 00:00:00+00:00 to 2025-06-25 00:00:00+00:00
2025-06-26 17:29:23,076 - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-06-26 17:29:23,077 - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-06-26 17:29:23,079 - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-06-26 17:29:23,082 - root - INFO - MTPI disabled - skipping MTPI signal calculation
2025-06-26 17:29:23,085 - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-06-26 17:29:23,085 - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-06-26 17:29:23,122 - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:29:23,131 - root - INFO - Saved metrics to new file: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_172906.csv
2025-06-26 17:29:23,131 - root - INFO - Saved performance metrics to CSV: Performance_Metrics\metrics_BestAsset_1d_1d_assets13_since_20250619_run_********_172906.csv
2025-06-26 17:29:23,131 - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-06-26 17:29:23,132 - root - INFO - Results type: <class 'dict'>
2025-06-26 17:29:23,132 - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-06-26 17:29:23,132 - root - INFO - Success flag set to: True
2025-06-26 17:29:23,132 - root - INFO - Message set to: Strategy calculation completed successfully
2025-06-26 17:29:23,132 - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:29:23,132 - root - INFO -   - buy_hold_curves: dict with 13 entries
2025-06-26 17:29:23,133 - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 196 entries
2025-06-26 17:29:23,133 - root - INFO -   - mtpi_signals: <class 'NoneType'>
2025-06-26 17:29:23,133 - root - INFO -   - mtpi_score: <class 'NoneType'>
2025-06-26 17:29:23,133 - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 196 entries
2025-06-26 17:29:23,133 - root - INFO -   - performance_metrics: dict with 3 entries
2025-06-26 17:29:23,133 - root - INFO -   - metrics_file: <class 'str'>
2025-06-26 17:29:23,133 - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-06-26 17:29:23,133 - root - INFO -   - success: <class 'bool'>
2025-06-26 17:29:23,134 - root - INFO -   - message: <class 'str'>
2025-06-26 17:29:23,134 - root - INFO - MTPI disabled - using default bullish signal (1) for trading execution
2025-06-26 17:29:23,134 - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-06-26 17:29:23,134 - root - ERROR - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: TRX/EUR
2025-06-26 17:29:23,135 - root - ERROR - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-06-21 00:00:00+00:00    TRX/EUR
2025-06-22 00:00:00+00:00    TRX/EUR
2025-06-23 00:00:00+00:00    TRX/EUR
2025-06-24 00:00:00+00:00    TRX/EUR
2025-06-25 00:00:00+00:00    TRX/EUR
dtype: object
2025-06-26 17:29:23,135 - root - ERROR - [DEBUG] ASSET SELECTION - No 'latest_scores' found in results
2025-06-26 17:29:23,152 - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR',
       'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR',
       'DOT/EUR'],
      dtype='object')
2025-06-26 17:29:23,152 - root - INFO - Last row columns: ['ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-06-26 17:29:23,152 - root - INFO - Single asset strategy with best asset: TRX/EUR
2025-06-26 17:29:23,152 - root - ERROR - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-06-26 17:29:23,152 - root - ERROR - [DEBUG]   - Best asset selected: TRX/EUR
2025-06-26 17:29:23,153 - root - ERROR - [DEBUG]   - Assets held: {'TRX/EUR': 1.0}
2025-06-26 17:29:23,153 - root - ERROR - [DEBUG]   - MTPI signal: 1
2025-06-26 17:29:23,153 - root - ERROR - [DEBUG]   - Use MTPI signal: False
2025-06-26 17:29:23,153 - root - ERROR - [DEBUG] ERROR - TRX WAS SELECTED - INVESTIGATING WHY!
2025-06-26 17:29:23,153 - root - INFO - Executing single-asset strategy with best asset: TRX/EUR
2025-06-26 17:29:23,153 - root - INFO - Executing strategy signal: best_asset=TRX/EUR, mtpi_signal=1, mode=paper
2025-06-26 17:29:23,153 - root - INFO - Initialized daily trades counter for 2025-06-26
2025-06-26 17:29:23,153 - root - INFO - Incremented daily trade counter for TRX/EUR: 1/5
2025-06-26 17:29:23,153 - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: TRX/EUR
2025-06-26 17:29:23,153 - root - INFO - Attempting to enter position for TRX/EUR in paper mode
2025-06-26 17:29:23,154 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Starting enter_position attempt
2025-06-26 17:29:23,154 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Trading mode: paper
2025-06-26 17:29:23,154 - root - INFO - TRADE ATTEMPT - TRX/EUR: Getting current market price...
2025-06-26 17:29:23,154 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Starting get_current_price
2025-06-26 17:29:23,154 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange ID: bitvavo
2025-06-26 17:29:23,154 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Initializing exchange bitvavo
2025-06-26 17:29:23,159 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange initialized successfully
2025-06-26 17:29:23,159 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Exchange markets not loaded, loading now...
2025-06-26 17:29:23,399 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Symbol found after loading markets
2025-06-26 17:29:23,399 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Attempting to fetch ticker...
2025-06-26 17:29:23,442 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker fetched successfully
2025-06-26 17:29:23,442 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Ticker data: {'symbol': 'TRX/EUR', 'timestamp': 1750951757575, 'datetime': '2025-06-26T15:29:17.575Z', 'high': 0.23507, 'low': 0.23076, 'bid': 0.23195, 'bidVolume': 4678.818713, 'ask': 0.23204, 'askVolume': 3454.868569, 'vwap': 0.23282229585850114, 'open': 0.23311, 'close': 0.23231, 'last': 0.23231, 'previousClose': None, 'change': -0.0008, 'percentage': -0.3431856205225001, 'average': 0.23271, 'baseVolume': 1383201.84701, 'quoteVolume': 322040.2296565875, 'info': {'market': 'TRX-EUR', 'startTimestamp': 1750865357575, 'timestamp': 1750951757575, 'open': '0.23311', 'openTimestamp': 1750865649161, 'high': '0.23507', 'low': '0.23076', 'last': '0.23231', 'closeTimestamp': 1750951218751, 'bid': '0.2319500', 'bidSize': '4678.818713', 'ask': '0.2320400', 'askSize': '3454.868569', 'volume': '1383201.84701', 'volumeQuote': '322040.22965658746'}, 'indexPrice': None, 'markPrice': None}
2025-06-26 17:29:23,443 - root - ERROR - [DEBUG] PRICE - TRX/EUR: Last price: 0.23231
2025-06-26 17:29:23,443 - root - ERROR - [DEBUG] TRADE - TRX/EUR: get_current_price returned: 0.23231
2025-06-26 17:29:23,443 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price type: <class 'float'>
2025-06-26 17:29:23,443 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - not price: False
2025-06-26 17:29:23,443 - root - ERROR - [DEBUG] TRADE - TRX/EUR: Price evaluation - price <= 0: False
2025-06-26 17:29:23,443 - root - INFO - TRADE ATTEMPT - TRX/EUR: Current price: 0.********
2025-06-26 17:29:23,443 - root - INFO - Available balance for EUR: 100.********
2025-06-26 17:29:23,446 - root - INFO - Loaded market info for 176 trading pairs
2025-06-26 17:29:23,447 - root - INFO - Calculated position size for TRX/EUR: 42.******** (using 10% of 100, accounting for fees)
2025-06-26 17:29:23,447 - root - INFO - Calculated position size: 42.******** TRX
2025-06-26 17:29:23,447 - root - INFO - Entering position for TRX/EUR: 42.******** units at 0.******** (value: 9.******** EUR)
2025-06-26 17:29:23,447 - root - INFO - Executing paper market buy order for TRX/EUR
2025-06-26 17:29:23,448 - root - INFO - Paper trading buy for TRX/EUR: original=42.********, adjusted=42.********, after_fee=42.********
2025-06-26 17:29:23,449 - root - INFO - Saved paper trading history to paper_trading_history.json
2025-06-26 17:29:23,449 - root - INFO - Created paper market buy order: TRX/EUR, amount: 42.**************, price: 0.23231
2025-06-26 17:29:23,449 - root - INFO - Filled amount: 42.******** TRX
2025-06-26 17:29:23,449 - root - INFO - Order fee: 0.******** EUR
2025-06-26 17:29:23,449 - root - INFO - Successfully entered position: TRX/EUR, amount: 42.********, price: 0.********
2025-06-26 17:29:23,457 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:29:23,457 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:29:23,457 - root - INFO - TRADE SUCCESS - TRX/EUR: Successfully updated current asset
2025-06-26 17:29:23,474 - root - INFO - Trade executed: BUY TRX/EUR, amount=42.********, price=0.********, filled=42.********
2025-06-26 17:29:23,474 - root - INFO -   Fee: 0.******** EUR
2025-06-26 17:29:23,474 - root - INFO - Single-asset trade result logged to trade log file
2025-06-26 17:29:23,474 - root - INFO - Trade executed: {'success': True, 'symbol': 'TRX/EUR', 'side': 'buy', 'amount': 42.830700357281216, 'price': 0.23231, 'order': {'id': 'paper-1750951763-TRX/EUR-buy-42.**************', 'symbol': 'TRX/EUR', 'side': 'buy', 'type': 'market', 'amount': 42.**************, 'price': 0.23231, 'cost': 9.90025, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'status': 'closed', 'filled': 42.**************, 'remaining': 0, 'timestamp': 1750951763448, 'datetime': '2025-06-26T17:29:23.448422', 'trades': [], 'average': 0.23231, 'average_price': 0.23231}, 'filled_amount': 42.**************, 'fee': {'cost': 0.********, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'TRX', 'timestamp': '2025-06-26T17:29:23.449437'}
2025-06-26 17:29:23,572 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:29:23,583 - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-06-26 17:29:23,584 - root - INFO - Asset scores (sorted by score):
2025-06-26 17:29:23,584 - root - INFO -   TRX/EUR: score=12.0, status=SELECTED, weight=1.00
2025-06-26 17:29:23,584 - root - INFO -   BNB/EUR: score=11.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,584 - root - INFO -   XRP/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   AAVE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   ETH/EUR: score=8.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   SOL/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   LINK/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   AVAX/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   ADA/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,585 - root - INFO -   DOGE/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,586 - root - INFO -   SUI/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,586 - root - INFO -   DOT/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,586 - root - INFO -   PEPE/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-06-26 17:29:23,586 - root - INFO - Asset selection logged with 13 assets scored and 1 assets selected
2025-06-26 17:29:23,586 - root - INFO - MTPI disabled - skipping MTPI score extraction
2025-06-26 17:29:23,587 - root - INFO - Extracted asset scores: {'ETH/EUR': 8.0, 'SOL/EUR': 6.0, 'SUI/EUR': 2.0, 'XRP/EUR': 9.0, 'AAVE/EUR': 9.0, 'AVAX/EUR': 3.0, 'ADA/EUR': 3.0, 'LINK/EUR': 6.0, 'TRX/EUR': 12.0, 'PEPE/EUR': 0.0, 'DOGE/EUR': 3.0, 'BNB/EUR': 11.0, 'DOT/EUR': 1.0}
2025-06-26 17:29:23,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-06-26 17:29:23,625 - root - INFO - Strategy execution completed successfully in 16.96 seconds
2025-06-26 17:29:23,627 - root - INFO - Saved recovery state to data/state\recovery_state.json
2025-06-26 17:29:26,135 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:29:36,141 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:29:46,159 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:29:56,178 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:30:06,297 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:30:16,318 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:30:26,341 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:30:36,351 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:30:46,364 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:30:56,373 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:31:06,390 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:31:16,391 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:31:26,411 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:31:36,427 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:31:46,443 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:31:56,455 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:32:06,466 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/getUpdates "HTTP/1.1 200 OK"
2025-06-26 17:32:06,957 - root - INFO - Received signal 2, shutting down...
2025-06-26 17:32:12,141 - root - INFO - Network watchdog stopped
2025-06-26 17:32:12,141 - root - INFO - Network watchdog stopped
2025-06-26 17:32:12,141 - root - INFO - Background service stopped
2025-06-26 17:32:12,214 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
